#!/bin/bash

# Start Docker daemon if not running
if ! pgrep -x "dockerd" > /dev/null; then
    echo "Starting Docker daemon..."
    sudo dockerd > /dev/null 2>&1 &
    
    # Wait for Docker to be ready
    echo "Waiting for Docker to be ready..."
    for i in {1..30}; do
        if docker info > /dev/null 2>&1; then
            echo "Docker is ready!"
            break
        fi
        sleep 1
    done
    
    if ! docker info > /dev/null 2>&1; then
        echo "Failed to start Docker daemon"
        exit 1
    fi
else
    echo "Docker daemon is already running"
fi

# Add current user to docker group if not already added
if ! groups $USER | grep -q docker; then
    echo "Adding user to docker group..."
    sudo usermod -aG docker $USER
    echo "Please log out and log back in for group changes to take effect"
fi

echo "Docker is ready to use!"
