{"info": {"_postman_id": "e004214e-8d9b-4f00-ab45-d2f695a8ce31", "name": "ZATCA Laravel API - Complete Collection", "description": "Complete Laravel ZATCA API collection with all environments and document types", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "14595927"}, "item": [{"name": "🔧 Installation Wizard", "item": [{"name": "Check Installation Status", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/installation/status", "host": ["{{base_url}}"], "path": ["api", "installation", "status"]}}, "response": []}, {"name": "Step 1: Create User", "event": [{"listen": "test", "script": {"exec": ["// 🔧 INSTALLATION STEP 1 - USER CREATION", "", "if (pm.response.code === 201) {", "    const responseData = pm.response.json();", "    ", "    if (responseData.user && responseData.user.id) {", "        pm.collectionVariables.set('user_id', responseData.user.id);", "        console.log('✅ User created successfully - ID: ' + responseData.user.id);", "    }", "    ", "    console.log('📋 Next Step: ' + responseData.next_step);", "    console.log('✅ Step 1 completed - proceed to create company');", "} else {", "    console.error('❌ User creation failed - check request data');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Super Admin\",\n    \"email\": \"<EMAIL>\",\n    \"password\": \"SecurePassword123!\",\n    \"password_confirmation\": \"SecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/installation/create-user", "host": ["{{base_url}}"], "path": ["api", "installation", "create-user"]}}, "response": []}, {"name": "Step 3: Complete Installation", "event": [{"listen": "test", "script": {"exec": ["// 🔧 INSTALLATION STEP 3 - COMPLETE INSTALLATION", "", "if (pm.response.code === 200) {", "    const responseData = pm.response.json();", "    ", "    if (responseData.installation_completed) {", "        console.log('🎉 Installation completed successfully!');", "        console.log('📋 Next Step: ' + responseData.next_step);", "        console.log('✅ You can now use all application features');", "    }", "} else {", "    console.error('❌ Installation completion failed - check requirements');", "    const responseData = pm.response.json();", "    if (responseData.next_step) {", "        console.log('📋 Required Step: ' + responseData.next_step);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/installation/complete", "host": ["{{base_url}}"], "path": ["api", "installation", "complete"]}}, "response": []}]}, {"name": "🔐 Authentication", "item": [{"name": "Get JWT Token", "event": [{"listen": "test", "script": {"exec": ["// 🔐 JWT TOKEN EXTRACTION AND STORAGE", "", "if (pm.response.code === 200) {", "    const responseData = pm.response.json();", "    ", "    if (responseData.access) {", "        pm.collectionVariables.set('access_token', responseData.access);", "        console.log('🔐 JWT Access Token stored successfully');", "    }", "    ", "    if (responseData.refresh) {", "        pm.collectionVariables.set('refresh_token', responseData.refresh);", "        console.log('🔐 JWT Refresh Token stored successfully');", "    }", "    ", "    console.log('✅ Authentication successful - tokens ready for use');", "} else {", "    console.error('❌ Authentication failed - check credentials');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"username\": \"<EMAIL>\",\n    \"password\": \"password\"\n}"}, "url": {"raw": "{{base_url}}/api/token", "host": ["{{base_url}}"], "path": ["api", "token"]}}, "response": []}]}, {"name": "🏢 Company Management", "item": [{"name": "Step 2: Create Company", "event": [{"listen": "test", "script": {"exec": ["// 🏢 INSTALLATION STEP 2 - COMPANY CREATION", "", "if (pm.response.code === 201) {", "    const responseData = pm.response.json();", "    ", "    if (responseData.id) {", "        pm.collectionVariables.set('company_id', responseData.id);", "        pm.collectionVariables.set('company_api_key', responseData.api_key);", "        console.log('✅ Company created successfully - ID: ' + responseData.id);", "        console.log('🔑 API Key stored: ' + responseData.api_key);", "    }", "    ", "    if (responseData.message) {", "        console.log('📋 Message: ' + responseData.message);", "    }", "    ", "    if (responseData.next_step) {", "        console.log('📋 Next Step: ' + responseData.next_step);", "    }", "    ", "    console.log('✅ Step 2 completed - proceed to create business location');", "} else {", "    console.error('❌ Company creation failed - check request data');", "}"], "type": "text/javascript"}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"ZatcaNext Solutions\"\n}"}, "url": {"raw": "{{base_url}}/api/company", "host": ["{{base_url}}"], "path": ["api", "company"]}}, "response": []}, {"name": "Get Company", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/company/{{company_id}}", "host": ["{{base_url}}"], "path": ["api", "company", "{{company_id}}"]}}, "response": []}, {"name": "Update Company", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated ZATCA Company\"\n}"}, "url": {"raw": "{{base_url}}/api/company/{{company_id}}", "host": ["{{base_url}}"], "path": ["api", "company", "{{company_id}}"]}}, "response": []}, {"name": "List Companies", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/companies", "host": ["{{base_url}}"], "path": ["api", "companies"]}}, "response": []}, {"name": "Delete Company", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{access_token}}", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/api/company/{{company_id}}", "host": ["{{base_url}}"], "path": ["api", "company", "{{company_id}}"]}}, "response": []}]}, {"name": "📍 Location Management", "item": [{"name": "Step 2.5: Create Business Location", "event": [{"listen": "test", "script": {"exec": ["// 📍 INSTALLATION STEP 2.5 - BUSINESS LOCATION CREATION", "", "if (pm.response.code === 201) {", "    const responseData = pm.response.json();", "    ", "    if (responseData.id) {", "        pm.collectionVariables.set('location_id', responseData.id);", "        console.log('✅ Business location created successfully - ID: ' + responseData.id);", "    }", "    ", "    if (responseData.authentication_token) {", "        pm.collectionVariables.set('location_auth_token', responseData.authentication_token);", "        console.log('🔑 Location auth token stored');", "    }", "    ", "    if (responseData.message) {", "        console.log('📋 Message: ' + responseData.message);", "    }", "    ", "    if (responseData.next_step) {", "        console.log('📋 Next Step: ' + responseData.next_step);", "    }", "    ", "    console.log('✅ Step 2.5 completed - proceed to complete installation');", "} else {", "    console.error('❌ Business location creation failed - check request data');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Api-Key {{company_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"seller_name\": \"ZatcaNext Solutions Store\",\n    \"tax_no\": \"***************\",\n    \"organisation\": \"ZatcaNext Solutions\",\n    \"serial_number\": \"1-zatcanext.com|2-version 1.0|3-ed22f1d8-e6a2-1118-9b58-d9a8f11e445f\",\n    \"organisation_unit\": \"Main Store\",\n    \"registered_address\": \"Riyadh Business District, King Fahd Road, Riyadh\",\n    \"business_category\": \"Technology Services\",\n    \"title\": \"1100\",\n    \"common_name\": \"ZatcaNext Main Store\"\n}"}, "url": {"raw": "{{base_url}}/api/locations", "host": ["{{base_url}}"], "path": ["api", "locations"]}}, "response": []}, {"name": "Get Business Location", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Api-Key {{company_api_key}}"}], "url": {"raw": "{{base_url}}/api/locations/{{location_id}}", "host": ["{{base_url}}"], "path": ["api", "locations", "{{location_id}}"]}}, "response": []}, {"name": "Update Business Location", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Api-Key {{company_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"seller_name\": \"Updated Store Name\",\n    \"tax_no\": \"***************\"\n}"}, "url": {"raw": "{{base_url}}/api/locations/{{location_id}}", "host": ["{{base_url}}"], "path": ["api", "locations", "{{location_id}}"]}}, "response": []}, {"name": "List Business Locations", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Api-Key {{company_api_key}}"}], "url": {"raw": "{{base_url}}/api/locations", "host": ["{{base_url}}"], "path": ["api", "locations"]}}, "response": []}, {"name": "Delete Business Location", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Api-Key {{company_api_key}}"}], "url": {"raw": "{{base_url}}/api/locations/{{location_id}}", "host": ["{{base_url}}"], "path": ["api", "locations", "{{location_id}}"]}}, "response": []}]}, {"name": "🔒 ZATCA Compliance API - Sandbox", "item": [{"name": "Complete Compliance Check - Sandbox", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"otp\": \"123345\"\n}"}, "url": {"raw": "{{base_url}}/api/{{location_id}}/sandbox/compliance-check", "host": ["{{base_url}}"], "path": ["api", "{{location_id}}", "sandbox", "compliance-check"]}}, "response": []}]}, {"name": "🔒 ZATCA Compliance API - Simulation", "item": [{"name": "Complete Compliance Check - Simulation", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"otp\": \"REAL_OTP_FROM_FATOORA\"\n}"}, "url": {"raw": "{{base_url}}/api/{{location_id}}/simulation/compliance-check", "host": ["{{base_url}}"], "path": ["api", "{{location_id}}", "simulation", "compliance-check"]}}, "response": []}]}, {"name": "🔒 ZATCA Compliance API - Production", "item": [{"name": "Complete Compliance Check - Production", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"otp\": \"REAL_OTP_FROM_FATOORA\"\n}"}, "url": {"raw": "{{base_url}}/api/{{location_id}}/production/compliance-check", "host": ["{{base_url}}"], "path": ["api", "{{location_id}}", "production", "compliance-check"]}}, "response": []}]}, {"name": "🔄 ZATCA Clearance API - Sandbox", "item": [{"name": "Sandbox Clearance - Invoice", "event": [{"listen": "prerequest", "script": {"exec": ["// 🚀 AUTOMATED CLEARANCE API - STANDARD INVOICE", "// This script automatically generates dynamic values and sets authentication", "", "console.log('🔄 Preparing Clearance API request for Standard Invoice...');", "", "// Generate specific values for clearance", "const clearanceId = 'CLR-STD-' + Math.floor(Math.random() * 90000 + 10000);", "const uuid = pm.variables.replaceIn('{{$guid}}');", "const icv = Math.floor(Math.random() * 9000 + 1000);", "", "// Override dynamic variables for this specific request", "pm.collectionVariables.set('dynamic_invoice_id', clearanceId);", "pm.collectionVariables.set('dynamic_uuid', uuid);", "pm.collectionVariables.set('dynamic_icv', icv);", "", "console.log('📋 Generated Clearance Invoice ID: ' + clearanceId);", "console.log('📋 Generated UUID: ' + uuid);", "console.log('📋 Generated ICV: ' + icv);", "console.log('🔐 Authentication will be set by global pre-request script');"], "type": "text/javascript", "packages": {}}}, {"listen": "test", "script": {"exec": ["// 🧪 AUTOMATED TESTS FOR CLEARANCE API", "", "pm.test('Clearance API - Standard Invoice should succeed', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Clearance API - Should return success status', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.equal('200');", "    pm.expect(jsonData.Message).to.equal('Success');", "});", "", "pm.test('Clearance API - Should generate clearance data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('invoiceHash');", "    pm.expect(jsonData).to.have.property('qrcode');", "    ", "    if (jsonData.data && jsonData.data.clearanceStatus) {", "        pm.expect(jsonData.data.clearanceStatus).to.equal('CLEARED');", "        console.log('🔄 Clearance Status: ' + jsonData.data.clearanceStatus);", "    }", "});", "", "// Store clearance results for potential use in other requests", "if (pm.response.code === 200) {", "    const responseData = pm.response.json();", "    pm.collectionVariables.set('last_clearance_hash', responseData.invoiceHash);", "    pm.collectionVariables.set('last_clearance_qr', responseData.qrcode);", "    console.log('✅ Clearance completed successfully!');", "    console.log('📋 Invoice Hash stored for future use');", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"standard\",\n        \"documentType\": \"invoice\"\n    },\n    \"profileID\": \"clearance:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"28762\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"accountingCustomerParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Customer Street\",\n        \"buildingNumber\": \"9318\",\n        \"plotIdentification\": \"12\",\n        \"citySubdivisionName\": \"Customer District\",\n        \"cityName\": \"Customer City\",\n        \"postalZone\": \"13714\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Test Customer\",\n        \"countryCode\": \"SA\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/sandbox/clearance", "host": ["{{base_url}}"], "path": ["api", "sandbox", "clearance"]}}, "response": []}, {"name": "Sandbox Clearance - Credit Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"standard\",\n        \"documentType\": \"credit_note\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"accountingCustomerParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Customer Street\",\n        \"buildingNumber\": \"9318\",\n        \"plotIdentification\": \"12\",\n        \"citySubdivisionName\": \"Customer District\",\n        \"cityName\": \"Customer City\",\n        \"postalZone\": \"13714\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Test Customer\",\n        \"countryCode\": \"SA\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ],\n    \"billingReference\": {\n        \"invoiceDocumentReference\": {\n            \"id\": \"INV-{{dynamic_invoice_id}}\",\n            \"issueDate\": \"2025-06-03\"\n        }\n    },\n    \"creditDebitReason\": \"Product return\"\n}"}, "url": {"raw": "{{base_url}}/api/sandbox/clearance", "host": ["{{base_url}}"], "path": ["api", "sandbox", "clearance"]}}, "response": []}, {"name": "Sandbox Clearance - Debit Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"standard\",\n        \"documentType\": \"debit_note\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"accountingCustomerParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Customer Street\",\n        \"buildingNumber\": \"9318\",\n        \"plotIdentification\": \"12\",\n        \"citySubdivisionName\": \"Customer District\",\n        \"cityName\": \"Customer City\",\n        \"postalZone\": \"13714\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Test Customer\",\n        \"countryCode\": \"SA\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ],\n    \"billingReference\": {\n        \"invoiceDocumentReference\": {\n            \"id\": \"INV-{{dynamic_invoice_id}}\",\n            \"issueDate\": \"2025-06-03\"\n        }\n    },\n    \"creditDebitReason\": \"Additional charges\"\n}"}, "url": {"raw": "{{base_url}}/api/sandbox/clearance", "host": ["{{base_url}}"], "path": ["api", "sandbox", "clearance"]}}, "response": []}]}, {"name": "🔄 ZATCA Clearance API - Simulation", "item": [{"name": "Simulation Clearance - Invoice", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"standard\",\n        \"documentType\": \"invoice\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"accountingCustomerParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Customer Street\",\n        \"buildingNumber\": \"9318\",\n        \"plotIdentification\": \"12\",\n        \"citySubdivisionName\": \"Customer District\",\n        \"cityName\": \"Customer City\",\n        \"postalZone\": \"13714\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Test Customer\",\n        \"countryCode\": \"SA\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/simulation/clearance", "host": ["{{base_url}}"], "path": ["api", "simulation", "clearance"]}}, "response": []}, {"name": "Simulation Clearance - Credit Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"standard\",\n        \"documentType\": \"credit_note\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"accountingCustomerParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Customer Street\",\n        \"buildingNumber\": \"9318\",\n        \"plotIdentification\": \"12\",\n        \"citySubdivisionName\": \"Customer District\",\n        \"cityName\": \"Customer City\",\n        \"postalZone\": \"13714\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Test Customer\",\n        \"countryCode\": \"SA\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ],\n    \"billingReference\": {\n        \"invoiceDocumentReference\": {\n            \"id\": \"INV-{{dynamic_invoice_id}}\",\n            \"issueDate\": \"2025-06-03\"\n        }\n    },\n    \"creditDebitReason\": \"Product return\"\n}"}, "url": {"raw": "{{base_url}}/api/simulation/clearance", "host": ["{{base_url}}"], "path": ["api", "simulation", "clearance"]}}, "response": []}, {"name": "Simulation Clearance - Debit Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"standard\",\n        \"documentType\": \"debit_note\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"accountingCustomerParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Customer Street\",\n        \"buildingNumber\": \"9318\",\n        \"plotIdentification\": \"12\",\n        \"citySubdivisionName\": \"Customer District\",\n        \"cityName\": \"Customer City\",\n        \"postalZone\": \"13714\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Test Customer\",\n        \"countryCode\": \"SA\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ],\n    \"billingReference\": {\n        \"invoiceDocumentReference\": {\n            \"id\": \"INV-{{dynamic_invoice_id}}\",\n            \"issueDate\": \"2025-06-03\"\n        }\n    },\n    \"creditDebitReason\": \"Additional charges\"\n}"}, "url": {"raw": "{{base_url}}/api/simulation/clearance", "host": ["{{base_url}}"], "path": ["api", "simulation", "clearance"]}}, "response": []}]}, {"name": "🔄 ZATCA Clearance API - Production", "item": [{"name": "Production Clearance - Invoice", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"standard\",\n        \"documentType\": \"invoice\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"accountingCustomerParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Customer Street\",\n        \"buildingNumber\": \"9318\",\n        \"plotIdentification\": \"12\",\n        \"citySubdivisionName\": \"Customer District\",\n        \"cityName\": \"Customer City\",\n        \"postalZone\": \"13714\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Test Customer\",\n        \"countryCode\": \"SA\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/production/clearance", "host": ["{{base_url}}"], "path": ["api", "production", "clearance"]}}, "response": []}, {"name": "Production Clearance - Credit Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"standard\",\n        \"documentType\": \"credit_note\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"accountingCustomerParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Customer Street\",\n        \"buildingNumber\": \"9318\",\n        \"plotIdentification\": \"12\",\n        \"citySubdivisionName\": \"Customer District\",\n        \"cityName\": \"Customer City\",\n        \"postalZone\": \"13714\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Test Customer\",\n        \"countryCode\": \"SA\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ],\n    \"billingReference\": {\n        \"invoiceDocumentReference\": {\n            \"id\": \"INV-{{dynamic_invoice_id}}\",\n            \"issueDate\": \"2025-06-03\"\n        }\n    },\n    \"creditDebitReason\": \"Product return\"\n}"}, "url": {"raw": "{{base_url}}/api/production/clearance", "host": ["{{base_url}}"], "path": ["api", "production", "clearance"]}}, "response": []}, {"name": "Production Clearance - Debit Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"standard\",\n        \"documentType\": \"debit_note\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"accountingCustomerParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Customer Street\",\n        \"buildingNumber\": \"9318\",\n        \"plotIdentification\": \"12\",\n        \"citySubdivisionName\": \"Customer District\",\n        \"cityName\": \"Customer City\",\n        \"postalZone\": \"13714\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Test Customer\",\n        \"countryCode\": \"SA\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ],\n    \"billingReference\": {\n        \"invoiceDocumentReference\": {\n            \"id\": \"INV-{{dynamic_invoice_id}}\",\n            \"issueDate\": \"2025-06-03\"\n        }\n    },\n    \"creditDebitReason\": \"Additional charges\"\n}"}, "url": {"raw": "{{base_url}}/api/production/clearance", "host": ["{{base_url}}"], "path": ["api", "production", "clearance"]}}, "response": []}]}, {"name": "📊 ZATCA Reporting API - Sandbox", "item": [{"name": "Sandbox Reporting - Invoice", "event": [{"listen": "prerequest", "script": {"exec": ["// 📊 AUTOMATED REPORTING API - SIMPLIFIED INVOICE", "// This script automatically generates dynamic values for reporting", "", "console.log('📊 Preparing Reporting API request for Simplified Invoice...');", "", "// Generate specific values for reporting", "const reportingId = 'RPT-SME-' + Math.floor(Math.random() * 90000 + 10000);", "const uuid = pm.variables.replaceIn('{{$guid}}');", "const icv = Math.floor(Math.random() * 9000 + 1000);", "", "// Override dynamic variables for this specific request", "pm.collectionVariables.set('dynamic_invoice_id', reportingId);", "pm.collectionVariables.set('dynamic_uuid', uuid);", "pm.collectionVariables.set('dynamic_icv', icv);", "", "console.log('📋 Generated Reporting Invoice ID: ' + reportingId);", "console.log('📋 Generated UUID: ' + uuid);", "console.log('📋 Generated ICV: ' + icv);", "console.log('🔐 Authentication will be set by global pre-request script');"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 🧪 AUTOMATED TESTS FOR REPORTING API", "", "pm.test('Reporting API - Simplified Invoice should succeed', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Reporting API - Should return success status', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.equal('200');", "    pm.expect(jsonData.Message).to.equal('Success');", "});", "", "pm.test('Reporting API - Should generate reporting data', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('invoiceHash');", "    pm.expect(jsonData).to.have.property('qrcode');", "    ", "    if (jsonData.data && jsonData.data.reportingStatus) {", "        pm.expect(jsonData.data.reportingStatus).to.equal('REPORTED');", "        console.log('📊 Reporting Status: ' + jsonData.data.reportingStatus);", "    }", "});", "", "// Store reporting results for potential use in other requests", "if (pm.response.code === 200) {", "    const responseData = pm.response.json();", "    pm.collectionVariables.set('last_reporting_hash', responseData.invoiceHash);", "    pm.collectionVariables.set('last_reporting_qr', responseData.qrcode);", "    console.log('✅ Reporting completed successfully!');", "    console.log('📋 Invoice Hash stored for future use');", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"simplified\",\n        \"documentType\": \"invoice\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"accountingCustomerParty\": {\n        \"streetName\": \"\",\n        \"buildingNumber\": \"\",\n        \"citySubdivisionName\": \"\",\n        \"cityName\": \"\",\n        \"postalZone\": \"\",\n        \"registrationName\": \"\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/sandbox/reporting", "host": ["{{base_url}}"], "path": ["api", "sandbox", "reporting"]}}, "response": []}, {"name": "Sandbox Reporting - Credit Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"simplified\",\n        \"documentType\": \"credit_note\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ],\n    \"billingReference\": {\n        \"invoiceDocumentReference\": {\n            \"id\": \"INV-{{dynamic_invoice_id}}\",\n            \"issueDate\": \"2025-06-03\"\n        }\n    },\n    \"creditDebitReason\": \"Product return\"\n}"}, "url": {"raw": "{{base_url}}/api/sandbox/reporting", "host": ["{{base_url}}"], "path": ["api", "sandbox", "reporting"]}}, "response": []}, {"name": "Sandbox Reporting - Debit Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"simplified\",\n        \"documentType\": \"debit_note\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ],\n    \"billingReference\": {\n        \"invoiceDocumentReference\": {\n            \"id\": \"INV-{{dynamic_invoice_id}}\",\n            \"issueDate\": \"2025-06-03\"\n        }\n    },\n    \"creditDebitReason\": \"Additional charges\"\n}"}, "url": {"raw": "{{base_url}}/api/sandbox/reporting", "host": ["{{base_url}}"], "path": ["api", "sandbox", "reporting"]}}, "response": []}]}, {"name": "📊 ZATCA Reporting API - Simulation", "item": [{"name": "Simulation Reporting - Invoice", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"simplified\",\n        \"documentType\": \"invoice\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/simulation/reporting", "host": ["{{base_url}}"], "path": ["api", "simulation", "reporting"]}}, "response": []}, {"name": "Simulation Reporting - Credit Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"simplified\",\n        \"documentType\": \"credit_note\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ],\n    \"billingReference\": {\n        \"invoiceDocumentReference\": {\n            \"id\": \"INV-{{dynamic_invoice_id}}\",\n            \"issueDate\": \"2025-06-03\"\n        }\n    },\n    \"creditDebitReason\": \"Product return\"\n}"}, "url": {"raw": "{{base_url}}/api/simulation/reporting", "host": ["{{base_url}}"], "path": ["api", "simulation", "reporting"]}}, "response": []}, {"name": "Simulation Reporting - Debit Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"simplified\",\n        \"documentType\": \"debit_note\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ],\n    \"billingReference\": {\n        \"invoiceDocumentReference\": {\n            \"id\": \"INV-{{dynamic_invoice_id}}\",\n            \"issueDate\": \"2025-06-03\"\n        }\n    },\n    \"creditDebitReason\": \"Additional charges\"\n}"}, "url": {"raw": "{{base_url}}/api/simulation/reporting", "host": ["{{base_url}}"], "path": ["api", "simulation", "reporting"]}}, "response": []}]}, {"name": "📊 ZATCA Reporting API - Production", "item": [{"name": "Production Reporting - Invoice", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"simplified\",\n        \"documentType\": \"invoice\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/production/reporting", "host": ["{{base_url}}"], "path": ["api", "production", "reporting"]}}, "response": []}, {"name": "Production Reporting - Credit Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"simplified\",\n        \"documentType\": \"credit_note\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ],\n    \"billingReference\": {\n        \"invoiceDocumentReference\": {\n            \"id\": \"INV-{{dynamic_invoice_id}}\",\n            \"issueDate\": \"2025-06-03\"\n        }\n    },\n    \"creditDebitReason\": \"Product return\"\n}"}, "url": {"raw": "{{base_url}}/api/production/reporting", "host": ["{{base_url}}"], "path": ["api", "production", "reporting"]}}, "response": []}, {"name": "Production Reporting - Debit Note", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"simplified\",\n        \"documentType\": \"debit_note\"\n    },\n    \"profileID\": \"reporting:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ],\n    \"billingReference\": {\n        \"invoiceDocumentReference\": {\n            \"id\": \"INV-{{dynamic_invoice_id}}\",\n            \"issueDate\": \"2025-06-03\"\n        }\n    },\n    \"creditDebitReason\": \"Additional charges\"\n}"}, "url": {"raw": "{{base_url}}/api/production/reporting", "host": ["{{base_url}}"], "path": ["api", "production", "reporting"]}}, "response": []}]}, {"name": "🛠️ Utility APIs", "item": [{"name": "Generate XML from Payload", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Api-Key {{company_api_key}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"invoice\": {\n        \"invoiceType\": \"standard\",\n        \"documentType\": \"invoice\"\n    },\n    \"profileID\": \"clearance:1.0\",\n    \"id\": \"{{dynamic_invoice_id}}\",\n    \"uuid\": \"{{dynamic_uuid}}\",\n    \"issueDate\": \"{{current_date}}\",\n    \"issueTime\": \"{{current_time}}\",\n    \"documentCurrencyCode\": \"SAR\",\n    \"taxCurrencyCode\": \"SAR\",\n    \"additionalDocumentReference\": {\n        \"id\": \"ICV\",\n        \"uuid\": \"{{dynamic_icv}}\",\n        \"pih\": \"y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M=\"\n    },\n    \"accountingSupplierParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Mihar Al-Daylami\",\n        \"buildingNumber\": \"2862\",\n        \"plotIdentification\": \"7864\",\n        \"citySubdivisionName\": \"Al salamah\",\n        \"cityName\": \"Makkah\",\n        \"postalZone\": \"24226\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Safa cold store for foodstuff Co.\"\n    },\n    \"accountingCustomerParty\": {\n        \"id\": \"**********\",\n        \"schema\": \"CRN\",\n        \"streetName\": \"Customer Street\",\n        \"buildingNumber\": \"9318\",\n        \"plotIdentification\": \"12\",\n        \"citySubdivisionName\": \"Customer District\",\n        \"cityName\": \"Customer City\",\n        \"postalZone\": \"13714\",\n        \"companyID\": \"***************\",\n        \"taxID\": \"VAT\",\n        \"registrationName\": \"Test Customer\",\n        \"countryCode\": \"SA\"\n    },\n    \"paymentMeansCode\": \"10\",\n    \"actualDeliveryDate\": \"{{current_date}}\",\n    \"latestDeliveryDate\": \"{{current_date}}\",\n    \"allowanceCharge\": {\n        \"chargeIndicator\": \"false\",\n        \"allowanceChargeReason\": \"discount\",\n        \"amount\": \"0\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"taxAmount\": \"1.50\",\n    \"taxTotal\": {\n        \"taxAmount\": \"1.50\",\n        \"tsttaxableAmount\": \"10.00\",\n        \"tsttaxAmount\": \"1.50\",\n        \"taxId\": \"S\",\n        \"taxPercentage\": \"15.00\",\n        \"taxScheme\": \"VAT\"\n    },\n    \"legalMonetaryTotal\": {\n        \"lineExtensionAmount\": \"10.00\",\n        \"taxExclusiveAmount\": \"10.00\",\n        \"taxInclusiveAmount\": \"11.50\",\n        \"allowanceTotalAmount\": \"0.00\",\n        \"prepaidAmount\": \"0.00\",\n        \"payableAmount\": \"11.50\"\n    },\n    \"invoiceLines\": [\n        {\n            \"id\": \"1\",\n            \"invoicedQuantity\": \"1.0000\",\n            \"lineExtensionAmount\": \"10.00\",\n            \"taxAmount\": \"1.50\",\n            \"roundingAmount\": \"11.50\",\n            \"itemName\": \"Test Product\",\n            \"taxId\": \"S\",\n            \"taxPercentage\": \"15.00\",\n            \"taxScheme\": \"VAT\",\n            \"priceAmount\": \"10.00\",\n            \"allowanceChargeReason\": \"discount\",\n            \"allowanceChargeAmount\": \"0.00\"\n        }\n    ]\n}"}, "url": {"raw": "{{base_url}}/api/generate-xml", "host": ["{{base_url}}"], "path": ["api", "generate-xml"]}}, "response": []}, {"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/health", "host": ["{{base_url}}"], "path": ["api", "health"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"exec": ["// 🔍 DYNAMIC ZATCA AUTOMATION - USES ACTUAL API RESPONSES", "console.log('🔍 GLOBAL SCRIPT EXECUTING...');", "console.log('🔗 Request URL: ' + pm.request.url.toString());", "", "const requestUrl = pm.request.url.toString();", "", "// Check if this is a ZATCA API request", "if (requestUrl.includes('compliance-check') || requestUrl.includes('/clearance') || requestUrl.includes('/reporting')) {", "    console.log('✅ ZATCA API DETECTED!');", "    ", "    // Get ACTUAL credentials from API responses (not hardcoded)", "    const companyApiKey = pm.collectionVariables.get('actual_company_api_key') || pm.collectionVariables.get('company_api_key');", "    const locationSecret = pm.collectionVariables.get('actual_location_secret') || pm.collectionVariables.get('location_secret');", "    ", "    console.log('🔍 Using credentials:');", "    console.log('   Company API Key: ' + (companyApiKey ? companyApiKey.substring(0, 10) + '...' : 'NOT SET'));", "    console.log('   Location Secret: ' + (locationSecret ? locationSecret.substring(0, 10) + '...' : 'NOT SET'));", "    ", "    if (companyApiKey && locationSecret) {", "        console.log('🔑 Setting headers...');", "        ", "        // Clear and set headers", "        pm.request.headers.clear();", "        pm.request.headers.add({key: 'Content-Type', value: 'application/json'});", "        pm.request.headers.add({key: 'secret', value: locationSecret});", "        pm.request.headers.add({key: 'Authorization', value: 'Api-Key ' + companyApiKey});", "        ", "        console.log('✅ Headers set successfully!');", "        console.log('📋 Content-Type: application/json');", "        console.log('📋 secret: ' + locationSecret.substring(0, 15) + '...');", "        console.log('📋 Authorization: Api-Key ' + companyApiKey.substring(0, 15) + '...');", "    } else {", "        console.error('❌ Missing ZATCA credentials!');", "        console.error('   Company API Key: ' + (companyApiKey ? 'SET' : 'MISSING'));", "        console.error('   Location Secret: ' + (locationSecret ? 'SET' : 'MISSING'));", "        console.error('💡 Make sure to run Create Company and Create Location first');", "    }", "} else {", "    console.log('ℹ️ Non-ZATCA request - no special headers needed');", "}", "", "// Set dynamic variables for invoice generation", "const currentDate = new Date().toISOString().split('T')[0];", "const currentTime = new Date().toISOString().split('T')[1].split('.')[0];", "pm.collectionVariables.set('current_date', currentDate);", "pm.collectionVariables.set('current_time', currentTime);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// 🧪 ENHANCED GL<PERSON>BAL TEST SCRIPT - COMPLETE ZATCA API AUTOMATION", "// This script provides comprehensive testing and response handling", "", "const requestName = pm.info.requestName || 'Unknown Request';", "const requestUrl = pm.request.url.toString();", "const responseTime = pm.response.responseTime;", "const statusCode = pm.response.code;", "", "console.log('📥 RESPONSE ANALYSIS FOR: ' + requestName);", "console.log('📊 Status: ' + statusCode + ' ' + pm.response.status);", "console.log('⏱️ Response Time: ' + responseTime + 'ms');", "console.log('📏 Response Size: ' + pm.response.responseSize + ' bytes');", "", "// Enhanced error handling with detailed diagnostics", "if (statusCode === 401) {", "    console.error('❌ AUTHENTICATION FAILED');", "    console.error('💡 Troubleshooting Steps:');", "    console.error('   1. Check company_api_key variable');", "    console.error('   2. Check location_secret variable');", "    console.error('   3. Verify API key is active');", "    console.error('   4. Check location authentication token');", "} else if (statusCode === 404) {", "    console.error('❌ ENDPOINT NOT FOUND');", "    console.error('💡 Troubleshooting Steps:');", "    console.error('   1. Verify base_url: ' + (pm.collectionVariables.get('base_url') || 'NOT SET'));", "    console.error('   2. Check endpoint path in URL');", "    console.error('   3. Verify location_id: ' + (pm.collectionVariables.get('location_id') || 'NOT SET'));", "    console.error('   4. Ensure Docker container is running');", "} else if (statusCode === 422) {", "    console.error('❌ VALIDATION ERROR');", "    try {", "        const errorData = pm.response.json();", "        console.error('💡 Validation Details:', errorData);", "    } catch (e) {", "        console.error('💡 Response:', pm.response.text());", "    }", "} else if (statusCode === 500) {", "    console.error('❌ SERVER ERROR');", "    console.error('💡 Troubleshooting Steps:');", "    console.error('   1. Check server logs');", "    console.error('   2. Verify payload format');", "    console.error('   3. Check ZATCA service status');", "    try {", "        const errorData = pm.response.json();", "        console.error('💡 Error Details:', errorData);", "    } catch (e) {", "        console.error('💡 Raw Response:', pm.response.text());", "    }", "} else if (statusCode >= 200 && statusCode < 300) {", "    console.log('✅ REQUEST SUCCESSFUL');", "    ", "    // Enhanced response data extraction and storage", "    try {", "        const responseData = pm.response.json();", "        ", "        // Extract and store authentication tokens", "        if (responseData.access) {", "            pm.collectionVariables.set('access_token', responseData.access);", "            console.log('🔐 JWT Access Token stored');", "        }", "        if (responseData.refresh) {", "            pm.collectionVariables.set('refresh_token', responseData.refresh);", "            console.log('🔐 JWT Refresh Token stored');", "        }", "        ", "        // Extract and store company data", "        if (responseData.id && requestUrl.includes('/company')) {", "            pm.collectionVariables.set('company_id', responseData.id.toString());", "            console.log('🏢 Company ID stored: ' + responseData.id);", "        }", "        ", "        // Extract and store company API key", "        if (responseData.api_key && requestUrl.includes('/company')) {", "            pm.collectionVariables.set('actual_company_api_key', responseData.api_key);", "            console.log('🔑 Company API Key stored: ' + responseData.api_key.substring(0, 10) + '...');", "        }", "        ", "        // Extract and store location data", "        if (responseData.id && requestUrl.includes('/location')) {", "            pm.collectionVariables.set('location_id', responseData.id.toString());", "            console.log('📍 Location ID stored: ' + responseData.id);", "        }", "        ", "        // Extract and store location authentication token", "        if (responseData.authentication_token && requestUrl.includes('/location')) {", "            pm.collectionVariables.set('actual_location_secret', responseData.authentication_token);", "            console.log('🔐 Location Secret stored: ' + responseData.authentication_token.substring(0, 15) + '...');", "        }", "        ", "        // Enhanced ZATCA response processing", "        if (responseData.invoiceHash) {", "            const hashPreview = responseData.invoiceHash.substring(0, 25) + '...';", "            console.log('📋 Invoice Hash: ' + hashPreview);", "            pm.collectionVariables.set('last_invoice_hash', responseData.invoiceHash);", "            pm.collectionVariables.set('last_hash_' + (pm.collectionVariables.get('api_type') || 'unknown'), responseData.invoiceHash);", "        }", "        ", "        if (responseData.qrcode) {", "            const qrPreview = responseData.qrcode.substring(0, 40) + '...';", "            console.log('📋 QR Code: ' + qrPreview);", "            pm.collectionVariables.set('last_qr_code', responseData.qrcode);", "            pm.collectionVariables.set('last_qr_' + (pm.collectionVariables.get('api_type') || 'unknown'), responseData.qrcode);", "            ", "            // Validate QR code format (should contain == and + signs)", "            if (responseData.qrcode.includes('==') && responseData.qrcode.includes('+')) {", "                console.log('✅ QR Code format validated (contains == and + signs)');", "            } else {", "                console.warn('⚠️ QR Code may not have expected format');", "            }", "        }", "        ", "        // Enhanced ZATCA status processing", "        if (responseData.data) {", "            if (responseData.data.validationResults) {", "                const status = responseData.data.validationResults.status || 'N/A';", "                console.log('📋 Validation Status: ' + status);", "                pm.collectionVariables.set('last_validation_status', status);", "            }", "            if (responseData.data.reportingStatus) {", "                console.log('📊 Reporting Status: ' + responseData.data.reportingStatus);", "                pm.collectionVariables.set('last_reporting_status', responseData.data.reportingStatus);", "            }", "            if (responseData.data.clearanceStatus) {", "                console.log('🔄 Clearance Status: ' + responseData.data.clearanceStatus);", "                pm.collectionVariables.set('last_clearance_status', responseData.data.clearanceStatus);", "            }", "        }", "        ", "        // Enhanced compliance check results", "        if (responseData.compliance_results) {", "            console.log('🔒 Compliance Results Available');", "            const results = responseData.compliance_results;", "            Object.keys(results).forEach(key => {", "                const result = results[key];", "                if (result.status === '200') {", "                    console.log('   ✅ ' + key + ': PASSED');", "                } else {", "                    console.log('   ❌ ' + key + ': FAILED');", "                }", "            });", "        }", "        ", "        // Success indicators", "        if (responseData.status === '200' && responseData.Message === 'Success') {", "            console.log('🎉 ZATCA API OPERATION COMPLETED SUCCESSFULLY!');", "        }", "        if (responseData.message === 'Compliance check completed') {", "            console.log('🎉 UNIFIED COMPLIANCE CHECK COMPLETED!');", "            if (responseData.test_statistics) {", "                console.log('📊 Success Rate: ' + responseData.test_statistics.success_rate);", "            }", "        }", "        ", "    } catch (e) {", "        // Handle non-JSON responses", "        console.log('📋 Response processed (non-JSON format)');", "    }", "} else {", "    console.error('❌ REQUEST FAILED WITH STATUS: ' + statusCode);", "}", "", "// ENHANCED AUTOMATED TESTS", "console.log('🧪 Running Enhanced Automated Tests...');", "", "// Basic response tests", "pm.test('Response - Status code should be successful', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201, 202]);", "});", "", "pm.test('Response - Should complete within reasonable time', function () {", "    pm.expect(pm.response.responseTime).to.be.below(30000); // 30 seconds", "});", "", "// Authentication API specific tests", "if (requestUrl.includes('/token') && !requestUrl.includes('/refresh') && !requestUrl.includes('/logout')) {", "    pm.test('Auth - Should return access and refresh tokens', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('access');", "        pm.expect(jsonData).to.have.property('refresh');", "        pm.expect(jsonData.access).to.not.be.empty;", "        pm.expect(jsonData.refresh).to.not.be.empty;", "    });", "}", "", "// Company API specific tests", "if (requestUrl.includes('/company') && pm.response.code === 201) {", "    pm.test('Company - Should return company data with ID', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('name');", "        pm.expect(jsonData.id).to.be.a('number');", "    });", "}", "", "// Location API specific tests", "if (requestUrl.includes('/location') && pm.response.code === 201) {", "    pm.test('Location - Should return location data with ID', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('id');", "        pm.expect(jsonData).to.have.property('seller_name');", "        pm.expect(jsonData.id).to.be.a('number');", "    });", "}", "", "// Enhanced ZATCA API specific tests", "if (requestUrl.includes('/clearance') || requestUrl.includes('/reporting') || requestUrl.includes('/compliance') || requestUrl.includes('/compliance-check') || requestUrl.includes('/sandbox/compliance-check') || requestUrl.includes('/simulation/compliance-check') || requestUrl.includes('/production/compliance-check')) {", "    pm.test('ZATCA API - Status code should be 200', function () {", "        pm.response.to.have.status(200);", "    });", "    ", "    pm.test('ZATCA API - Response should be JSON', function () {", "        pm.response.to.be.json;", "    });", "    ", "    pm.test('ZATCA API - Response should have required fields', function () {", "        const jsonData = pm.response.json();", "        pm.expect(jsonData).to.have.property('status');", "        // Compliance-check has different response format", "        if (!requestUrl.includes('/compliance-check') && !requestUrl.includes('/sandbox/compliance-check') && !requestUrl.includes('/simulation/compliance-check') && !requestUrl.includes('/production/compliance-check')) {", "            pm.expect(jsonData).to.have.property('Message');", "        }", "    });", "    ", "    pm.test('ZATCA API - Operation should be successful', function () {", "        const jsonData = pm.response.json();", "        if (requestUrl.includes('/compliance-check') || requestUrl.includes('/sandbox/compliance-check') || requestUrl.includes('/simulation/compliance-check') || requestUrl.includes('/production/compliance-check')) {", "            pm.expect(jsonData.status).to.equal('200');", "            pm.expect(jsonData.message).to.equal('Compliance check completed');", "        } else {", "            pm.expect(jsonData.status).to.equal('200');", "            pm.expect(jsonData.Message).to.equal('Success');", "        }", "    });", "    ", "    // Enhanced tests for clearance and reporting APIs", "    if (requestUrl.includes('/clearance') || requestUrl.includes('/reporting')) {", "        pm.test('ZATCA API - Should generate invoice hash', function () {", "            const jsonData = pm.response.json();", "            pm.expect(jsonData).to.have.property('invoiceHash');", "            pm.expect(jsonData.invoiceHash).to.not.be.empty;", "            pm.expect(jsonData.invoiceHash).to.be.a('string');", "        });", "        ", "        pm.test('ZATCA API - Should generate QR code', function () {", "            const jsonData = pm.response.json();", "            pm.expect(jsonData).to.have.property('qrcode');", "            pm.expect(jsonData.qrcode).to.not.be.empty;", "            pm.expect(jsonData.qrcode).to.be.a('string');", "        });", "        ", "        pm.test('ZATCA API - QR code should have correct format', function () {", "            const jsonData = pm.response.json();", "            pm.expect(jsonData.qrcode).to.include('==');", "            pm.expect(jsonData.qrcode).to.include('+');", "        });", "    }", "    ", "    // Enhanced tests for compliance API", "    if (requestUrl.includes('/compliance-check')) {", "        pm.test('Compliance API - Should return compliance results', function () {", "            const jsonData = pm.response.json();", "            pm.expect(jsonData).to.have.property('message');", "            pm.expect(jsonData.message).to.equal('Compliance check completed');", "        });", "        ", "        pm.test('Compliance API - Should test multiple document types', function () {", "            const jsonData = pm.response.json();", "            pm.expect(jsonData).to.have.property('compliance_results');", "            pm.expect(jsonData.compliance_results).to.be.an('object');", "        });", "        ", "        pm.test('Compliance API - Should provide test statistics', function () {", "            const jsonData = pm.response.json();", "            pm.expect(jsonData).to.have.property('test_statistics');", "            pm.expect(jsonData.test_statistics).to.have.property('success_rate');", "        });", "    }", "}", "", "console.log('✅ Enhanced Automated Tests Completed');"], "type": "text/javascript"}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "current_date", "value": "2025-06-04", "type": "string"}, {"key": "current_time", "value": "15:30:00", "type": "string"}, {"key": "company_api_key", "value": "g3Zx1t4mtnax9Xl18VF8h6P06nsmyeLz", "type": "string"}, {"key": "location_secret", "value": "2qrzJvUz9Cpn5C83XhqJUhKfqbILyDQ4tiPwqAQiIhOa99muNaSjyQ2Zw995UwyC", "type": "string"}, {"key": "location_id", "value": "123", "type": "string"}, {"key": "company_id", "value": "1", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "dynamic_invoice_id", "value": "", "type": "string"}, {"key": "dynamic_uuid", "value": "", "type": "string"}, {"key": "dynamic_icv", "value": "", "type": "string"}, {"key": "last_invoice_hash", "value": "", "type": "string"}, {"key": "last_qr_code", "value": "", "type": "string"}, {"key": "original_invoice_id", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "dynamic_reference", "value": "", "type": "string"}, {"key": "api_type", "value": "", "type": "string"}, {"key": "current_timestamp", "value": "", "type": "string"}, {"key": "last_validation_status", "value": "", "type": "string"}, {"key": "last_reporting_status", "value": "", "type": "string"}, {"key": "last_clearance_status", "value": "", "type": "string"}, {"key": "last_hash_clearance", "value": "", "type": "string"}, {"key": "last_hash_reporting", "value": "", "type": "string"}, {"key": "last_hash_compliance", "value": "", "type": "string"}, {"key": "last_qr_clearance", "value": "", "type": "string"}, {"key": "last_qr_reporting", "value": "", "type": "string"}, {"key": "last_qr_compliance", "value": "", "type": "string"}, {"key": "actual_company_api_key", "value": "", "type": "string"}, {"key": "actual_location_secret", "value": "", "type": "string"}, {"key": "last_hash_unknown", "value": ""}, {"key": "last_qr_unknown", "value": ""}, {"key": "last_reporting_hash", "value": ""}, {"key": "last_reporting_qr", "value": ""}, {"key": "last_clearance_hash", "value": ""}, {"key": "last_clearance_qr", "value": ""}]}