#!/bin/bash

# ZATCA Admin Panel Docker Startup Script
# This script helps start the complete ZATCA application stack

echo "🚀 Starting ZATCA Admin Panel..."
echo "=================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Navigate to backend directory
cd backend

echo "📦 Building and starting containers..."

# Stop any existing containers
docker-compose down

# Build and start containers
docker-compose up -d --build

echo "⏳ Waiting for services to start..."
sleep 10

# Check if services are running
echo "🔍 Checking service status..."

# Check Laravel backend
if curl -f http://localhost:8080 > /dev/null 2>&1; then
    echo "✅ Laravel Backend: http://localhost:8080"
else
    echo "⚠️  Laravel Backend: Starting up..."
fi

# Check React frontend
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ React Frontend: http://localhost:3000"
else
    echo "⚠️  React Frontend: Starting up..."
fi

# Check phpMyAdmin
if curl -f http://localhost:8082 > /dev/null 2>&1; then
    echo "✅ phpMyAdmin: http://localhost:8082"
else
    echo "⚠️  phpMyAdmin: Starting up..."
fi

echo ""
echo "🎉 ZATCA Admin Panel is starting up!"
echo "=================================="
echo "📱 Frontend (React):     http://localhost:3000"
echo "🔧 Backend (Laravel):    http://localhost:8080"
echo "🗄️  Database (phpMyAdmin): http://localhost:8082"
echo ""
echo "📋 Database Credentials:"
echo "   Username: zatca_user"
echo "   Password: zatca_password"
echo "   Database: zatca_einvoicing"
echo ""
echo "⏱️  Services may take a few minutes to fully start."
echo "📖 Check logs with: docker-compose logs -f"
echo "🛑 Stop services with: docker-compose down"
echo ""
echo "Happy coding! 🎯"
