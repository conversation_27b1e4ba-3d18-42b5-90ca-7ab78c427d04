<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\Company;
use App\Models\BusinessLocation;
use App\Models\Sandbox;
use App\Models\Simulation;
use App\Models\Production;

class ZatcaApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private $company;
    private $location;
    private $companyApiKey = '0wMv80uWcCjjh3BZyVnNe5FUw7NQWhUM8UaokYrNnIjFs8rH2kK0vwhFxblcym97';
    private $locationSecret = 'ZgNdVVDLJSiqpsoJM6CxnnExgE046eaNDYVi53ni9QdSUJBHSZ8iezaOF3C1Lfw3';

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupTestData();
    }

    private function setupTestData()
    {
        // Create test company
        $this->company = Company::create([
            'name' => 'Test Company',
            'api_key' => $this->companyApiKey,
            'status' => 'active'
        ]);

        // Create test location
        $this->location = BusinessLocation::create([
            'company_id' => $this->company->id,
            'name' => 'Test Location',
            'authentication_token' => $this->locationSecret,
            'status' => 'active'
        ]);

        // Create sandbox environment
        Sandbox::create([
            'location_id' => $this->location->id,
            'csid' => 'test_csid',
            'secret_csid' => 'test_secret_csid',
            'x509_certificate' => 'test_x509_cert',
            'x509_secret' => 'test_x509_secret',
            'private_key' => 'test_private_key',
            'certificate' => 'test_certificate'
        ]);

        // Create simulation environment
        Simulation::create([
            'location_id' => $this->location->id,
            'csid' => 'test_csid_sim',
            'secret_csid' => 'test_secret_csid_sim',
            'x509_certificate' => 'test_x509_cert_sim',
            'x509_secret' => 'test_x509_secret_sim',
            'private_key' => 'test_private_key_sim',
            'certificate' => 'test_certificate_sim'
        ]);

        // Create production environment
        Production::create([
            'location_id' => $this->location->id,
            'csid' => 'test_csid_prod',
            'secret_csid' => 'test_secret_csid_prod',
            'x509_certificate' => 'test_x509_cert_prod',
            'x509_secret' => 'test_x509_secret_prod',
            'private_key' => 'test_private_key_prod',
            'certificate' => 'test_certificate_prod'
        ]);
    }

    private function getValidInvoicePayload($documentType = 'invoice')
    {
        return [
            'invoice' => [
                'invoiceType' => 'simplified',
                'documentType' => $documentType
            ],
            'profileID' => 'reporting:1.0',
            'id' => 'SME' . $this->faker->numberBetween(10000, 99999),
            'uuid' => $this->faker->uuid,
            'issueDate' => now()->format('Y-m-d'),
            'issueTime' => now()->format('H:i:s'),
            'documentCurrencyCode' => 'SAR',
            'taxCurrencyCode' => 'SAR',
            'additionalDocumentReference' => [
                'id' => 'ICV',
                'uuid' => $this->faker->numberBetween(1000, 9999),
                'pih' => 'y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M='
            ],
            'accountingSupplierParty' => [
                'id' => '**********',
                'schema' => 'CRN',
                'streetName' => 'Mihar Al-Daylami',
                'buildingNumber' => '2862',
                'plotIdentification' => '7864',
                'citySubdivisionName' => 'Al salamah',
                'cityName' => 'Makkah',
                'postalZone' => '24226',
                'companyID' => '***************',
                'taxID' => 'VAT',
                'registrationName' => 'Safa cold store for foodstuff Co.'
            ],
            'accountingCustomerParty' => [
                'streetName' => '',
                'buildingNumber' => '',
                'citySubdivisionName' => '',
                'cityName' => '',
                'postalZone' => '',
                'registrationName' => ''
            ],
            'paymentMeansCode' => '10',
            'actualDeliveryDate' => now()->format('Y-m-d'),
            'latestDeliveryDate' => now()->format('Y-m-d'),
            'allowanceCharge' => [
                'chargeIndicator' => 'false',
                'allowanceChargeReason' => 'discount',
                'amount' => '0',
                'taxId' => 'S',
                'taxPercentage' => '15',
                'taxScheme' => 'VAT'
            ],
            'taxAmount' => '1.50',
            'taxTotal' => [
                'taxAmount' => '1.50',
                'tsttaxableAmount' => '10.00',
                'tsttaxAmount' => '1.50',
                'taxId' => 'S',
                'taxPercentage' => '15.00',
                'taxScheme' => 'VAT'
            ],
            'legalMonetaryTotal' => [
                'lineExtensionAmount' => '10.00',
                'taxExclusiveAmount' => '10.00',
                'taxInclusiveAmount' => '11.50',
                'allowanceTotalAmount' => '0.00',
                'prepaidAmount' => '0.00',
                'payableAmount' => '11.50'
            ],
            'invoiceLines' => [
                [
                    'id' => '1',
                    'invoicedQuantity' => '1.0000',
                    'lineExtensionAmount' => '10.00',
                    'taxAmount' => '1.50',
                    'roundingAmount' => '11.50',
                    'itemName' => 'Test Product',
                    'taxId' => 'S',
                    'taxPercentage' => '15.00',
                    'taxScheme' => 'VAT',
                    'priceAmount' => '10.00',
                    'allowanceChargeReason' => 'discount',
                    'allowanceChargeAmount' => '0.00'
                ]
            ]
        ];
    }

    private function getValidStandardInvoicePayload($documentType = 'invoice')
    {
        $payload = $this->getValidInvoicePayload($documentType);
        $payload['invoice']['invoiceType'] = 'standard';
        $payload['profileID'] = 'clearance:1.0';
        $payload['id'] = 'STD' . $this->faker->numberBetween(10000, 99999);
        
        // Add customer details for standard invoice
        $payload['accountingCustomerParty'] = [
            'id' => '**********',
            'schema' => 'CRN',
            'streetName' => 'Customer Street',
            'buildingNumber' => '9318',
            'plotIdentification' => '12',
            'citySubdivisionName' => 'Customer District',
            'cityName' => 'Customer City',
            'postalZone' => '13714',
            'companyID' => '**********00003',
            'taxID' => 'VAT',
            'registrationName' => 'Test Customer',
            'countryCode' => 'SA'
        ];

        return $payload;
    }

    private function getAuthHeaders()
    {
        return [
            'Content-Type' => 'application/json',
            'secret' => $this->locationSecret,
            'Authorization' => 'Api-Key ' . $this->companyApiKey
        ];
    }

    /**
     * Test Compliance API - Standard Invoice
     */
    public function test_compliance_standard_invoice()
    {
        $payload = $this->getValidStandardInvoicePayload('invoice');
        
        $response = $this->postJson('/api/sandbox/compliance', $payload, $this->getAuthHeaders());
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'Message',
                    'invoiceHash',
                    'qrcode',
                    'data' => [
                        'validationResults',
                        'clearanceStatus',
                        'reportingStatus'
                    ]
                ])
                ->assertJson([
                    'status' => '200',
                    'Message' => 'Success'
                ]);
    }

    /**
     * Test Compliance API - Simplified Invoice
     */
    public function test_compliance_simplified_invoice()
    {
        $payload = $this->getValidInvoicePayload('invoice');
        
        $response = $this->postJson('/api/sandbox/compliance', $payload, $this->getAuthHeaders());
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'Message',
                    'invoiceHash',
                    'qrcode',
                    'data'
                ])
                ->assertJson([
                    'status' => '200',
                    'Message' => 'Success'
                ]);
    }

    /**
     * Test Reporting API - Simplified Invoice
     */
    public function test_reporting_simplified_invoice()
    {
        $payload = $this->getValidInvoicePayload('invoice');
        
        $response = $this->postJson('/api/sandbox/reporting', $payload, $this->getAuthHeaders());
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'Message',
                    'invoiceHash',
                    'qrcode',
                    'data' => [
                        'validationResults',
                        'reportingStatus'
                    ]
                ])
                ->assertJson([
                    'status' => '200',
                    'Message' => 'Success',
                    'data' => [
                        'reportingStatus' => 'REPORTED'
                    ]
                ]);
    }

    /**
     * Test Clearance API - Standard Invoice
     */
    public function test_clearance_standard_invoice()
    {
        $payload = $this->getValidStandardInvoicePayload('invoice');
        
        $response = $this->postJson('/api/sandbox/clearance', $payload, $this->getAuthHeaders());
        
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'status',
                    'Message',
                    'invoiceHash',
                    'qrcode',
                    'data' => [
                        'validationResults',
                        'clearanceStatus'
                    ]
                ])
                ->assertJson([
                    'status' => '200',
                    'Message' => 'Success',
                    'data' => [
                        'clearanceStatus' => 'CLEARED'
                    ]
                ]);
    }

    /**
     * Test Authentication Failure
     */
    public function test_authentication_failure()
    {
        $payload = $this->getValidInvoicePayload();
        
        $response = $this->postJson('/api/sandbox/reporting', $payload, [
            'Content-Type' => 'application/json',
            'secret' => 'invalid_secret',
            'Authorization' => 'Api-Key invalid_key'
        ]);
        
        $response->assertStatus(401);
    }

    /**
     * Test Validation Failure - Missing Required Fields
     */
    public function test_validation_failure_missing_fields()
    {
        $payload = [
            'invoice' => [
                'invoiceType' => 'simplified',
                'documentType' => 'invoice'
            ]
            // Missing required fields
        ];
        
        $response = $this->postJson('/api/sandbox/reporting', $payload, $this->getAuthHeaders());
        
        $response->assertStatus(400)
                ->assertJson([
                    'status' => '400',
                    'Message' => 'Validation failed'
                ]);
    }

    /**
     * Test Invalid Environment
     */
    public function test_invalid_environment()
    {
        $payload = $this->getValidInvoicePayload();
        
        $response = $this->postJson('/api/invalid_env/reporting', $payload, $this->getAuthHeaders());
        
        $response->assertStatus(404); // Route not found for invalid environment
    }
}
