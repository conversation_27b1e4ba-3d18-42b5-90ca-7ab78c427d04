<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Services\ZatcaComplianceService;
use App\Constants\ZatcaConstants;

class ZatcaComplianceServiceTest extends TestCase
{
    private $complianceService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->complianceService = new ZatcaComplianceService();
    }

    /**
     * Test invoice type code generation
     */
    public function test_invoice_type_code_generation()
    {
        // Test simplified invoice
        $code = $this->complianceService->getInvoiceTypeCode('simplified', 'invoice');
        $this->assertEquals('388', $code);

        // Test simplified credit note
        $code = $this->complianceService->getInvoiceTypeCode('simplified', 'credit_note');
        $this->assertEquals('383', $code);

        // Test simplified debit note
        $code = $this->complianceService->getInvoiceTypeCode('simplified', 'debit_note');
        $this->assertEquals('381', $code);

        // Test standard invoice
        $code = $this->complianceService->getInvoiceTypeCode('standard', 'invoice');
        $this->assertEquals('388', $code);

        // Test standard credit note
        $code = $this->complianceService->getInvoiceTypeCode('standard', 'credit_note');
        $this->assertEquals('383', $code);

        // Test standard debit note
        $code = $this->complianceService->getInvoiceTypeCode('standard', 'debit_note');
        $this->assertEquals('383', $code);
    }

    /**
     * Test invoice type code name generation
     */
    public function test_invoice_type_code_name_generation()
    {
        // Test simplified invoice
        $codeName = $this->complianceService->getInvoiceTypeCodeName('simplified', 'invoice');
        $this->assertEquals('0200000', $codeName);

        // Test simplified credit note
        $codeName = $this->complianceService->getInvoiceTypeCodeName('simplified', 'credit_note');
        $this->assertEquals('0211010', $codeName);

        // Test simplified debit note
        $codeName = $this->complianceService->getInvoiceTypeCodeName('simplified', 'debit_note');
        $this->assertEquals('0211010', $codeName);

        // Test standard invoice
        $codeName = $this->complianceService->getInvoiceTypeCodeName('standard', 'invoice');
        $this->assertEquals('0100000', $codeName);

        // Test standard credit note
        $codeName = $this->complianceService->getInvoiceTypeCodeName('standard', 'credit_note');
        $this->assertEquals('0111010', $codeName);

        // Test standard debit note
        $codeName = $this->complianceService->getInvoiceTypeCodeName('standard', 'debit_note');
        $this->assertEquals('0111010', $codeName);
    }

    /**
     * Test XML generation for simplified invoice
     */
    public function test_xml_generation_simplified_invoice()
    {
        $invoiceData = [
            'id' => 'SME12345',
            'uuid' => '********-1234-1234-1234-********9012',
            'issueDate' => '2025-06-04',
            'issueTime' => '15:30:00',
            'documentCurrencyCode' => 'SAR',
            'taxCurrencyCode' => 'SAR',
            'additionalDocumentReference' => [
                'id' => 'ICV',
                'uuid' => '1234',
                'pih' => 'y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M='
            ],
            'accountingSupplierParty' => [
                'companyID' => '***************',
                'registrationName' => 'Test Company'
            ],
            'accountingCustomerParty' => [
                'registrationName' => ''
            ],
            'taxTotal' => [
                'taxAmount' => '1.50'
            ],
            'legalMonetaryTotal' => [
                'taxInclusiveAmount' => '11.50'
            ],
            'invoiceLines' => [
                [
                    'id' => '1',
                    'itemName' => 'Test Product'
                ]
            ]
        ];

        $xml = $this->complianceService->generateInvoiceXml($invoiceData, 'simplified', 'invoice');

        // Test that XML contains required elements
        $this->assertStringContainsString('<cbc:ID>SME12345</cbc:ID>', $xml);
        $this->assertStringContainsString('<cbc:UUID>********-1234-1234-1234-********9012</cbc:UUID>', $xml);
        $this->assertStringContainsString('<cbc:IssueDate>2025-06-04</cbc:IssueDate>', $xml);
        $this->assertStringContainsString('<cbc:InvoiceTypeCode name="0200000">388</cbc:InvoiceTypeCode>', $xml);
        $this->assertStringContainsString('<cbc:DocumentCurrencyCode>SAR</cbc:DocumentCurrencyCode>', $xml);
        $this->assertStringContainsString('<cbc:TaxCurrencyCode>SAR</cbc:TaxCurrencyCode>', $xml);
    }

    /**
     * Test XML generation for standard invoice
     */
    public function test_xml_generation_standard_invoice()
    {
        $invoiceData = [
            'id' => 'STD12345',
            'uuid' => '********-1234-1234-1234-********9012',
            'issueDate' => '2025-06-04',
            'issueTime' => '15:30:00',
            'documentCurrencyCode' => 'SAR',
            'taxCurrencyCode' => 'SAR',
            'additionalDocumentReference' => [
                'id' => 'ICV',
                'uuid' => '1234',
                'pih' => 'y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M='
            ],
            'accountingSupplierParty' => [
                'companyID' => '***************',
                'registrationName' => 'Test Company'
            ],
            'accountingCustomerParty' => [
                'companyID' => '***************',
                'registrationName' => 'Test Customer'
            ],
            'taxTotal' => [
                'taxAmount' => '1.50'
            ],
            'legalMonetaryTotal' => [
                'taxInclusiveAmount' => '11.50'
            ],
            'invoiceLines' => [
                [
                    'id' => '1',
                    'itemName' => 'Test Product'
                ]
            ]
        ];

        $xml = $this->complianceService->generateInvoiceXml($invoiceData, 'standard', 'invoice');

        // Test that XML contains required elements
        $this->assertStringContainsString('<cbc:ID>STD12345</cbc:ID>', $xml);
        $this->assertStringContainsString('<cbc:InvoiceTypeCode name="0100000">388</cbc:InvoiceTypeCode>', $xml);
        $this->assertStringContainsString('Test Customer', $xml);
    }

    /**
     * Test XML generation for credit note
     */
    public function test_xml_generation_credit_note()
    {
        $invoiceData = [
            'id' => 'SME12345',
            'uuid' => '********-1234-1234-1234-********9012',
            'issueDate' => '2025-06-04',
            'issueTime' => '15:30:00',
            'documentCurrencyCode' => 'SAR',
            'taxCurrencyCode' => 'SAR',
            'additionalDocumentReference' => [
                'id' => 'ICV',
                'uuid' => '1234',
                'pih' => 'y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M='
            ],
            'accountingSupplierParty' => [
                'companyID' => '***************',
                'registrationName' => 'Test Company'
            ],
            'accountingCustomerParty' => [
                'registrationName' => ''
            ],
            'taxTotal' => [
                'taxAmount' => '1.50'
            ],
            'legalMonetaryTotal' => [
                'taxInclusiveAmount' => '11.50'
            ],
            'invoiceLines' => [
                [
                    'id' => '1',
                    'itemName' => 'Test Product'
                ]
            ],
            'billingReference' => [
                'invoiceDocumentReference' => [
                    'id' => 'INV-12345',
                    'issueDate' => '2025-06-03'
                ]
            ],
            'creditDebitReason' => 'Product return'
        ];

        $xml = $this->complianceService->generateInvoiceXml($invoiceData, 'simplified', 'credit_note');

        // Test that XML contains credit note specific elements
        $this->assertStringContainsString('<cbc:InvoiceTypeCode name="0211010">383</cbc:InvoiceTypeCode>', $xml);
        $this->assertStringContainsString('<cac:BillingReference>', $xml);
        $this->assertStringContainsString('<cbc:ID>INV-12345</cbc:ID>', $xml);
        $this->assertStringContainsString('Product return', $xml);
    }

    /**
     * Test XML generation for debit note
     */
    public function test_xml_generation_debit_note()
    {
        $invoiceData = [
            'id' => 'SME12345',
            'uuid' => '********-1234-1234-1234-********9012',
            'issueDate' => '2025-06-04',
            'issueTime' => '15:30:00',
            'documentCurrencyCode' => 'SAR',
            'taxCurrencyCode' => 'SAR',
            'additionalDocumentReference' => [
                'id' => 'ICV',
                'uuid' => '1234',
                'pih' => 'y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M='
            ],
            'accountingSupplierParty' => [
                'companyID' => '***************',
                'registrationName' => 'Test Company'
            ],
            'accountingCustomerParty' => [
                'registrationName' => ''
            ],
            'taxTotal' => [
                'taxAmount' => '1.50'
            ],
            'legalMonetaryTotal' => [
                'taxInclusiveAmount' => '11.50'
            ],
            'invoiceLines' => [
                [
                    'id' => '1',
                    'itemName' => 'Test Product'
                ]
            ],
            'billingReference' => [
                'invoiceDocumentReference' => [
                    'id' => 'INV-12345',
                    'issueDate' => '2025-06-03'
                ]
            ],
            'creditDebitReason' => 'Additional charges'
        ];

        $xml = $this->complianceService->generateInvoiceXml($invoiceData, 'simplified', 'debit_note');

        // Test that XML contains debit note specific elements
        $this->assertStringContainsString('<cbc:InvoiceTypeCode name="0211010">381</cbc:InvoiceTypeCode>', $xml);
        $this->assertStringContainsString('<cac:BillingReference>', $xml);
        $this->assertStringContainsString('Additional charges', $xml);
    }

    /**
     * Test environment URL retrieval
     */
    public function test_environment_url_retrieval()
    {
        // Test compliance URLs
        $sandboxUrl = ZatcaConstants::ZATCA_COMPLIANCE_URLS['sandbox'];
        $this->assertEquals('https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/compliance/invoices', $sandboxUrl);

        $simulationUrl = ZatcaConstants::ZATCA_COMPLIANCE_URLS['simulation'];
        $this->assertEquals('https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/compliance/invoices', $simulationUrl);

        $productionUrl = ZatcaConstants::ZATCA_COMPLIANCE_URLS['production'];
        $this->assertEquals('https://gw-fatoora.zatca.gov.sa/e-invoicing/core/compliance/invoices', $productionUrl);

        // Test reporting URLs
        $sandboxReportingUrl = ZatcaConstants::ZATCA_REPORTING_URLS['sandbox'];
        $this->assertEquals('https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/invoices/reporting/single', $sandboxReportingUrl);

        // Test clearance URLs
        $sandboxClearanceUrl = ZatcaConstants::ZATCA_CLEARANCE_URLS['sandbox'];
        $this->assertEquals('https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/invoices/clearance/single', $sandboxClearanceUrl);
    }

    /**
     * Test constants validation
     */
    public function test_constants_validation()
    {
        // Test environments
        $this->assertContains('sandbox', ZatcaConstants::ENVIRONMENTS);
        $this->assertContains('simulation', ZatcaConstants::ENVIRONMENTS);
        $this->assertContains('production', ZatcaConstants::ENVIRONMENTS);

        // Test invoice types
        $this->assertContains('standard', ZatcaConstants::INVOICE_TYPES);
        $this->assertContains('simplified', ZatcaConstants::INVOICE_TYPES);

        // Test document types
        $this->assertContains('invoice', ZatcaConstants::DOCUMENT_TYPES);
        $this->assertContains('credit_note', ZatcaConstants::DOCUMENT_TYPES);
        $this->assertContains('debit_note', ZatcaConstants::DOCUMENT_TYPES);

        // Test profile IDs
        $this->assertEquals('reporting:1.0', ZatcaConstants::PROFILE_REPORTING);
        $this->assertEquals('clearance:1.0', ZatcaConstants::PROFILE_CLEARANCE);

        // Test status codes
        $this->assertEquals('200', ZatcaConstants::HTTP_STATUS_SUCCESS);
        $this->assertEquals('400', ZatcaConstants::HTTP_STATUS_BAD_REQUEST);

        // Test messages
        $this->assertEquals('Success', ZatcaConstants::MESSAGE_SUCCESS);
        $this->assertEquals('Fail', ZatcaConstants::MESSAGE_FAIL);
    }
}
