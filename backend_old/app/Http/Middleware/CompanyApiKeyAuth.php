<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Company;

class CompanyApiKeyAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get API key from Authorization header
        $authHeader = $request->header('Authorization');

        if (!$authHeader) {
            return response()->json([
                'detail' => 'Authorization header required.',
            ], 401);
        }

        // Extract API key from Authorization header
        $apiKey = $authHeader;

        // Remove "Api-Key " prefix if present
        if (str_starts_with($apiKey, 'Api-Key ')) {
            $apiKey = substr($apiKey, 8);
        } elseif (str_starts_with($apiKey, 'Bearer ')) {
            // Also support Bearer format for backward compatibility
            $apiKey = substr($apiKey, 7);
        }

        // Find company by API key
        $company = Company::where('api_key', $apiKey)->first();

        if (!$company) {
            return response()->json([
                'detail' => 'Invalid API key or company not found.',
            ], 401);
        }

        // Add company to request for use in controllers
        $request->attributes->set('authenticated_company', $company);

        return $next($request);
    }
}
