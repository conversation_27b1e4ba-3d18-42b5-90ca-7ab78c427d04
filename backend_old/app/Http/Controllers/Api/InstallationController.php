<?php

/*
ZatcaNext - ZATCA e-Invoicing Middleware for Phase 2 Compliance

Copyright (c) 2025 ZatcaNext. All rights reserved.

This software is the proprietary property of ZatcaNext and is protected
by copyright and other intellectual property laws. Unauthorized use,
reproduction, modification, distribution, or reverse-engineering of this
software, in whole or in part, is strictly prohibited without express
written permission from ZatcaNext.

This software is provided "as is" without warranties of any kind, express
or implied. ZatcaNext disclaims all liability for damages arising from its use.

For support or licensing inquiries, contact: <EMAIL>
Website: https://zatcanext.com
Terms of Use: https://zatcanext.com/terms
*/

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Models\User;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;

/**
 * @OA\Tag(
 *     name="🔧 Installation",
 *     description="Installation wizard endpoints"
 * )
 */
class InstallationController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/installation/status",
     *     summary="Check Installation Status",
     *     tags={"🔧 Installation"},
     *     @OA\Response(
     *         response=200,
     *         description="Installation status",
     *         @OA\JsonContent(
     *             @OA\Property(property="installation_completed", type="boolean"),
     *             @OA\Property(property="message", type="string")
     *         )
     *     )
     * )
     */
    public function status(): JsonResponse
    {
        $isCompleted = Setting::isInstallationCompleted();

        return response()->json([
            'installation_completed' => $isCompleted,
            'message' => $isCompleted
                ? 'Installation completed. You may login now.'
                : 'Installation setup required.'
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/installation/create-user",
     *     summary="Step 1: Create Initial Superadmin User",
     *     tags={"🔧 Installation"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name", "email", "password"},
     *             @OA\Property(property="name", type="string", example="Super Admin"),
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", example="SecurePassword123!"),
     *             @OA\Property(property="password_confirmation", type="string", example="SecurePassword123!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="User created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string"),
     *             @OA\Property(property="user", type="object"),
     *             @OA\Property(property="next_step", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Installation already completed or validation failed"
     *     )
     * )
     */
    public function createUser(Request $request): JsonResponse
    {
        // Check if installation is already completed
        if (Setting::isInstallationCompleted()) {
            return response()->json([
                'message' => 'Installation already completed. You may login now.',
                'error' => 'INSTALLATION_ALREADY_COMPLETED'
            ], 400);
        }

        // Check if user already exists (step already completed)
        if (User::count() > 0) {
            return response()->json([
                'message' => 'User already created. Please create a company now.',
                'error' => 'USER_ALREADY_EXISTS',
                'next_step' => 'Create a company using POST /api/company'
            ], 400);
        }

        // Validate request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Create superadmin user
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'email_verified_at' => now(),
            ]);

            return response()->json([
                'message' => 'Success! User created successfully. Please create a company now.',
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                ],
                'next_step' => 'Create a company using POST /api/company with JWT authentication'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'User creation failed. Please try again.',
                'error' => 'USER_CREATION_FAILED'
            ], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/installation/complete",
     *     summary="Step 3: Complete Installation Process",
     *     tags={"🔧 Installation"},
     *     @OA\Response(
     *         response=200,
     *         description="Installation completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string"),
     *             @OA\Property(property="installation_completed", type="boolean")
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Installation requirements not met"
     *     )
     * )
     */
    public function complete(): JsonResponse
    {
        // Check if installation is already completed
        if (Setting::isInstallationCompleted()) {
            return response()->json([
                'message' => 'Installation already completed. You may login now.',
                'installation_completed' => true
            ], 200);
        }

        // Check if all required steps are completed
        $userExists = User::count() > 0;
        $companyExists = Company::count() > 0;
        $locationExists = \App\Models\BusinessLocation::count() > 0;

        if (!$userExists) {
            return response()->json([
                'message' => 'User not created yet. Please create a user first.',
                'error' => 'USER_NOT_CREATED',
                'next_step' => 'Create user using POST /api/installation/create-user'
            ], 400);
        }

        if (!$companyExists) {
            return response()->json([
                'message' => 'Company not created yet. Please create a company.',
                'error' => 'COMPANY_NOT_CREATED',
                'next_step' => 'Create company using POST /api/company'
            ], 400);
        }

        if (!$locationExists) {
            return response()->json([
                'message' => 'Business location not created yet. Please create a location.',
                'error' => 'LOCATION_NOT_CREATED',
                'next_step' => 'Create location using POST /api/business-location'
            ], 400);
        }

        // Mark installation as completed
        Setting::markInstallationCompleted();

        return response()->json([
            'message' => 'Success! Installation completed successfully. You can now use all application features.',
            'installation_completed' => true,
            'next_step' => 'Login using POST /api/token'
        ], 200);
    }
}
