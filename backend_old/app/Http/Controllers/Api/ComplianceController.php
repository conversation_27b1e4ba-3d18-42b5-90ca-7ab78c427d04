<?php

namespace App\Http\Controllers\Api;

use App\Constants\ZatcaConstants;
use App\Http\Controllers\Controller;
use App\Models\BusinessLocation;
use App\Services\ZatcaComplianceService;
use App\Services\ZatcaCsidService;
use App\Services\ZatcaXmlSigningService;
use App\Services\CsrGenerationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Exception;
use RuntimeException;

/**
 * ZATCA Compliance Controller
 *
 * Handles all ZATCA compliance operations including
 * - Certificate generation (CSID/X509)
 * - Document compliance validation
 * - Multi-environment support (sandbox/simulation/production)
 * - All document types (invoice/credit_note/debit_note)
 *
 * @OA\Tag(
 *     name="ZATCA Compliance",
 *     description="ZATCA Compliance API endpoints"
 * )
 */
class ComplianceController extends Controller
{
    private ZatcaComplianceService $complianceService;
    private ZatcaXmlSigningService $xmlSigningService;

    public function __construct(
        ZatcaComplianceService $complianceService,
        ZatcaXmlSigningService $xmlSigningService
    ) {
        $this->complianceService = $complianceService;
        $this->xmlSigningService = $xmlSigningService;
    }
    /**
     * Unified Compliance Check
     *
     * Performs complete ZATCA compliance validation with automatic certificate generation.
     * Tests all 6 document types (standard/simplified × invoice/credit_note/debit_note).
     *
     * @OA\Post(
     *     path="/api/{locationId}/{environment}/compliance-check",
     *     summary="Complete ZATCA Compliance Check",
     *     description="Performs comprehensive compliance validation for all document types with automatic certificate management",
     *     operationId="complianceCheck",
     *     tags={"ZATCA Compliance"},
     *     security={{"ApiKeyAuth": {}}},
     *     @OA\Parameter(
     *         name="locationId",
     *         in="path",
     *         required=true,
     *         description="Business Location ID",
     *         @OA\Schema(type="integer", example=141)
     *     ),
     *     @OA\Parameter(
     *         name="environment",
     *         in="path",
     *         required=true,
     *         description="ZATCA Environment",
     *         @OA\Schema(type="string", enum={"sandbox", "simulation", "production"}, example="sandbox")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="OTP for compliance check",
     *         @OA\JsonContent(
     *             required={"otp"},
     *             @OA\Property(
     *                 property="otp",
     *                 type="string",
     *                 description="One-time password (sandbox: 123345, others: from FATOORA portal)",
     *                 example="123345"
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Compliance check completed",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string", example="200"),
     *             @OA\Property(property="message", type="string", example="Compliance check completed"),
     *             @OA\Property(property="location_id", type="integer", example=141),
     *             @OA\Property(property="environment", type="string", example="sandbox"),
     *             @OA\Property(
     *                 property="certificates_generated",
     *                 type="object",
     *                 @OA\Property(property="csid", type="string", example="✅ Generated successfully"),
     *                 @OA\Property(property="x509", type="string", example="✅ Generated successfully")
     *             ),
     *             @OA\Property(
     *                 property="compliance_results",
     *                 type="object",
     *                 @OA\Property(property="standard_invoice", type="string", example="✅ PASS"),
     *                 @OA\Property(property="simplified_invoice", type="string", example="✅ PASS"),
     *                 @OA\Property(property="standard_credit_note", type="string", example="✅ PASS"),
     *                 @OA\Property(property="simplified_credit_note", type="string", example="✅ PASS"),
     *                 @OA\Property(property="standard_debit_note", type="string", example="✅ PASS"),
     *                 @OA\Property(property="simplified_debit_note", type="string", example="✅ PASS")
     *             ),
     *             @OA\Property(
     *                 property="test_statistics",
     *                 type="object",
     *                 @OA\Property(property="total_tests", type="integer", example=6),
     *                 @OA\Property(property="passed_tests", type="integer", example=6),
     *                 @OA\Property(property="failed_tests", type="integer", example=0),
     *                 @OA\Property(property="success_rate", type="string", example="100%")
     *             )
     *         )
     *     )
     * )
     */
    public function complianceCheck(Request $request, int $locationId, string $environment): JsonResponse
    {
        try {
            $this->validateEnvironment($environment);
            $this->validateOtpInput($request);

            $location = $this->getAuthenticatedLocation($request, $locationId);
            $otp = $request->input('otp');

            $results = [
                'status' => '200',
                'message' => 'Compliance check completed',
                'location_id' => $locationId,
                'location_name' => $location->seller_name,
                'environment' => $environment,
                'otp_used' => $otp,
                'certificates_generated' => [],
                'compliance_results' => [],
                'summary' => ''
            ];

            $results['certificates_generated']['csid'] = $this->generateCsidIfNeeded($location, $environment, $otp);
            $results['certificates_generated']['x509'] = $this->generateX509IfNeeded($location, $environment);

            $testResults = $this->validateAllDocumentTypes($location, $environment);
            $results['compliance_results'] = $testResults['results'];
            $results['test_statistics'] = $testResults['statistics'];
            $results['summary'] = $testResults['summary'];

            return response()->json($results);

        } catch (Exception $e) {
            return response()->json([
                'status' => '500',
                'message' => 'Compliance check failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Validate environment parameter
     */
    private function validateEnvironment(string $environment): void
    {
        if (!in_array($environment, ZatcaConstants::ENVIRONMENTS)) {
            throw new RuntimeException('Invalid environment: ' . $environment);
        }
    }

    /**
     * Validate OTP input
     */
    private function validateOtpInput(Request $request): void
    {
        $validator = Validator::make($request->all(), [
            'otp' => 'required|string'
        ]);

        if ($validator->fails()) {
            throw new RuntimeException('OTP is required');
        }
    }

    /**
     * Get authenticated location
     */
    private function getAuthenticatedLocation(Request $request, int $locationId): BusinessLocation
    {
        $location = $request->attributes->get('authenticated_location');

        if (!$location || $location->id !== $locationId) {
            throw new RuntimeException('Location not found or access denied');
        }

        return $location;
    }

    /**
     * Generate CSID certificate if needed
     */
    private function generateCsidIfNeeded(BusinessLocation $location, string $environment, string $otp): string
    {
        $environmentData = $location->{$environment};

        if ($environmentData && $environmentData->csid) {
            return '✅ Already exists';
        }

        try {

            if (!$environmentData || !$environmentData->csr) {
                $this->generateCsrForLocation($location, $environmentData);
            }

            $csidService = app(ZatcaCsidService::class);
            $result = $csidService->generateCsid($environmentData->csr, $otp, $environment);

            $environmentData->update([
                'csid' => $result['binarySecurityToken'],
                'csid_base64' => base64_decode($result['binarySecurityToken']),
                'secret_csid' => $result['secret'],
                'csid_request' => $result['requestID'],
            ]);

            return '✅ Generated successfully';
        } catch (Exception $e) {
            throw new RuntimeException('CSID generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Generate CSR for location using actual location data (no fallbacks)
     */
    private function generateCsrForLocation(BusinessLocation $location, $environmentData): void
    {
        if (!$location->seller_name) {
            throw new RuntimeException('Location seller_name is required for CSR generation');
        }

        if (!$location->tax_no) {
            throw new RuntimeException('Location tax_no is required for CSR generation');
        }

        if (!$location->address) {
            throw new RuntimeException('Location address is required for CSR generation');
        }

        $csrService = app(CsrGenerationService::class);
        $locationData = [
            'organization_name' => $location->seller_name,
            'organization_unit' => $location->seller_name,
            'common_name' => $location->seller_name,
            'serial_number' => $location->serial_number ?: '1-' . $location->seller_name . '|2-1100|3-' . $location->tax_no,
            'tax_no' => $location->tax_no,
            'title' => '1100',
            'registered_address' => $location->address,
            'business_category' => 'Supply activities'
        ];

        $csrData = $csrService->generateCsr($locationData);

        $environmentData->update([
            'csr' => $csrData['csr'],
            'private_key' => $csrData['pvt'],
            'public_key' => $csrData['pbl']
        ]);
    }

    /**
     * Generate X509 certificate if needed
     */
    private function generateX509IfNeeded(BusinessLocation $location, string $environment): string
    {
        $environmentData = $location->{$environment};

        if ($environmentData && $environmentData->x509_certificate) {
            return '✅ Already exists';
        }

        try {

            if (!$environmentData || !$environmentData->csid || !$environmentData->secret_csid || !$environmentData->csid_request) {
                return '❌ CSID must be generated first';
            }

            $csidService = app(ZatcaCsidService::class);
            $result = $csidService->generateX509(
                $environmentData->csid,
                $environmentData->secret_csid,
                $environmentData->csid_request,
                $environment
            );

            // Update environment with X509 data
            $environmentData->update([
                'x509_certificate' => $result['binarySecurityToken'],
                'x509_base64' => base64_decode($result['binarySecurityToken']),
                'x509_secret' => $result['secret'],
                'x509_request' => $result['requestID'] ?? null,
            ]);

            return '✅ Generated successfully';
        } catch (Exception $e) {
            throw new RuntimeException('X509 generation failed: ' . $e->getMessage());
        }
    }

    /**
     * Validate all document types for compliance
     */
    private function validateAllDocumentTypes(BusinessLocation $location, string $environment): array
    {
        $documentTypes = ['invoice', 'credit_note', 'debit_note'];
        $invoiceTypes = ['standard', 'simplified'];

        $results = [];
        $totalTests = 0;
        $passedTests = 0;

        foreach ($documentTypes as $documentType) {
            foreach ($invoiceTypes as $invoiceType) {
                $testKey = "{$invoiceType}_{$documentType}";
                $totalTests++;

                try {
                    $testResult = $this->validateDocumentType($location, $environment, $documentType, $invoiceType);
                    $isSuccess = $this->isValidationSuccessful($testResult);

                    $results[$testKey] = $isSuccess ? '✅ PASS' : '❌ FAIL';

                    if ($isSuccess) {
                        $passedTests++;
                    }
                } catch (Exception $e) {
                    $results[$testKey] = '❌ ERROR: ' . $e->getMessage();
                }
            }
        }

        $successRate = round(($passedTests / $totalTests) * 100, 2) . '%';
        $summary = $passedTests === $totalTests
            ? "✅ All {$totalTests} document types validated successfully"
            : "⚠️ {$passedTests}/{$totalTests} document types passed validation";

        return [
            'results' => $results,
            'statistics' => [
                'total_tests' => $totalTests,
                'passed_tests' => $passedTests,
                'failed_tests' => $totalTests - $passedTests,
                'success_rate' => $successRate
            ],
            'summary' => $summary
        ];
    }

    /**
     * Validate specific document type
     */
    private function validateDocumentType(BusinessLocation $location, string $environment, string $documentType, string $invoiceType): array
    {
        $environmentData = $location->{$environment};

        if (!$environmentData || !$environmentData->csid) {
            throw new RuntimeException('CSID not available for validation');
        }

        if (!$environmentData->private_key || !$environmentData->csid_base64) {
            throw new RuntimeException('Private key or certificate not available for signing');
        }


        $testPayload = $this->generateTestPayload($location, $documentType, $invoiceType);

        $xmlContent = $this->complianceService->generateInvoiceXml($testPayload, $invoiceType, $documentType);
        $xmlBase64 = base64_encode($xmlContent);

        $signingResult = $this->xmlSigningService->signXmlDocument(
            $xmlBase64,
            $environmentData->private_key,
            $environmentData->csid_base64
        );

        $complianceData = [
            'uuid' => $testPayload['uuid'],
            'invoiceHash' => $signingResult['invoiceHash'],
            'invoice' => $signingResult['invoiceXml']
        ];

        return $this->complianceService->processCompliance(
            $complianceData,
            $environmentData->csid,
            $environmentData->secret_csid,
            $environment
        );
    }

    /**
     * Check if a validation result indicates success
     */
    private function isValidationSuccessful(array $testResult): bool
    {
        if (!is_array($testResult)) {
            return false;
        }

        if (isset($testResult['validationResults']['status']) &&
            in_array($testResult['validationResults']['status'], ['PASS', 'WARNING'])) {
            return true;
        }

        if (isset($testResult['clearanceStatus']) && $testResult['clearanceStatus'] === 'CLEARED') {
            return true;
        }

        if (isset($testResult['reportingStatus']) && $testResult['reportingStatus'] === 'REPORTED') {
            return true;
        }

        return false;
    }

    /**
     * Generate test payload using actual location data (no fallback values)
     */
    private function generateTestPayload(BusinessLocation $location, string $documentType, string $invoiceType): array
    {

        if (!$location->seller_name) {
            throw new RuntimeException('Location seller_name is required');
        }

        if (!$location->tax_no) {
            throw new RuntimeException('Location tax_no is required');
        }

        $currentDate = date('Y-m-d');
        $currentTime = date('H:i:s');

        $basePayload = [
            'invoice' => [
                'invoiceType' => $invoiceType,
                'documentType' => $documentType
            ],
            'profileID' => ZatcaConstants::PROFILE_REPORTING,
            'id' => random_int(1000, 9999),
            'uuid' => $this->generateUuid(),
            'issueDate' => $currentDate,
            'issueTime' => $currentTime,
            'documentCurrencyCode' => 'SAR',
            'taxCurrencyCode' => 'SAR',
            'additionalDocumentReference' => [
                'id' => 'ICV',
                'uuid' => random_int(100, 999),
                'pih' => 'y8jYyqsoabWMo02F3+euJY4gxwAGwDl+ijqPoiVWG8M='
            ],
            'accountingSupplierParty' => [
                'id' => '**********',
                'schema' => 'CRN',
                'streetName' => $location->address,
                'buildingNumber' => '2862',
                'plotIdentification' => '7864',
                'citySubdivisionName' => 'Business District',
                'cityName' => 'Business City',
                'postalZone' => '24226',
                'companyID' => $location->tax_no,
                'taxID' => 'VAT',
                'registrationName' => $location->seller_name
            ],
            'accountingCustomerParty' => [
                'id' => '**********',
                'schema' => 'CRN',
                'streetName' => 'Customer Street',
                'buildingNumber' => '9318',
                'plotIdentification' => '12',
                'citySubdivisionName' => 'Customer District',
                'cityName' => 'Customer City',
                'postalZone' => '13714',
                'companyID' => '***************',
                'taxID' => 'VAT',
                'registrationName' => 'Test Customer',
                'countryCode' => 'SA'
            ],
            'paymentMeansCode' => '10',
            'actualDeliveryDate' => $currentDate,
            'latestDeliveryDate' => $currentDate,
            'instructionNote' => 'Test instruction note',
            'allowanceCharge' => [
                'chargeIndicator' => 'false',
                'allowanceChargeReason' => 'discount',
                'amount' => '0',
                'taxId' => 'S',
                'taxPercentage' => '15',
                'taxScheme' => 'VAT'
            ],
            'taxAmount' => '1.50',
            'taxTotal' => [
                'taxAmount' => '1.50',
                'tsttaxableAmount' => '10.00',
                'tsttaxAmount' => '1.50',
                'taxId' => 'S',
                'taxPercentage' => '15.00',
                'taxScheme' => 'VAT'
            ],
            'legalMonetaryTotal' => [
                'lineExtensionAmount' => '10.00',
                'taxExclusiveAmount' => '10.00',
                'taxInclusiveAmount' => '11.50',
                'allowanceTotalAmount' => '0.00',
                'prepaidAmount' => '0.00',
                'payableAmount' => '11.50'
            ],
            'invoiceLines' => [
                [
                    'id' => '1',
                    'invoicedQuantity' => '1.0000',
                    'lineExtensionAmount' => '10.00',
                    'taxAmount' => '1.50',
                    'roundingAmount' => '11.50',
                    'itemName' => "Test {$documentType} item",
                    'taxId' => 'S',
                    'taxPercentage' => '15.00',
                    'taxScheme' => 'VAT',
                    'priceAmount' => '10.00',
                    'allowanceChargeReason' => 'discount',
                    'allowanceChargeAmount' => '0.00'
                ]
            ]
        ];

        if (in_array($documentType, ['credit_note', 'debit_note'])) {
            $basePayload['paymentMeansCode'] = $documentType === 'credit_note' ? '31' : '32';
            $basePayload['billingReference'] = [
                'invoiceDocumentReference' => [
                    'id' => 'INV-' . random_int(1000, 9999),
                    'issueDate' => date('Y-m-d', strtotime('-1 day'))
                ]
            ];
            $basePayload['creditDebitReason'] = $documentType === 'credit_note'
                ? 'Product return'
                : 'Additional charges';
            $basePayload['instructionNote'] = $documentType === 'credit_note'
                ? 'Credit note issued for returned goods'
                : 'Debit note issued for additional charges';
        }

        return $basePayload;
    }

    /**
     * Generate UUID
     */
    private function generateUuid(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * Generate CSID for specific environment
     */
    public function generateCsid(Request $request, int $locationId, string $environment): JsonResponse
    {
        try {
            $this->validateEnvironment($environment);
            $this->validateOtpInput($request);

            $location = $this->getAuthenticatedLocation($request, $locationId);
            $otp = $request->input('otp');

            $result = $this->generateCsidIfNeeded($location, $environment, $otp);

            return response()->json([
                'status' => 'success',
                'message' => $result
            ]);

        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }




    /**
     * Generate X509 certificate for specific environment
     */
    public function generateX509(Request $request, int $locationId, string $environment): JsonResponse
    {
        try {
            $this->validateEnvironment($environment);
            $location = $this->getAuthenticatedLocation($request, $locationId);

            $result = $this->generateX509IfNeeded($location, $environment);

            return response()->json([
                'status' => 'success',
                'message' => $result,
                'environment' => $environment,
                'location_id' => $locationId
            ]);

        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

}
