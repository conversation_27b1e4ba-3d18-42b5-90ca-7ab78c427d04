<?php

/*
ZatcaNext - ZATCA e-Invoicing Middleware for Phase 2 Compliance

Copyright (c) 2025 ZatcaNext. All rights reserved.

This software is the proprietary property of ZatcaNext and is protected
by copyright and other intellectual property laws. Unauthorized use,
reproduction, modification, distribution, or reverse-engineering of this
software, in whole or in part, is strictly prohibited without express
written permission from ZatcaNext.

This software is provided "as is" without warranties of any kind, express
or implied. ZatcaNext disclaims all liability for damages arising from its use.

For support or licensing inquiries, contact: <EMAIL>
Website: https://zatcanext.com
Terms of Use: https://zatcanext.com/terms
*/

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use JsonException;

/**
 * Authentication Controller
 */
class AuthController extends Controller
{
    /**
     * @OA\Post(
     *     path="/api/token",
     *     summary="Get JWT Token",
     *     tags={"🔐 Authentication"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"username", "password"},
     *             @OA\Property(property="username", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", example="password")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Authentication successful",
     *         @OA\JsonContent(
     *             @OA\Property(property="access", type="string"),
     *             @OA\Property(property="refresh", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Authentication failed"
     *     )
     * )
     * @throws JsonException
     */
    public function login(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'detail' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = User::where('email', $request->username)
                   ->orWhere('name', $request->username)
                   ->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json([
                'detail' => 'No active account found with the given credentials',
            ], 401);
        }

        $payload = [
            'token_type' => 'access',
            'exp' => time() + 3600,
            'iat' => time(),
            'jti' => Str::random(32),
            'user_id' => $user->id
        ];

        // Generate a signed token
        $accessToken = $this->generateSignedToken($payload);

        $refreshPayload = [
            'token_type' => 'refresh',
            'exp' => time() + (7 * 24 * 3600), // 7 days
            'iat' => time(),
            'jti' => Str::random(32),
            'user_id' => $user->id
        ];

        $refreshToken = $this->generateSignedToken($refreshPayload);

        return response()->json([
            'access' => $accessToken,
            'refresh' => $refreshToken,
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/token/refresh",
     *     summary="Refresh JWT Token",
     *     tags={"🔐 Authentication"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"refresh"},
     *             @OA\Property(property="refresh", type="string", example="refresh_token_here")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Token refreshed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="access", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Invalid refresh token"
     *     )
     * )
     * @throws JsonException
     */
    public function refresh(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'refresh' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'detail' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $refreshToken = $request->refresh;
        $userId = $this->validateRefreshToken($refreshToken);

        if (!$userId) {
            return response()->json([
                'detail' => 'Invalid or expired refresh token',
            ], 401);
        }

        $user = User::find($userId);
        if (!$user) {
            return response()->json([
                'detail' => 'User not found',
            ], 401);
        }

        $payload = [
            'token_type' => 'access',
            'exp' => time() + 3600,
            'iat' => time(),
            'jti' => Str::random(32),
            'user_id' => $user->id
        ];

        $accessToken = $this->generateSignedToken($payload);

        return response()->json([
            'access' => $accessToken,
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/token/logout",
     *     summary="Logout and invalidate JWT Token",
     *     tags={"🔐 Authentication"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Successfully logged out",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Successfully logged out")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized"
     *     )
     * )
     */
    public function logout(Request $request): JsonResponse
    {

        return response()->json([
            'message' => 'Successfully logged out'
        ]);
    }

    /**
     * Generate a cryptographically signed token
     *
     * @param array $payload
     * @return string
     * @throws JsonException
     */
    private function generateSignedToken(array $payload): string
    {
        $payloadBase64 = base64_encode(json_encode($payload, JSON_THROW_ON_ERROR));
        $signature = hash_hmac('sha256', $payloadBase64, config('app.key'));
        return $payloadBase64 . '.' . $signature;
    }

    /**
     * Validate refresh token and return user ID if valid
     *
     * @param string $token
     * @return int|null User ID if token is valid, null otherwise
     */
    private function validateRefreshToken(string $token): ?int
    {
        try {

            $parts = explode('.', $token);
            if (count($parts) !== 2) {
                return null;
            }

            [$payloadBase64, $signature] = $parts;

            $expectedSignature = hash_hmac('sha256', $payloadBase64, config('app.key'));
            if (!hash_equals($expectedSignature, $signature)) {
                return null;
            }

            $decoded = base64_decode($payloadBase64);
            if (!$decoded) {
                return null;
            }

            $payload = json_decode($decoded, true, 512, JSON_THROW_ON_ERROR);
            if (!$payload ||
                !isset($payload['user_id']) ||
                !isset($payload['exp']) ||
                !isset($payload['token_type']) ||
                $payload['token_type'] !== 'refresh') {
                return null;
            }


            if ($payload['exp'] < time()) {
                return null;
            }

            return $payload['user_id'];

        } catch (\Exception $e) {
            Log::error('Refresh token validation error: ' . $e->getMessage());
            return null;
        }
    }
}
