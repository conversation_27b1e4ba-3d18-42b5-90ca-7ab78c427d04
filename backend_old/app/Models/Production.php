<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Production extends Model
{
    use HasFactory;

    protected $fillable = [
        'business_location_id',
        'private_key',
        'public_key',
        'csr',
        'csid',
        'csid_base64',
        'secret_csid',
        'csid_request',
        'x509_base64',
        'x509_certificate',
        'x509_secret',
        'x509_request',
    ];

    /**
     * Get the business location that owns the production.
     */
    public function businessLocation(): BelongsTo
    {
        return $this->belongsTo(BusinessLocation::class);
    }
}
