<?php

namespace App\Constants;

use InvalidArgumentException;

/**
 * ZATCA Constants - Consolidated
 * All ZATCA-related constants and configuration values
 * Consolidated from ZatcaConstants.php and ZatcaFatooraConstants.php
 */
class ZatcaConstants
{
    // ========================================
    // ENVIRONMENT CONSTANTS
    // ========================================

    public const string ENVIRONMENT_SANDBOX = 'sandbox';
    public const string ENVIRONMENT_SIMULATION = 'simulation';
    public const string ENVIRONMENT_PRODUCTION = 'production';

    public const array ENVIRONMENTS = [
        self::ENVIRONMENT_SANDBOX,
        self::ENVIRONMENT_SIMULATION,
        self::ENVIRONMENT_PRODUCTION
    ];

    // ========================================
    // ZATCA API ENDPOINTS
    // ========================================

    public const array ZATCA_CSID_URLS = [
        self::ENVIRONMENT_SANDBOX => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/compliance',
        self::ENVIRONMENT_SIMULATION => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/compliance',
        self::ENVIRONMENT_PRODUCTION => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/core/compliance',
    ];

    public const array ZATCA_X509_URLS = [
        self::ENVIRONMENT_SANDBOX => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/production/csids',
        self::ENVIRONMENT_SIMULATION => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/production/csids',
        self::ENVIRONMENT_PRODUCTION => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/core/production/csids',
    ];

    public const array ZATCA_COMPLIANCE_URLS = [
        self::ENVIRONMENT_SANDBOX => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/compliance/invoices',
        self::ENVIRONMENT_SIMULATION => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/compliance/invoices',
        self::ENVIRONMENT_PRODUCTION => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/core/compliance/invoices',
    ];

    public const array ZATCA_REPORTING_URLS = [
        self::ENVIRONMENT_SANDBOX => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/invoices/reporting/single',
        self::ENVIRONMENT_SIMULATION => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/invoices/reporting/single',
        self::ENVIRONMENT_PRODUCTION => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/core/invoices/reporting/single',
    ];

    public const array ZATCA_CLEARANCE_URLS = [
        self::ENVIRONMENT_SANDBOX => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal/invoices/clearance/single',
        self::ENVIRONMENT_SIMULATION => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/simulation/invoices/clearance/single',
        self::ENVIRONMENT_PRODUCTION => 'https://gw-fatoora.zatca.gov.sa/e-invoicing/core/invoices/clearance/single',
    ];

    // ========================================
    // ZATCA SDK PATHS
    // ========================================

    public const string ZATCA_TEMPLATE_PATH = '/var/www/html/storage/app/zatca-sdk/zatca-einvoicing-sdk-Java-238-R3.4.1/Data/Samples/Standard/Invoice/Standard_Invoice.xml';

    // ========================================
    // FATOORA SDK CONSTANTS
    // ========================================

    public const string FATOORA_HOME = '/var/www/html/storage/app/zatca-sdk/zatca-einvoicing-sdk-Java-238-R3.4.1/Apps';
    public const string SDK_CONFIG_PATH = '/var/www/html/storage/app/zatca-sdk/zatca-einvoicing-sdk-Java-238-R3.4.1/Configuration/config.json';
    public const string ZATCA_LIB_PATH = '/var/www/html/storage/app/zatca-sdk/zatca-einvoicing-sdk-Java-238-R3.4.1/Lib';

    // Output Parsing Patterns (exact working patterns from production)
    public const string HASH_PATTERN = '/\*\*\*\s*INVOICE\s*HASH\s*=\s*([A-Za-z0-9+\/=]+)/';
    public const string QR_PATTERN_XML = '/<cac:AdditionalDocumentReference>.*?<cbc:ID>QR<\/cbc:ID>.*?<cbc:EmbeddedDocumentBinaryObject[^>]*>([A-Za-z0-9+\/=\s]+)<\/cbc:EmbeddedDocumentBinaryObject>.*?<\/cac:AdditionalDocumentReference>/s';

    // ========================================
    // INVOICE TYPES & DOCUMENT TYPES
    // ========================================

    // Invoice Types
    public const string INVOICE_TYPE_STANDARD = 'standard';
    public const string INVOICE_TYPE_SIMPLIFIED = 'simplified';

    public const array INVOICE_TYPES = [
        self::INVOICE_TYPE_STANDARD,
        self::INVOICE_TYPE_SIMPLIFIED
    ];

    // Document Types
    public const string DOCUMENT_TYPE_INVOICE = 'invoice';
    public const string DOCUMENT_TYPE_CREDIT_NOTE = 'credit_note';
    public const string DOCUMENT_TYPE_DEBIT_NOTE = 'debit_note';

    public const array DOCUMENT_TYPES = [
        self::DOCUMENT_TYPE_INVOICE,
        self::DOCUMENT_TYPE_CREDIT_NOTE,
        self::DOCUMENT_TYPE_DEBIT_NOTE
    ];


    public const array INVOICE_TYPE_CODES = [
        'simplified' => [
            'invoice' => '388',
            'credit_note' => '383',
            'debit_note' => '381',
        ],
        'standard' => [
            'invoice' => '388',
            'credit_note' => '381',
            'debit_note' => '383',
        ]
    ];

    // Invoice Type Code Names (ZATCA specific)
    public const array INVOICE_TYPE_CODE_NAMES = [
        'simplified' => [
            'invoice' => '0200000',
            'credit_note' => '0211010',
            'debit_note' => '0211010',
        ],
        'standard' => [
            'invoice' => '0100000',
            'credit_note' => '0100000',
            'debit_note' => '0100000',
        ]
    ];

    // ========================================
    // PROFILE IDS & STATUS
    // ========================================

    // Profile IDs
    public const string PROFILE_REPORTING = 'reporting:1.0';
    public const string PROFILE_CLEARANCE = 'clearance:1.0';

    // Clearance Status
    public const string CLEARANCE_STATUS_CLEARED = 'CLEARED';
    public const string CLEARANCE_STATUS_REPORTED = 'REPORTED';

    // ========================================
    // CURRENCY & COUNTRY CODES
    // ========================================

    public const string CURRENCY_SAR = 'SAR';

    // ========================================
    // XML NAMESPACES
    // ========================================

    public const string NAMESPACE_CAC = 'urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2';
    public const string NAMESPACE_CBC = 'urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2';

    // ========================================
    // VALIDATION RULES
    // ========================================

    public const array REQUIRED_INVOICE_FIELDS = [
        'id',
        'uuid',
        'issueDate',
        'issueTime',
        'profileID',
        'documentCurrencyCode',
        'taxCurrencyCode',
        'additionalDocumentReference',
        'accountingSupplierParty',
        'accountingCustomerParty',
        'taxTotal',
        'legalMonetaryTotal',
        'invoiceLines'
    ];

    public const array REQUIRED_SUPPLIER_FIELDS = [
        'companyID',
        'partyName',
        'postalAddress',
        'partyTaxScheme'
    ];

    public const array REQUIRED_CUSTOMER_FIELDS = [
        'partyName',
        'postalAddress'
    ];

    public const array REQUIRED_TAX_TOTAL_FIELDS = [
        'taxAmount',
        'taxSubtotal'
    ];

    public const array REQUIRED_MONETARY_TOTAL_FIELDS = [
        'lineExtensionAmount',
        'taxExclusiveAmount',
        'taxInclusiveAmount',
        'payableAmount'
    ];

    public const array REQUIRED_INVOICE_LINE_FIELDS = [
        'id',
        'invoicedQuantity',
        'lineExtensionAmount',
        'item',
        'price'
    ];

    public const array VALIDATION_RULES = [
        'invoice' => 'required|array',
        'invoice.invoiceType' => 'required|string|in:' . self::INVOICE_TYPE_STANDARD . ',' . self::INVOICE_TYPE_SIMPLIFIED,
        'invoice.documentType' => 'required|string|in:' . self::DOCUMENT_TYPE_INVOICE . ',' . self::DOCUMENT_TYPE_DEBIT_NOTE . ',' . self::DOCUMENT_TYPE_CREDIT_NOTE,
        'profileID' => 'required|string',
        'id' => 'required|string',
        'uuid' => 'required|string',
    ];

    // ========================================
    // ERROR MESSAGES
    // ========================================

    public const string ERROR_INVALID_ENVIRONMENT = 'Invalid environment';
    public const string ERROR_TEMPLATE_NOT_FOUND = 'ZATCA template file not found';
    public const string ERROR_ACCOUNT_NOT_FOUND = 'account not found with current secret key';
    public const string ERROR_ENVIRONMENT_NOT_FOUND = 'Environment not found for this location';
    public const string ERROR_INVALID_JSON_BODY = 'Invalid Json Body Data';
    public const string ERROR_VALIDATION_FAILED = 'Validation failed';
    public const string ERROR_LOCATION_NOT_FOUND = 'Location not found';
    public const string ERROR_INVALID_DOCUMENT_TYPE = 'Invalid document type';

    public const string MESSAGE_SUCCESS = 'Success';
    public const string MESSAGE_FAIL = 'Fail';

    // ========================================
    // HTTP STATUS CODES
    // ========================================

    public const string HTTP_STATUS_SUCCESS = '200';
    public const string HTTP_STATUS_BAD_REQUEST = '400';

    // ========================================
    // HELPER METHODS
    // ========================================

    /**
     * Get ZATCA API URL for specific environment and endpoint type
     */
    public static function getApiUrl(string $environment, string $endpointType): string
    {
        $urls = match($endpointType) {
            'csid' => self::ZATCA_CSID_URLS,
            'x509' => self::ZATCA_X509_URLS,
            'compliance' => self::ZATCA_COMPLIANCE_URLS,
            'reporting' => self::ZATCA_REPORTING_URLS,
            'clearance' => self::ZATCA_CLEARANCE_URLS,
            default => throw new InvalidArgumentException("Invalid endpoint type: {$endpointType}")
        };

        return $urls[$environment] ?? throw new InvalidArgumentException("Invalid environment: {$environment}");
    }

    /**
     * Get invoice type code based on invoice type and document type
     */
    public static function getInvoiceTypeCode(string $invoiceType, string $documentType): string
    {
        return self::INVOICE_TYPE_CODES[$invoiceType][$documentType] ?? '388';
    }

    /**
     * Get invoice type code name based on invoice type and document type
     */
    public static function getInvoiceTypeCodeName(string $invoiceType, string $documentType): string
    {
        return self::INVOICE_TYPE_CODE_NAMES[$invoiceType][$documentType] ?? '0100000';
    }
    /**
     * Validate required fields in invoice data
     * @param array $invoiceData
     * @return array Array of validation errors (empty if valid)
     */
    public static function validateInvoiceData(array $invoiceData): array
    {
        $errors = [];

        foreach (self::REQUIRED_INVOICE_FIELDS as $field) {
            if (empty($invoiceData[$field])) {
                $errors[] = "Missing required field: $field";
            }
        }

        return $errors;
    }
}
