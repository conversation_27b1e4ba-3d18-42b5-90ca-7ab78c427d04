<?php

namespace App\Services;

use App\Constants\ZatcaConstants;
use Exception;
use RuntimeException;

class ZatcaXmlSigningService
{
    /**
     * Sign an XML document using ZATCA FATOORA command
     * Uses ZATCA SDK for document signing and QR code generation
     * Now properly uses private key and certificate
     */
    public function signXmlDocument(string $xmlContent, string $privateKey, string $certificate): array
    {
        try {

            if (!empty($privateKey) && !empty($certificate)) {
                return $this->signWithCertificates($xmlContent, $privateKey, $certificate);
            }

            return $this->signWithFatooraCommand($xmlContent);

        } catch (Exception $e) {
            throw new RuntimeException('ZATCA signing failed: ' . $e->getMessage());
        }
    }

    /**
     * Sign XML using certificates
     * This method mimics Django's sign_xml_document function
     * @throws Exception
     */
    private function signWithCertificates(string $xmlContent, string $privateKey, string $certificate): array
    {
        try {

            $tempDir = sys_get_temp_dir() . '/zatca_signing_' . uniqid('', true);
            if (!mkdir($tempDir, 0755, true) && !is_dir($tempDir)) {
                throw new RuntimeException(sprintf('Directory "%s" was not created', $tempDir));
            }

            $xmlFile = $tempDir . '/invoice.xml';
            $privateKeyFile = $tempDir . '/private.key';
            $certificateFile = $tempDir . '/certificate.pem';

            $decodedXml = base64_decode($xmlContent, true);
            if ($decodedXml !== false && $this->isValidXml($decodedXml)) {
                file_put_contents($xmlFile, $decodedXml);
            } else {
                file_put_contents($xmlFile, $xmlContent);
            }

            file_put_contents($privateKeyFile, $privateKey);
            file_put_contents($certificateFile, $certificate);

            $signedFile = $tempDir . '/invoice_signed.xml';

            putenv('SDK_CONFIG=' . ZatcaConstants::SDK_CONFIG_PATH);
            putenv('FATOORA_HOME=' . ZatcaConstants::FATOORA_HOME);

            $workingDir = ZatcaConstants::FATOORA_HOME;

            $command = sprintf(
                'cd %s && SDK_CONFIG=%s ./fatoora -sign -qr -invoice %s -signedInvoice %s',
                escapeshellarg($workingDir),
                escapeshellarg(ZatcaConstants::SDK_CONFIG_PATH),
                escapeshellarg($xmlFile),
                escapeshellarg($signedFile)
            );

            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            if ($returnCode !== 0) {
                throw new RuntimeException('ZATCA FATOORA command failed with return code ' . $returnCode . ': ' . implode("\n", $output));
            }

            if (!file_exists($signedFile)) {
                throw new RuntimeException('ZATCA FATOORA command did not create signed file. Output: ' . implode("\n", $output));
            }

            $invoiceHash = $this->extractInvoiceHashFromFatooraOutput($output);
            $signedXml = file_get_contents($signedFile);
            $qrCode = $this->extractQrCodeFromFatooraOutput($output, $signedXml);

            $this->cleanupTempDirectory($tempDir);

            if (empty($invoiceHash) || empty($qrCode)) {
                throw new RuntimeException('fatoora command did not generate proper hash or QR code');
            }

            return [
                'invoiceHash' => $invoiceHash,
                'invoiceXml' => $signedXml,
                'invoiceQRCode' => $qrCode
            ];

        } catch (Exception $e) {
            if (isset($tempDir)) {
                $this->cleanupTempDirectory($tempDir);
            }
            throw $e;
        }
    }

    /**
     * Sign XML using ZATCA FATOORA command
     * Executes the FATOORA SDK signing process
     * @throws Exception
     */
    private function signWithFatooraCommand(string $xmlContent): array
    {
        try {

            $tempDir = sys_get_temp_dir() . '/zatca_fatoora_' . uniqid('', true);
            if (!mkdir($tempDir, 0755, true) && !is_dir($tempDir)) {
                throw new RuntimeException(sprintf('Directory "%s" was not created', $tempDir));
            }

            $xmlFile = $tempDir . '/invoice.xml';

            $decodedXml = base64_decode($xmlContent, true);
            if ($decodedXml !== false && $this->isValidXml($decodedXml)) {
                file_put_contents($xmlFile, $decodedXml);
            } else {
                file_put_contents($xmlFile, $xmlContent);
            }

            $signedFile = $tempDir . '/invoice_signed.xml';

            putenv('SDK_CONFIG=' . ZatcaConstants::SDK_CONFIG_PATH);
            putenv('FATOORA_HOME=' . ZatcaConstants::FATOORA_HOME);

            $sdkErrors = $this->validateSdkInstallation();
            if (!empty($sdkErrors)) {
                throw new RuntimeException('SDK validation failed: ' . implode(', ', $sdkErrors));
            }

            $workingDir = ZatcaConstants::FATOORA_HOME;

            $command = sprintf(
                'cd %s && SDK_CONFIG=%s ./fatoora -sign -qr -invoice %s -signedInvoice %s',
                escapeshellarg($workingDir),
                escapeshellarg(ZatcaConstants::SDK_CONFIG_PATH),
                escapeshellarg($xmlFile),
                escapeshellarg($signedFile)
            );



            $output = [];
            $returnCode = 0;
            exec($command . ' 2>&1', $output, $returnCode);

            if ($returnCode !== 0) {
                throw new RuntimeException('ZATCA FATOORA command failed with return code ' . $returnCode . ': ' . implode("\n", $output));
            }

            if (!file_exists($signedFile)) {
                throw new RuntimeException('ZATCA FATOORA command did not create signed file. Output: ' . implode("\n", $output));
            }

            $invoiceHash = $this->extractInvoiceHashFromFatooraOutput($output);

            $signedXml = '';
            if (file_exists($signedFile)) {
                $signedXml = file_get_contents($signedFile);
            }

            $qrCode = $this->extractQrCodeFromFatooraOutput($output, $signedXml);

            $this->cleanupTempDirectory($tempDir);

            if (empty($invoiceHash) || empty($qrCode)) {
                throw new RuntimeException('fatoora command did not generate proper hash or QR code');
            }

            return [
                'invoiceHash' => $invoiceHash,
                'invoiceXml' => !empty($signedXml) ? $signedXml : $xmlContent,
                'invoiceQRCode' => $qrCode
            ];

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Extract invoice hash from fatoora output
     */
    private function extractInvoiceHashFromFatooraOutput(array $output): string
    {
        foreach ($output as $line) {
            if (preg_match(ZatcaConstants::HASH_PATTERN, $line, $matches)) {
                return trim($matches[1]);
            }
        }

        throw new RuntimeException('Invoice hash not found in fatoora output');
    }

    /**
     * Extract QR code from signed XML
     */
    private function extractQrCodeFromFatooraOutput(array $output, string $signedXml = ''): string
    {
        if (!empty($signedXml) && preg_match(ZatcaConstants::QR_PATTERN_XML, $signedXml, $matches)) {
            $qrCode = trim(preg_replace('/\s+/', '', $matches[1]));

            $decoded = base64_decode($qrCode);
            if ($decoded !== false && strlen($decoded) > 10) {
                $firstByte = ord($decoded[0]);
                if ($firstByte >= 1 && $firstByte <= 10) {
                    return $qrCode;
                }
            }
        }

        throw new RuntimeException('QR code not found in fatoora output or signed XML');
    }



    /**
     * Clean up temporary directory
     */
    private function cleanupTempDirectory(string $tempDir): void
    {
        if (is_dir($tempDir)) {
            $files = array_diff(scandir($tempDir), ['.', '..']);
            foreach ($files as $file) {
                unlink($tempDir . '/' . $file);
            }
            rmdir($tempDir);
        }
    }

    /**
     * Validate SDK installation
     */
    private function validateSdkInstallation(): array
    {
        $errors = [];

        if (!is_dir(ZatcaConstants::FATOORA_HOME)) {
            $errors[] = 'FATOORA_HOME not found: ' . ZatcaConstants::FATOORA_HOME;
        }

        if (!file_exists(ZatcaConstants::SDK_CONFIG_PATH)) {
            $errors[] = 'SDK config not found: ' . ZatcaConstants::SDK_CONFIG_PATH;
        }

        if (!is_dir(ZatcaConstants::ZATCA_LIB_PATH)) {
            $errors[] = 'ZATCA Lib path not found: ' . ZatcaConstants::ZATCA_LIB_PATH;
        }

        return $errors;
    }

    /**
     * Check if string is valid XML
     */
    private function isValidXml(string $xmlString): bool
    {
        $previousUseErrors = libxml_use_internal_errors(true);
        libxml_clear_errors();

        $doc = simplexml_load_string($xmlString);
        $errors = libxml_get_errors();

        libxml_use_internal_errors($previousUseErrors);

        return $doc !== false && empty($errors);
    }
}
