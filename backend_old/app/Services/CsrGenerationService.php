<?php

namespace App\Services;

use Exception;
use Random\RandomException;
use RuntimeException;

/**
 * CSR Generation Service
 *
 * Generates Certificate Signing Requests (CSR) for ZATCA compliance

 */
class CsrGenerationService
{
    /**
     * Generate CSR data for a business location
     *
     * @param array $locationData
     * @param string $type 'ZATCA-Code-Signing'
     * @return array
     * @throws Exception
     */
    public function generateCsr(array $locationData, string $type = 'ZATCA-Code-Signing'): array
    {
        try {

            $privateKey = $this->generatePrivateKey();

            $publicKey = $this->generatePublicKey($privateKey);

            $csr = $this->generateCertificateSigningRequest($locationData, $privateKey, $type);

            return [
                'pvt' => $privateKey,
                'pbl' => $publicKey,
                'csr' => $csr
            ];

        } catch (Exception $e) {
            throw new RuntimeException('Failed to generate CSR: ' . $e->getMessage());
        }
    }

    /**
     * Generate a private key using OpenSSL
     *
     * @return string
     * @throws Exception
     */
    private function generatePrivateKey(): string
    {
        $tempFile = tempnam(sys_get_temp_dir(), 'zatca_private_key');

        $command = "openssl ecparam -name secp256k1 -genkey -noout -out {$tempFile} 2>&1";
        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            throw new Exception('Failed to generate EC private key: ' . implode("\n", $output));
        }

        $privateKey = file_get_contents($tempFile);
        unlink($tempFile);

        return $privateKey;
    }

    /**
     * Generate public key from a private key
     *
     * @param string $privateKey
     * @return string
     * @throws Exception
     */
    private function generatePublicKey(string $privateKey): string
    {
        $tempPrivateFile = tempnam(sys_get_temp_dir(), 'zatca_private');
        $tempPublicFile = tempnam(sys_get_temp_dir(), 'zatca_public');

        file_put_contents($tempPrivateFile, $privateKey);

        $command = "openssl ec -in {$tempPrivateFile} -pubout -conv_form compressed -out {$tempPublicFile} 2>&1";
        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            unlink($tempPrivateFile);
            throw new RuntimeException('Failed to generate EC public key: ' . implode("\n", $output));
        }

        $publicKey = file_get_contents($tempPublicFile);

        unlink($tempPrivateFile);
        unlink($tempPublicFile);

        return $publicKey;
    }

    /**
     * Generate Certificate Signing Request
     *
     * @param array $locationData
     * @param string $privateKey
     * @param string $type
     * @return string
     * @throws Exception
     */
    private function generateCertificateSigningRequest(array $locationData, string $privateKey, string $type): string
    {
        $tempPrivateFile = tempnam(sys_get_temp_dir(), 'zatca_private');
        $tempCsrFile = tempnam(sys_get_temp_dir(), 'zatca_csr');
        $tempConfigFile = tempnam(sys_get_temp_dir(), 'zatca_config');

        file_put_contents($tempPrivateFile, $privateKey);

        $config = $this->generateOpenSslConfig($locationData, $type);
        file_put_contents($tempConfigFile, $config);

        $command = "openssl req -new -sha256 -key {$tempPrivateFile} -extensions v3_req -config {$tempConfigFile} -out {$tempCsrFile} 2>&1";
        exec($command, $output, $returnCode);

        if ($returnCode !== 0) {
            unlink($tempPrivateFile);
            unlink($tempConfigFile);
            throw new RuntimeException('Failed to generate CSR: ' . implode("\n", $output));
        }

        $csr = file_get_contents($tempCsrFile);

        $csrBase64 = base64_encode($csr);

        unlink($tempPrivateFile);
        unlink($tempConfigFile);
        unlink($tempCsrFile);

        return $csrBase64;
    }

    /**
     * Generate OpenSSL configuration for CSR
     *
     * @param array $locationData
     * @param string $type
     * @return string
     */
    private function generateOpenSslConfig(array $locationData, string $type): string
    {
        return "oid_section = my_oids
[my_oids]
tsa_policy1 =*******.4.1.311.20.2

[ req ]
default_bits	=     2048
emailAddress 	=     <EMAIL>
req_extensions    =   v3_req
x509_extensions	=     v3_ca
prompt =     no
default_md        =     sha256
req_extensions    =     req_ext
distinguished_name = dn

[ dn ]
C=SA
O={$locationData['organisation']}
OU={$locationData['organisation_unit']}
CN={$locationData['common_name']}

[ req_ext ]

*******.4.1.311.20.2 =ASN1:PRINTABLESTRING:{$type}
subjectAltName = dirName:alt_names

[ v3_req ]

# Extensions to add to a certificate request

basicConstraints = CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment

[ alt_names ]
SN={$locationData['serial_number']}
UID={$locationData['tax_no']}
title={$locationData['title']}
registeredAddress={$locationData['registered_address']}
businessCategory={$locationData['business_category']}
";
    }

    /**
     * Generate random hex key
     *
     * @param int $length
     * @return string
     * @throws RandomException
     */
    public function generateRandomKey(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }
}
