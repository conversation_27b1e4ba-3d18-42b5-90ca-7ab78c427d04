<?php

namespace App\Services;

use App\Constants\ZatcaConstants;
use Exception;
use RuntimeException;

/**
 * ZATCA Validation Service
 * Centralized validation for all ZATCA-related data
 * Eliminates fallback values and ensures data integrity
 */
class ZatcaValidationService
{
    /**
     * Validate complete invoice data structure
     * @throws Exception
     */
    public function validateInvoiceData(array $invoiceData): void
    {
        $this->validateRequiredFields($invoiceData, ZatcaConstants::REQUIRED_INVOICE_FIELDS);

        $this->validateSupplierData($invoiceData['accountingSupplierParty']);
        $this->validateCustomerData($invoiceData['accountingCustomerParty']);
        $this->validateTaxTotal($invoiceData['taxTotal']);
        $this->validateMonetaryTotal($invoiceData['legalMonetaryTotal']);
        $this->validateInvoiceLines($invoiceData['invoiceLines']);
        $this->validateAdditionalDocumentReference($invoiceData['additionalDocumentReference']);
    }

    /**
     * Validate supplier data
     * @throws Exception
     */
    public function validateSupplierData(array $supplierData): void
    {
        $this->validateRequiredFields($supplierData, ZatcaConstants::REQUIRED_SUPPLIER_FIELDS);

        if (!preg_match('/^\d{15}$/', $supplierData['companyID'])) {
            throw new RuntimeException('Invalid VAT number format. Must be 15 digits.');
        }
    }

    /**
     * Validate customer data
     * @throws Exception
     */
    public function validateCustomerData(array $customerData): void
    {
        $this->validateRequiredFields($customerData, ZatcaConstants::REQUIRED_CUSTOMER_FIELDS);
    }

    /**
     * Validate tax total data
     * @throws Exception
     */
    public function validateTaxTotal(array $taxTotal): void
    {
        $this->validateRequiredFields($taxTotal, ZatcaConstants::REQUIRED_TAX_TOTAL_FIELDS);

        // Validate tax amount is numeric
        if (!is_numeric($taxTotal['taxAmount'])) {
            throw new RuntimeException('Tax amount must be numeric');
        }
    }

    /**
     * Validate monetary total data
     * @throws Exception
     */
    public function validateMonetaryTotal(array $monetaryTotal): void
    {
        $this->validateRequiredFields($monetaryTotal, ZatcaConstants::REQUIRED_MONETARY_TOTAL_FIELDS);

        // Validate all amounts are numeric
        foreach (ZatcaConstants::REQUIRED_MONETARY_TOTAL_FIELDS as $field) {
            if (!is_numeric($monetaryTotal[$field])) {
                throw new RuntimeException("$field must be numeric");
            }
        }

        // Validate calculation logic
        $lineExtension = (float)$monetaryTotal['lineExtensionAmount'];
        $taxExclusive = (float)$monetaryTotal['taxExclusiveAmount'];
        $taxInclusive = (float)$monetaryTotal['taxInclusiveAmount'];
        $payable = (float)$monetaryTotal['payableAmount'];

        if (abs($lineExtension - $taxExclusive) > 0.01) {
            throw new RuntimeException('Line extension amount must equal tax exclusive amount');
        }

        if (abs($taxInclusive - $payable) > 0.01) {
            throw new RuntimeException('Tax inclusive amount must equal payable amount');
        }
    }

    /**
     * Validate invoice lines
     * @throws Exception
     */
    public function validateInvoiceLines(array $invoiceLines): void
    {
        if (empty($invoiceLines)) {
            throw new RuntimeException('At least one invoice line is required');
        }

        foreach ($invoiceLines as $index => $line) {
            $this->validateInvoiceLine($line, $index + 1);
        }
    }

    /**
     * Validate single invoice line
     * @throws Exception
     */
    public function validateInvoiceLine(array $line, int $lineNumber): void
    {
        $this->validateRequiredFields($line, ZatcaConstants::REQUIRED_INVOICE_LINE_FIELDS, "Invoice line $lineNumber");

        if (!is_numeric($line['invoicedQuantity'])) {
            throw new RuntimeException("Invoice line $lineNumber: invoicedQuantity must be numeric");
        }

        if (!is_numeric($line['lineExtensionAmount'])) {
            throw new RuntimeException("Invoice line $lineNumber: lineExtensionAmount must be numeric");
        }

        if (!is_numeric($line['priceAmount'])) {
            throw new RuntimeException("Invoice line $lineNumber: priceAmount must be numeric");
        }

        $quantity = (float)$line['invoicedQuantity'];
        $price = (float)$line['priceAmount'];
        $lineExtension = (float)$line['lineExtensionAmount'];

        $calculatedExtension = $quantity * $price;
        if (abs($calculatedExtension - $lineExtension) > 0.01) {
            throw new RuntimeException("Invoice line $lineNumber: line extension amount calculation error. Expected: $calculatedExtension, Got: $lineExtension");
        }
    }

    /**
     * Validate additional document reference
     * @throws Exception
     */
    public function validateAdditionalDocumentReference(array $docRef): void
    {
        $requiredFields = ['uuid', 'pih'];
        $this->validateRequiredFields($docRef, $requiredFields, 'Additional document reference');

        if (!preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $docRef['uuid'])) {
            throw new RuntimeException('Invalid UUID format in additional document reference');
        }

        if (!base64_decode($docRef['pih'], true)) {
            throw new RuntimeException('PIH must be valid base64 encoded string');
        }
    }

    /**
     * Validate date and time formats
     */
    public function validateDateTime(string $date, string $time): void
    {

        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $date)) {
            throw new RuntimeException('Invalid date format. Expected: YYYY-MM-DD');
        }

        if (!preg_match('/^\d{2}:\d{2}:\d{2}$/', $time)) {
            throw new RuntimeException('Invalid time format. Expected: HH:MM:SS');
        }

        $dateTime = \DateTime::createFromFormat('Y-m-d H:i:s', $date . ' ' . $time);
        if (!$dateTime || $dateTime->format('Y-m-d H:i:s') !== $date . ' ' . $time) {
            throw new RuntimeException('Invalid date/time values');
        }
    }

    /**
     * Validate invoice type and document type
     */
    public function validateInvoiceAndDocumentType(string $invoiceType, string $documentType): void
    {
        $validInvoiceTypes = [ZatcaConstants::INVOICE_TYPE_STANDARD, ZatcaConstants::INVOICE_TYPE_SIMPLIFIED];
        if (!in_array($invoiceType, $validInvoiceTypes, true)) {
            throw new RuntimeException('Invalid invoice type. Must be: ' . implode(', ', $validInvoiceTypes));
        }

        $validDocumentTypes = [
            ZatcaConstants::DOCUMENT_TYPE_INVOICE,
            ZatcaConstants::DOCUMENT_TYPE_CREDIT_NOTE,
            ZatcaConstants::DOCUMENT_TYPE_DEBIT_NOTE
        ];
        if (!in_array($documentType, $validDocumentTypes, true)) {
            throw new RuntimeException('Invalid document type. Must be: ' . implode(', ', $validDocumentTypes));
        }
    }

    /**
     * Validate currency code
     */
    public function validateCurrency(string $currency): void
    {
        if ($currency !== ZatcaConstants::CURRENCY_SAR) {
            throw new RuntimeException('Invalid currency. Only SAR is supported for ZATCA e-invoicing');
        }
    }

    /**
     * Generic required fields validation
     */
    private function validateRequiredFields(array $data, array $requiredFields, string $context = 'Data'): void
    {
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || $data[$field] === '') {
                throw new RuntimeException("$context: Required field '$field' is missing or empty");
            }
        }
    }

    /**
     * Validate QR code generation data
     */
    public function validateQrCodeData(array $invoiceData, string $invoiceHash): void
    {
        $requiredFields = [
            'accountingSupplierParty.registrationName',
            'accountingSupplierParty.companyID',
            'issueDate',
            'issueTime',
            'legalMonetaryTotal.taxInclusiveAmount',
            'taxTotal.taxAmount'
        ];

        foreach ($requiredFields as $field) {
            $keys = explode('.', $field);
            $value = $invoiceData;
            foreach ($keys as $key) {
                if (!isset($value[$key])) {
                    throw new RuntimeException("Required field missing for QR code generation: $field");
                }
                $value = $value[$key];
            }
        }

        if (empty($invoiceHash)) {
            throw new RuntimeException('Invoice hash is required for QR code generation');
        }

        if (!base64_decode($invoiceHash, true)) {
            throw new RuntimeException('Invoice hash must be valid base64 encoded string');
        }
    }

    /**
     * Validate ZATCA submission data
     */
    public function validateSubmissionData(array $data): void
    {
        $requiredFields = ['signedInvoiceXml', 'invoiceHash', 'uuid'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                throw new RuntimeException("Required field missing for ZATCA submission: $field");
            }
        }

        if (!preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $data['uuid'])) {
            throw new RuntimeException('Invalid UUID format for ZATCA submission');
        }

        if (strlen($data['signedInvoiceXml']) < 100) {
            throw new RuntimeException('Signed invoice XML appears to be invalid or too short');
        }
    }
}
