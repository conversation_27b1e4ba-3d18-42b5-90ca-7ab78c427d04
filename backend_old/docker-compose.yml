version: '3.8'

services:
  # Laravel Application with Apache (NO CACHE PLUGINS)
  laravel-app:
    build:
      context: .
      dockerfile: docker/Dockerfile
    container_name: laravel-zatca-app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      - .:/var/www/html
      - ./apache-config/000-default.conf:/etc/apache2/sites-available/000-default.conf
      - ./apache-config/apache2.conf:/etc/apache2/apache2.conf
      - ./storage/logs:/var/www/html/storage/logs
    ports:
      - "8080:80"
    depends_on:
      - mariadb
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=mariadb
      - DB_PORT=3306
      - DB_DATABASE=zatca_einvoicing
      - DB_USERNAME=zatca_user
      - DB_PASSWORD=zatca_password
      - CACHE_DRIVER=array
      - ZATCA_SANDBOX_MODE=true
      - ZATCA_API_URL=https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal
    networks:
      - zatca-network

  # MariaDB Database
  mariadb:
    image: mariadb:11.7
    container_name: laravel-zatca-mariadb
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: zatca_einvoicing
      MYSQL_USER: zatca_user
      MYSQL_PASSWORD: zatca_password
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - zatca-network

  # phpMyAdmin for database management
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: laravel-zatca-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mariadb
      PMA_PORT: 3306
      PMA_USER: zatca_user
      PMA_PASSWORD: zatca_password
      MYSQL_ROOT_PASSWORD: root_password
    ports:
      - "8082:80"
    depends_on:
      - mariadb
    networks:
      - zatca-network

volumes:
  mariadb_data:
    driver: local

networks:
  zatca-network:
    driver: bridge
