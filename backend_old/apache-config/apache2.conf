# Global Apache configuration for ZATCA e-invoicing

# Server root
ServerRoot /etc/apache2

# PID file
PidFile ${APACHE_PID_FILE}

# Timeout and KeepAlive settings
Timeout 300
KeepAlive On
MaxKeepAliveRequests 100
KeepAliveTimeout 5

# User and Group
User www-data
Group www-data

# Server tokens (security)
ServerTokens Prod
ServerSignature Off

# Host name lookups
HostnameLookups Off

# Error and access logs
ErrorLog ${APACHE_LOG_DIR}/error.log
LogLevel warn

# Include module configuration
IncludeOptional mods-enabled/*.load
IncludeOptional mods-enabled/*.conf

# Include list of ports to listen on
Include ports.conf

# Directory permissions
<Directory />
    Options FollowSymLinks
    AllowOverride None
    Require all denied
</Directory>

<Directory /usr/share>
    AllowOverride None
    Require all granted
</Directory>

<Directory /var/www/>
    Options Indexes FollowSymLinks
    AllowOverride All
    Require all granted
</Directory>

# Default file types
<FilesMatch "^\.ht">
    Require all denied
</FilesMatch>

# Log format
LogFormat "%v:%p %h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\"" vhost_combined
LogFormat "%h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\"" combined
LogFormat "%h %l %u %t \"%r\" %>s %O" common
LogFormat "%{Referer}i -> %U" referer
LogFormat "%{User-agent}i" agent

# Include generic snippets of statements
IncludeOptional conf-enabled/*.conf

# Include the virtual host configurations
IncludeOptional sites-enabled/*.conf

# Security configurations
<Directory /var/www/html/storage>
    Require all denied
</Directory>

<Directory /var/www/html/bootstrap/cache>
    Require all denied
</Directory>

# Hide sensitive files
<Files ".env">
    Require all denied
</Files>

<Files "composer.json">
    Require all denied
</Files>

<Files "composer.lock">
    Require all denied
</Files>

<Files "package.json">
    Require all denied
</Files>

# Disable server info
<Location "/server-status">
    Require all denied
</Location>

<Location "/server-info">
    Require all denied
</Location>
