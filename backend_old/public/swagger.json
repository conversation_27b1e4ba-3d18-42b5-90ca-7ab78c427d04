{"openapi": "3.0.0", "info": {"title": "ZATCA Laravel API", "description": "Laravel ZATCA E-Invoicing API with Django compatibility", "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080/api", "description": "Local Development Server"}], "paths": {"/token": {"post": {"tags": ["Authentication"], "summary": "Get JWT Token", "operationId": "00f16ab1099e66724fbe87c95df485cc", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["username", "password"], "properties": {"username": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "example": "password"}}, "type": "object"}}}}, "responses": {"200": {"description": "Authentication successful", "content": {"application/json": {"schema": {"properties": {"access": {"type": "string"}, "refresh": {"type": "string"}}, "type": "object"}}}}, "401": {"description": "Authentication failed"}}}}, "/company": {"get": {"tags": ["Company Management"], "summary": "List all companies", "operationId": "b6c0dd933a081555a160a30a0b878d59", "responses": {"200": {"description": "List of companies", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "string"}, "name": {"type": "string"}, "slug": {"type": "string"}, "api_key": {"type": "string"}}, "type": "object"}}}}}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["Company Management"], "summary": "Create new company", "description": "Create a new company with auto-generated UUID and API key", "operationId": "ac0540bd05d26472283ac368a6cf6e6e", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["name"], "properties": {"name": {"description": "Company name (max 100 characters)", "type": "string", "maxLength": 100, "example": "My Test Company"}}, "type": "object"}}}}, "responses": {"201": {"description": "Company created successfully", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string", "format": "uuid", "example": "*************-4b3d-b3eb-59733229ee6f"}, "name": {"type": "string", "example": "My Test Company"}, "slug": {"type": "string", "format": "uuid", "example": "9b9a8fc3-567d-41cb-a919-8626c5cdf5da"}, "api_key": {"type": "string", "example": "CIcJrc9frcWnqLzb1ojlopT0jtxJvbc7"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Validation failed"}, "errors": {"type": "object", "example": {"name": ["This field is required."]}}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Authentication credentials were not provided."}}, "type": "object"}}}}}, "security": [{"bearerAuth": []}]}}, "/company/{slug}": {"get": {"tags": ["Company Management"], "summary": "Get company by slug", "description": "Retrieve a specific company by its slug using API key authentication", "operationId": "80c49d4c71dc547152dbe85e3578f35f", "parameters": [{"name": "slug", "in": "path", "description": "Company slug (UUID)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "9b9a8fc3-567d-41cb-a919-8626c5cdf5da"}}], "responses": {"200": {"description": "Company details", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string", "format": "uuid", "example": "*************-4b3d-b3eb-59733229ee6f"}, "name": {"type": "string", "example": "My Test Company"}, "slug": {"type": "string", "format": "uuid", "example": "9b9a8fc3-567d-41cb-a919-8626c5cdf5da"}, "api_key": {"type": "string", "example": "CIcJrc9frcWnqLzb1ojlopT0jtxJvbc7"}}, "type": "object"}}}}, "404": {"description": "Company not found", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Not found."}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Invalid API key or company not found."}}, "type": "object"}}}}}, "security": [{"apiKeyAuth": []}]}, "put": {"tags": ["Company Management"], "summary": "Update company", "description": "Update a company's information using API key authentication", "operationId": "9b74bcb2fe67820a15e5afffab6dbb8e", "parameters": [{"name": "slug", "in": "path", "description": "Company slug (UUID)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "9b9a8fc3-567d-41cb-a919-8626c5cdf5da"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"name": {"description": "Updated company name", "type": "string", "maxLength": 100, "example": "Updated Company Name"}}, "type": "object"}}}}, "responses": {"200": {"description": "Company updated successfully", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string", "format": "uuid", "example": "*************-4b3d-b3eb-59733229ee6f"}, "name": {"type": "string", "example": "Updated Company Name"}, "slug": {"type": "string", "format": "uuid", "example": "9b9a8fc3-567d-41cb-a919-8626c5cdf5da"}, "api_key": {"type": "string", "example": "CIcJrc9frcWnqLzb1ojlopT0jtxJvbc7"}}, "type": "object"}}}}, "404": {"description": "Company not found", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Not found."}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Validation failed"}, "errors": {"type": "object", "example": {"name": ["This field is required."]}}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Invalid API key or company not found."}}, "type": "object"}}}}}, "security": [{"apiKeyAuth": []}]}, "delete": {"tags": ["Company Management"], "summary": "Delete company", "description": "Delete a company using API key authentication", "operationId": "4a02f8d08cd1c8411d69203dbd8484b9", "parameters": [{"name": "slug", "in": "path", "description": "Company slug (UUID)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "9b9a8fc3-567d-41cb-a919-8626c5cdf5da"}}], "responses": {"200": {"description": "Company deleted successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Company deleted successfully"}}, "type": "object"}}}}, "404": {"description": "Company not found", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Not found."}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Invalid API key or company not found."}}, "type": "object"}}}}}, "security": [{"apiKeyAuth": []}]}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT Bearer token for company creation and listing"}, "apiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "Company API Key authentication (format: Api-Key {key})", "name": "Authorization", "in": "header"}}}, "tags": [{"name": "Authentication", "description": "JWT token management"}, {"name": "Company Management", "description": "Company CRUD operations"}]}