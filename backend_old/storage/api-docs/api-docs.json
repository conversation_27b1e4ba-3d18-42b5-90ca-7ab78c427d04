{"openapi": "3.0.0", "info": {"title": "Laravel ZATCA API", "description": "Comprehensive Laravel ZATCA API with Django compatibility - Authentication + Company Management + Location Management + ZATCA Compliance", "contact": {"email": "<EMAIL>"}, "version": "1.0.0"}, "servers": [{"url": "http://localhost:8080", "description": "Laravel ZATCA Development Server"}], "paths": {"/api/token": {"post": {"tags": ["🔐 Authentication"], "summary": "Get JWT Token", "operationId": "de03153e44232da8da3b14cdce63c8cf", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["username", "password"], "properties": {"username": {"type": "string", "format": "email", "example": "<EMAIL>"}, "password": {"type": "string", "example": "password"}}, "type": "object"}}}}, "responses": {"200": {"description": "Authentication successful", "content": {"application/json": {"schema": {"properties": {"access": {"type": "string"}, "refresh": {"type": "string"}}, "type": "object"}}}}, "401": {"description": "Authentication failed"}}}}, "/api/token/refresh": {"post": {"tags": ["🔐 Authentication"], "summary": "Refresh JWT Token", "operationId": "be9d3bbb7d1ab304fcf53f017332f80d", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["refresh"], "properties": {"refresh": {"type": "string", "example": "refresh_token_here"}}, "type": "object"}}}}, "responses": {"200": {"description": "<PERSON><PERSON> refreshed successfully", "content": {"application/json": {"schema": {"properties": {"access": {"type": "string"}}, "type": "object"}}}}, "401": {"description": "Invalid refresh token"}}}}, "/api/token/logout": {"post": {"tags": ["🔐 Authentication"], "summary": "Logout and invalidate JWT Token", "operationId": "45593530d1e6bb4a94b57a06f79938c2", "responses": {"200": {"description": "Successfully logged out", "content": {"application/json": {"schema": {"properties": {"message": {"type": "string", "example": "Successfully logged out"}}, "type": "object"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"bearerAuth": []}]}}, "/api/locations": {"get": {"tags": ["Location Management"], "summary": "List all business locations", "description": "Get all business locations for the authenticated company", "operationId": "c43786b561c86ecd8f90a4547d25781b", "responses": {"200": {"description": "List of business locations", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "integer", "example": 1}, "authentication_token": {"type": "string", "example": "auth_token_123"}, "seller_name": {"type": "string", "example": "Safa cold store for foodstuff Co."}, "tax_no": {"type": "string", "example": "311253816600003"}, "common_name": {"type": "string", "example": "Safa Cold Store"}, "organisation": {"type": "string", "example": "Safa cold store for foodstuff Co."}, "organisation_unit": {"type": "string", "example": "Store"}, "serial_number": {"type": "string", "example": "1-nomizo.com|2-version 2.0|3-ed22f1d8-e6a2-1118-9b58-d9a8f11e445f"}, "title": {"type": "string", "example": "1100"}, "registered_address": {"type": "string", "example": "<PERSON><PERSON> 2682 , <PERSON> 7864 , <PERSON><PERSON><PERSON>"}, "business_category": {"type": "string", "example": "Store"}}, "type": "object"}}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Authentication credentials were not provided."}}, "type": "object"}}}}}, "security": [{"ApiKeyAuth": []}]}, "post": {"tags": ["Location Management"], "summary": "Create new business location", "description": "Create a new business location for the authenticated company", "operationId": "71ec83c8b4f2eeb74176ab3c0396e3ba", "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"seller_name": {"description": "Seller name", "type": "string", "example": "Safa cold store for foodstuff Co."}, "tax_no": {"description": "Tax number", "type": "string", "example": "311253816600003"}, "organisation": {"description": "Organisation name", "type": "string", "example": "Safa cold store for foodstuff Co."}, "serial_number": {"description": "Serial number", "type": "string", "example": "1-nomizo.com|2-version 2.0|3-ed22f1d8-e6a2-1118-9b58-d9a8f11e445f"}, "organisation_unit": {"description": "Organisation unit", "type": "string", "example": "Store"}, "registered_address": {"description": "Registered address", "type": "string", "example": "<PERSON><PERSON> 2682 , <PERSON> 7864 , <PERSON><PERSON><PERSON>"}, "business_category": {"description": "Business category", "type": "string", "example": "Store"}, "title": {"description": "Title", "type": "string", "example": "1100"}, "common_name": {"description": "Common name", "type": "string", "example": "Safa Cold Store"}}, "type": "object"}}}}, "responses": {"201": {"description": "Location created successfully", "content": {"application/json": {"schema": {"properties": {"id": {"type": "integer", "example": 1}, "authentication_token": {"type": "string", "example": "auth_token_123"}, "seller_name": {"type": "string", "example": "Safa cold store for foodstuff Co."}, "tax_no": {"type": "string", "example": "311253816600003"}, "common_name": {"type": "string", "example": "Safa Cold Store"}, "organisation": {"type": "string", "example": "Safa cold store for foodstuff Co."}, "organisation_unit": {"type": "string", "example": "Store"}, "serial_number": {"type": "string", "example": "1-nomizo.com|2-version 2.0|3-ed22f1d8-e6a2-1118-9b58-d9a8f11e445f"}, "title": {"type": "string", "example": "1100"}, "registered_address": {"type": "string", "example": "<PERSON><PERSON> 2682 , <PERSON> 7864 , <PERSON><PERSON><PERSON>"}, "business_category": {"type": "string", "example": "Store"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Validation failed"}, "errors": {"type": "object"}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Authentication credentials were not provided."}}, "type": "object"}}}}}, "security": [{"ApiKeyAuth": []}]}}, "/api/company": {"get": {"tags": ["Company Management"], "summary": "List all companies", "operationId": "08c6be3a956569c8ed98393de93653c9", "responses": {"200": {"description": "List of companies", "content": {"application/json": {"schema": {"type": "array", "items": {"properties": {"id": {"type": "string"}, "name": {"type": "string"}, "slug": {"type": "string"}, "api_key": {"type": "string"}}, "type": "object"}}}}}}, "security": [{"BearerAuth": []}]}, "post": {"tags": ["Company Management"], "summary": "Create new company", "description": "Create a new company with auto-generated UUID and API key", "operationId": "ac33eaa8422bb09ea6dfe7fe24255ed0", "requestBody": {"required": true, "content": {"application/json": {"schema": {"required": ["name"], "properties": {"name": {"description": "Company name (max 100 characters)", "type": "string", "maxLength": 100, "example": "My Test Company"}}, "type": "object"}}}}, "responses": {"201": {"description": "Company created successfully", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string", "format": "uuid", "example": "*************-4b3d-b3eb-59733229ee6f"}, "name": {"type": "string", "example": "My Test Company"}, "slug": {"type": "string", "format": "uuid", "example": "9b9a8fc3-567d-41cb-a919-8626c5cdf5da"}, "api_key": {"type": "string", "example": "CIcJrc9frcWnqLzb1ojlopT0jtxJvbc7"}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Validation failed"}, "errors": {"type": "object", "example": {"name": ["This field is required."]}}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Authentication credentials were not provided."}}, "type": "object"}}}}}, "security": [{"BearerAuth": []}]}}, "/api/company/{slug}": {"get": {"tags": ["Company Management"], "summary": "Get company by slug", "description": "Retrieve a specific company by its slug using API key authentication", "operationId": "eb17de54c44d577d00ce2b39b8278802", "parameters": [{"name": "slug", "in": "path", "description": "Company slug (UUID)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "9b9a8fc3-567d-41cb-a919-8626c5cdf5da"}}], "responses": {"200": {"description": "Company details", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string", "format": "uuid", "example": "*************-4b3d-b3eb-59733229ee6f"}, "name": {"type": "string", "example": "My Test Company"}, "slug": {"type": "string", "format": "uuid", "example": "9b9a8fc3-567d-41cb-a919-8626c5cdf5da"}, "api_key": {"type": "string", "example": "CIcJrc9frcWnqLzb1ojlopT0jtxJvbc7"}}, "type": "object"}}}}, "404": {"description": "Company not found", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Not found."}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Invalid API key or company not found."}}, "type": "object"}}}}}, "security": [{"ApiKeyAuth": []}]}, "put": {"tags": ["Company Management"], "summary": "Update company", "description": "Update a company's information using API key authentication", "operationId": "605d628653e07c2ea6b3522f7fd65d3c", "parameters": [{"name": "slug", "in": "path", "description": "Company slug (UUID)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "9b9a8fc3-567d-41cb-a919-8626c5cdf5da"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"properties": {"name": {"description": "Updated company name", "type": "string", "maxLength": 100, "example": "Updated Company Name"}}, "type": "object"}}}}, "responses": {"200": {"description": "Company updated successfully", "content": {"application/json": {"schema": {"properties": {"id": {"type": "string", "format": "uuid", "example": "*************-4b3d-b3eb-59733229ee6f"}, "name": {"type": "string", "example": "Updated Company Name"}, "slug": {"type": "string", "format": "uuid", "example": "9b9a8fc3-567d-41cb-a919-8626c5cdf5da"}, "api_key": {"type": "string", "example": "CIcJrc9frcWnqLzb1ojlopT0jtxJvbc7"}}, "type": "object"}}}}, "404": {"description": "Company not found", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Not found."}}, "type": "object"}}}}, "422": {"description": "Validation error", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Validation failed"}, "errors": {"type": "object", "example": {"name": ["This field is required."]}}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Invalid API key or company not found."}}, "type": "object"}}}}}, "security": [{"ApiKeyAuth": []}]}, "delete": {"tags": ["Company Management"], "summary": "Delete company", "description": "Delete a company using API key authentication", "operationId": "450126243250229f20085fbd03cff05e", "parameters": [{"name": "slug", "in": "path", "description": "Company slug (UUID)", "required": true, "schema": {"type": "string", "format": "uuid", "example": "9b9a8fc3-567d-41cb-a919-8626c5cdf5da"}}], "responses": {"200": {"description": "Company deleted successfully", "content": {"application/json": {"schema": {"properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Company deleted successfully"}}, "type": "object"}}}}, "404": {"description": "Company not found", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Not found."}}, "type": "object"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"properties": {"detail": {"type": "string", "example": "Invalid API key or company not found."}}, "type": "object"}}}}}, "security": [{"ApiKeyAuth": []}]}}, "/api/{environment}/reporting/": {"post": {"tags": ["ZATCA Reporting"], "summary": "Process ZATCA Reporting", "description": "Submit simplified invoices for ZATCA reporting in any environment", "operationId": "processReporting", "parameters": [{"name": "environment", "in": "path", "description": "Environment (sandbox, simulation, production)", "required": true, "schema": {"type": "string", "enum": ["sandbox", "simulation", "production"]}}], "requestBody": {"description": "Invoice data for reporting", "required": true, "content": {"application/json": {"schema": {"required": ["invoice", "profileID", "id", "uuid", "issueDate", "issueTime", "documentCurrencyCode", "taxCurrencyCode", "accountingSupplierParty", "accountingCustomerParty", "taxTotal", "legalMonetaryTotal", "invoiceLines"], "properties": {"invoice": {"properties": {"invoiceType": {"type": "string", "enum": ["simplified", "standard"]}, "documentType": {"type": "string", "enum": ["invoice", "credit_note", "debit_note"]}}, "type": "object"}, "profileID": {"type": "string", "example": "reporting:1.0"}, "id": {"type": "string", "example": "SME00062"}, "uuid": {"type": "string", "example": "6f4d20e0-6bfe-4a80-9389-7dabe6620f12"}, "issueDate": {"type": "string", "example": "2024-09-21"}, "issueTime": {"type": "string", "example": "14:40:40"}, "documentCurrencyCode": {"type": "string", "example": "SAR"}, "taxCurrencyCode": {"type": "string", "example": "SAR"}}, "type": "object"}}}}, "responses": {"200": {"description": "Successful reporting", "content": {"application/json": {"schema": {"properties": {"status": {"type": "string", "example": "200"}, "Message": {"type": "string", "example": "Success"}, "invoiceHash": {"type": "string"}, "qrcode": {"type": "string"}, "data": {"properties": {"reportingStatus": {"type": "string", "example": "REPORTED"}}, "type": "object"}}, "type": "object"}}}}, "400": {"description": "Bad request", "content": {"application/json": {"schema": {"properties": {"status": {"type": "string", "example": "400"}, "Message": {"type": "string", "example": "Validation failed"}}, "type": "object"}}}}}, "security": [{"bearerAuth": [], "apiKey": []}]}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "description": "JWT Bearer token authentication", "bearerFormat": "JWT", "scheme": "bearer"}, "ApiKeyAuth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "API Key authentication using 'Api-Key {key}' format", "name": "Authorization", "in": "header"}}}, "tags": [{"name": "Company Management", "description": "Company CRUD operations"}, {"name": "ZATCA Reporting", "description": "ZATCA Reporting API endpoints for simplified invoices"}, {"name": "🔐 Authentication", "description": "JWT token authentication endpoints"}, {"name": "Location Management", "description": "Location Management"}]}