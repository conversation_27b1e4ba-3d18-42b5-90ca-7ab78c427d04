<?php
// Test the create user API endpoint
$url = 'http://localhost/api/installation/create-user';
$data = [
    'name' => 'Test User',
    'email' => '<EMAIL>',
    'password' => 'password123'
];

$options = [
    'http' => [
        'header' => "Content-type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "Error: Could not make request\n";
} else {
    echo "Response: " . $result . "\n";
}

// Also check the response headers
if (isset($http_response_header)) {
    echo "HTTP Status: " . $http_response_header[0] . "\n";
}
?>
