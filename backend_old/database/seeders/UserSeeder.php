<?php

/*
ZatcaNext - ZATCA e-Invoicing Middleware for Phase 2 Compliance

Copyright (c) 2025 ZatcaNext. All rights reserved.

This software is the proprietary property of ZatcaNext and is protected
by copyright and other intellectual property laws. Unauthorized use,
reproduction, modification, distribution, or reverse-engineering of this
software, in whole or in part, is strictly prohibited without express
written permission from ZatcaNext.

This software is provided "as is" without warranties of any kind, express
or implied. ZatcaNext disclaims all liability for damages arising from its use.

For support or licensing inquiries, contact: <EMAIL>
Website: https://zatcanext.com
Terms of Use: https://zatcanext.com/terms
*/

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Setting;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Check if installation is completed
        if (Setting::isInstallationCompleted()) {
            echo "⚠️  Installation already completed. Skipping user seeding.\n";
            echo "   Use the installation wizard API to create users.\n";
            return;
        }

        // Create test user for JWT authentication
        User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        // Create additional test users
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123'),
            'email_verified_at' => now(),
        ]);

        User::create([
            'name' => 'ZATCA User',
            'email' => '<EMAIL>',
            'password' => Hash::make('zatca123'),
            'email_verified_at' => now(),
        ]);

        echo "✅ Created test users:\n";
        echo "   - <EMAIL> / password\n";
        echo "   - <EMAIL> / admin123\n";
        echo "   - <EMAIL> / zatca123\n";
        echo "⚠️  Note: Use installation wizard API for production setup.\n";
    }
}
