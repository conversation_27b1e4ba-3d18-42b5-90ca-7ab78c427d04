<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Company;

class CompanySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Only use fields that exist in the companies table migration
        $companies = [
            [
                'id' => \Illuminate\Support\Str::uuid(),
                'name' => 'ZATCA Demo Company Ltd',
                'api_key' => \Illuminate\Support\Str::random(32),
                'slug' => \Illuminate\Support\Str::uuid(),
                'vat_number' => '300000000000003',
            ],
            [
                'id' => \Illuminate\Support\Str::uuid(),
                'name' => 'Saudi Tech Solutions',
                'api_key' => \Illuminate\Support\Str::random(32),
                'slug' => \Illuminate\Support\Str::uuid(),
                'vat_number' => '300000000000004',
            ],
            [
                'id' => \Illuminate\Support\Str::uuid(),
                'name' => 'Al Riyadh Trading Company',
                'api_key' => \Illuminate\Support\Str::random(32),
                'slug' => \Illuminate\Support\Str::uuid(),
                'vat_number' => '300000000000005',
            ]
        ];

        foreach ($companies as $companyData) {
            Company::create($companyData);
        }

        $this->command->info('✅ Companies seeded successfully with correct fields!');
    }
}
