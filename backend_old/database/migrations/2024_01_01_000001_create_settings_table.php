<?php

/*
ZatcaNext - ZATCA e-Invoicing Middleware for Phase 2 Compliance

Copyright (c) 2025 ZatcaNext. All rights reserved.

This software is the proprietary property of ZatcaNext and is protected
by copyright and other intellectual property laws. Unauthorized use,
reproduction, modification, distribution, or reverse-engineering of this
software, in whole or in part, is strictly prohibited without express
written permission from ZatcaNext.

This software is provided "as is" without warranties of any kind, express
or implied. ZatcaNext disclaims all liability for damages arising from its use.

For support or licensing inquiries, contact: <EMAIL>
Website: https://zatcanext.com
Terms of Use: https://zatcanext.com/terms
*/

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique();
            $table->text('value')->nullable();
            $table->string('type')->default('string'); // string, boolean, integer, json
            $table->text('description')->nullable();
            $table->timestamps();
        });

        // Insert default installation setting
        DB::table('settings')->insert([
            'key' => 'installation_completed',
            'value' => 'false',
            'type' => 'boolean',
            'description' => 'Indicates whether the initial installation setup has been completed',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
