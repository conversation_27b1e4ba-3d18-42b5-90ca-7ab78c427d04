<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('business_locations', function (Blueprint $table) {
            $table->id();

            // Foreign key to companies table (UUID)
            $table->uuid('company_id');
            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');

            // Django BusinessLocation fields (all nullable like Django)
            $table->text('authentication_token')->nullable(); // max_length=2500 in Django
            $table->string('seller_name', 230)->nullable(); // max_length=230 in Django
            $table->string('tax_no', 16)->nullable(); // max_length=16 in Django
            $table->string('common_name', 230)->nullable(); // max_length=230 in Django
            $table->string('organisation', 230)->nullable(); // max_length=230 in Django
            $table->string('organisation_unit', 230)->nullable(); // max_length=230 in Django
            $table->string('serial_number', 230)->nullable(); // max_length=230 in Django
            $table->string('title', 230)->nullable(); // max_length=230 in Django
            $table->string('registered_address', 230)->nullable(); // max_length=230 in Django
            $table->string('business_category', 230)->nullable(); // max_length=230 in Django
            $table->string('otp', 230)->nullable(); // max_length=230 in Django

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('business_locations');
    }
};
