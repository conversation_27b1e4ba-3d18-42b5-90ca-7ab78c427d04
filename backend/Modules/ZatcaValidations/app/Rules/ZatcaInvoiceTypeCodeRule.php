<?php

namespace Modules\ZatcaValidations\Rules;

use App\Constants\ZatcaConstants;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * ZATCA Invoice Type Code Validation Rule
 * 
 * Validates invoice type codes according to ZATCA requirements:
 * - Must be a valid UBL invoice type code
 * - Must be appropriate for the invoice type (standard/simplified)
 */
class ZatcaInvoiceTypeCodeRule implements ValidationRule
{
    protected string $invoiceType;

    public function __construct(string $invoiceType = '')
    {
        $this->invoiceType = $invoiceType;
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Check if value is a string
        if (!is_string($value)) {
            $fail('The :attribute must be a string.');
            return;
        }

        // Get valid invoice type codes from constants
        $validCodes = [];
        $invoiceTypeCodes = ZatcaConstants::INVOICE_TYPE_CODES;

        // Flatten the nested array to get all valid codes
        foreach ($invoiceTypeCodes as $type => $codes) {
            foreach ($codes as $docType => $code) {
                $validCodes[] = $code;
            }
        }

        // Remove duplicates
        $validCodes = array_unique($validCodes);

        // Check if the code exists in our valid codes
        if (!in_array($value, $validCodes)) {
            $fail('The :attribute must be a valid UBL invoice type code.');
            return;
        }

        // If invoice type is provided, validate consistency
        if (!empty($this->invoiceType)) {
            $this->validateInvoiceTypeConsistency($value, $fail);
        }
    }

    /**
     * Validate that the invoice type code is consistent with the invoice type
     */
    protected function validateInvoiceTypeConsistency(string $code, Closure $fail): void
    {
        $typeCodeMapping = ZatcaConstants::INVOICE_TYPE_CODE_NAMES;

        $isValidForType = false;

        // Check if the code is valid for the specified invoice type
        if (isset($typeCodeMapping[$this->invoiceType])) {
            $validCodesForType = $typeCodeMapping[$this->invoiceType];
            $isValidForType = in_array($code, $validCodesForType);
        }

        if (!$isValidForType) {
            $fail("The :attribute '{$code}' is not valid for {$this->invoiceType} invoices.");
        }
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        return 'The :attribute must be a valid ZATCA invoice type code.';
    }
}
