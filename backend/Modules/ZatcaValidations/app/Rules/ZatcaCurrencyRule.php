<?php

namespace Modules\ZatcaValidations\Rules;

use App\Constants\ZatcaConstants;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * ZATCA Currency Validation Rule
 * 
 * Validates currency codes according to ZATCA requirements:
 * - Must be SAR (Saudi Riyal) for domestic transactions
 * - Must be a valid ISO 4217 currency code
 */
class ZatcaCurrencyRule implements ValidationRule
{
    protected bool $allowForeignCurrency;

    public function __construct(bool $allowForeignCurrency = false)
    {
        $this->allowForeignCurrency = $allowForeignCurrency;
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Check if value is a string
        if (!is_string($value)) {
            $fail('The :attribute must be a string.');
            return;
        }

        // Check if currency code is exactly 3 characters
        if (strlen($value) !== 3) {
            $fail('The :attribute must be exactly 3 characters.');
            return;
        }

        // Convert to uppercase for consistency
        $currencyCode = strtoupper($value);

        // For ZATCA compliance, SAR is required for domestic transactions
        if (!$this->allowForeignCurrency && $currencyCode !== ZatcaConstants::CURRENCY_SAR) {
            $fail('The :attribute must be SAR for domestic transactions.');
            return;
        }

        // Validate against ISO 4217 currency codes
        if (!$this->isValidIsoCurrencyCode($currencyCode)) {
            $fail('The :attribute must be a valid ISO 4217 currency code.');
            return;
        }
    }

    /**
     * Check if the currency code is a valid ISO 4217 code
     */
    protected function isValidIsoCurrencyCode(string $code): bool
    {
        // Common ISO 4217 currency codes
        $validCurrencyCodes = [
            'AED', 'AFN', 'ALL', 'AMD', 'ANG', 'AOA', 'ARS', 'AUD', 'AWG', 'AZN',
            'BAM', 'BBD', 'BDT', 'BGN', 'BHD', 'BIF', 'BMD', 'BND', 'BOB', 'BRL', 'BSD', 'BTN', 'BWP', 'BYN', 'BZD',
            'CAD', 'CDF', 'CHF', 'CLP', 'CNY', 'COP', 'CRC', 'CUC', 'CUP', 'CVE', 'CZK',
            'DJF', 'DKK', 'DOP', 'DZD',
            'EGP', 'ERN', 'ETB', 'EUR',
            'FJD', 'FKP',
            'GBP', 'GEL', 'GGP', 'GHS', 'GIP', 'GMD', 'GNF', 'GTQ', 'GYD',
            'HKD', 'HNL', 'HRK', 'HTG', 'HUF',
            'IDR', 'ILS', 'IMP', 'INR', 'IQD', 'IRR', 'ISK',
            'JEP', 'JMD', 'JOD', 'JPY',
            'KES', 'KGS', 'KHR', 'KMF', 'KPW', 'KRW', 'KWD', 'KYD', 'KZT',
            'LAK', 'LBP', 'LKR', 'LRD', 'LSL', 'LYD',
            'MAD', 'MDL', 'MGA', 'MKD', 'MMK', 'MNT', 'MOP', 'MRU', 'MUR', 'MVR', 'MWK', 'MXN', 'MYR', 'MZN',
            'NAD', 'NGN', 'NIO', 'NOK', 'NPR', 'NZD',
            'OMR',
            'PAB', 'PEN', 'PGK', 'PHP', 'PKR', 'PLN', 'PYG',
            'QAR',
            'RON', 'RSD', 'RUB', 'RWF',
            'SAR', 'SBD', 'SCR', 'SDG', 'SEK', 'SGD', 'SHP', 'SLE', 'SLL', 'SOS', 'SRD', 'STN', 'SYP', 'SZL',
            'THB', 'TJS', 'TMT', 'TND', 'TOP', 'TRY', 'TTD', 'TVD', 'TWD', 'TZS',
            'UAH', 'UGX', 'USD', 'UYU', 'UYW', 'UZS',
            'VED', 'VES', 'VND', 'VUV',
            'WST',
            'XAF', 'XCD', 'XDR', 'XOF', 'XPF',
            'YER',
            'ZAR', 'ZMW', 'ZWL'
        ];

        return in_array($code, $validCurrencyCodes);
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        if (!$this->allowForeignCurrency) {
            return 'The :attribute must be SAR for ZATCA compliance.';
        }
        
        return 'The :attribute must be a valid ISO 4217 currency code.';
    }
}
