<?php

namespace Modules\ZatcaValidations\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

/**
 * ZATCA VAT Number Validation Rule
 * 
 * Validates Saudi Arabian VAT numbers according to ZATCA requirements:
 * - Must be exactly 15 digits
 * - Must pass the Saudi VAT checksum algorithm
 */
class ZatcaVatNumberRule implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Check if value is a string
        if (!is_string($value)) {
            $fail('The :attribute must be a string.');
            return;
        }

        // Check if VAT number is exactly 15 digits
        if (!preg_match('/^\d{15}$/', $value)) {
            $fail('The :attribute must be exactly 15 digits.');
            return;
        }

        // Validate Saudi VAT checksum
        if (!$this->validateSaudiVatChecksum($value)) {
            $fail('The :attribute has an invalid checksum.');
            return;
        }
    }

    /**
     * Validate Saudi VAT number checksum using the official algorithm
     */
    protected function validateSaudiVatChecksum(string $vatNumber): bool
    {
        // Convert VAT number to array of digits
        $digits = str_split($vatNumber);
        
        // Extract check digit (last digit)
        $checkDigit = (int) array_pop($digits);
        
        $sum = 0;
        $multiplier = 2;
        
        // Process digits from right to left (excluding check digit)
        for ($i = count($digits) - 1; $i >= 0; $i--) {
            $digit = (int) $digits[$i];
            $product = $digit * $multiplier;
            
            // If product is greater than 9, subtract 9
            $sum += $product > 9 ? $product - 9 : $product;
            
            // Alternate multiplier between 2 and 1
            $multiplier = $multiplier === 2 ? 1 : 2;
        }
        
        // Calculate expected check digit
        $calculatedCheckDigit = (10 - ($sum % 10)) % 10;
        
        return $calculatedCheckDigit === $checkDigit;
    }
}
