<?php

namespace Modules\ZatcaValidations\Services;

use App\Constants\ZatcaConstants;
use Modules\ZatcaValidations\Rules\ZatcaVatNumberRule;
use Modules\ZatcaValidations\Rules\ZatcaInvoiceTypeCodeRule;
use Modules\ZatcaValidations\Rules\ZatcaCurrencyRule;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

/**
 * ZATCA Validation Rules Service
 * 
 * This service manages all validation rules for ZATCA compliance
 * and provides methods to validate different types of ZATCA data.
 * Used internally by controllers for data validation.
 */
class ValidationRulesService
{
    /**
     * Get validation rules for invoice data
     */
    public function getInvoiceValidationRules(): array
    {
        return [
            'id' => 'required|string|max:255',
            'uuid' => 'required|string|uuid',
            'issueDate' => 'required|date_format:Y-m-d',
            'issueTime' => 'required|date_format:H:i:s',
            'profileID' => 'required|string|in:' . implode(',', [
                ZatcaConstants::PROFILE_REPORTING,
                ZatcaConstants::PROFILE_CLEARANCE
            ]),
            'documentCurrencyCode' => ['required', 'string', 'size:3', new ZatcaCurrencyRule()],
            'taxCurrencyCode' => ['required', 'string', 'size:3', new ZatcaCurrencyRule()],
            'invoiceTypeCode' => ['required', 'string', new ZatcaInvoiceTypeCodeRule()],
            
            // Supplier validation
            'accountingSupplierParty' => 'required|array',
            'accountingSupplierParty.companyID' => ['required', 'string', new ZatcaVatNumberRule()],
            'accountingSupplierParty.partyName' => 'required|string|max:255',
            'accountingSupplierParty.postalAddress' => 'required|array',
            'accountingSupplierParty.postalAddress.streetName' => 'required|string|max:255',
            'accountingSupplierParty.postalAddress.cityName' => 'required|string|max:255',
            'accountingSupplierParty.postalAddress.postalZone' => 'required|string|max:10',
            'accountingSupplierParty.postalAddress.country' => 'required|string|size:2|in:SA',
            'accountingSupplierParty.partyTaxScheme' => 'required|array',

            // Customer validation (optional for simplified invoices)
            'accountingCustomerParty' => 'sometimes|array',
            'accountingCustomerParty.companyID' => ['sometimes', 'string', new ZatcaVatNumberRule()],
            'accountingCustomerParty.partyName' => 'sometimes|string|max:255',
            'accountingCustomerParty.postalAddress' => 'sometimes|array',
            'accountingCustomerParty.postalAddress.streetName' => 'sometimes|string|max:255',
            'accountingCustomerParty.postalAddress.cityName' => 'sometimes|string|max:255',
            'accountingCustomerParty.postalAddress.postalZone' => 'sometimes|string|max:10',
            'accountingCustomerParty.postalAddress.country' => 'sometimes|string|size:2',

            // Invoice lines validation
            'invoiceLines' => 'required|array|min:1',
            'invoiceLines.*.id' => 'required|string|max:255',
            'invoiceLines.*.invoicedQuantity' => 'required|numeric|min:0',
            'invoiceLines.*.lineExtensionAmount' => 'required|numeric|min:0',
            // 'invoiceLines.*.item' => 'required|array',
            'invoiceLines.*.itemName' => 'required|string|max:255',
            //'invoiceLines.*.price' => 'required|array',
            'invoiceLines.*.priceAmount' => 'required|numeric|min:0',

            // Tax totals validation
            'taxTotal' => 'required|array',
            'taxTotal.taxAmount' => 'required|numeric|min:0',
            'taxTotal.taxSubtotals' => 'required|array|min:1',
            'taxTotal.taxSubtotals.*.taxableAmount' => 'required|numeric|min:0',
            'taxTotal.taxSubtotals.*.taxAmount' => 'required|numeric|min:0',
            'taxTotal.taxSubtotals.*.taxCategory' => 'required|array',
            'taxTotal.taxSubtotals.*.taxCategory.id' => 'required|string|in:S,Z,E,G',
            'taxTotal.taxSubtotals.*.taxCategory.percent' => 'required|numeric|min:0|max:100',

            // Legal monetary totals validation
            'legalMonetaryTotal' => 'required|array',
            'legalMonetaryTotal.lineExtensionAmount' => 'required|numeric|min:0',
            'legalMonetaryTotal.taxExclusiveAmount' => 'required|numeric|min:0',
            'legalMonetaryTotal.taxInclusiveAmount' => 'required|numeric|min:0',
            'legalMonetaryTotal.payableAmount' => 'required|numeric|min:0',

            // Additional document reference validation
            'additionalDocumentReference' => 'required|array',
            'additionalDocumentReference.id' => 'required|string|in:ICV',
            'additionalDocumentReference.uuid' => 'required|integer|min:1',
            'additionalDocumentReference.pih' => 'sometimes|string',
        ];
    }

    /**
     * Get validation rules for compliance data
     */
    public function getComplianceValidationRules(): array
    {
        return [
            'invoiceHash' => 'required|string',
            'uuid' => 'required|string|uuid',
            'invoice' => 'required|string', // Base64 encoded XML
            'csid' => 'required|string',
            'secretCsid' => 'required|string',
            'environment' => 'required|string|in:sandbox,simulation,production',
        ];
    }

    /**
     * Get validation rules for clearance data
     */
    public function getClearanceValidationRules(): array
    {
        return [
            'invoiceHash' => 'required|string',
            'uuid' => 'required|string|uuid',
            'invoice' => 'required|string', // Base64 encoded XML
            'x509Certificate' => 'required|string',
            'x509Secret' => 'required|string',
            'environment' => 'required|string|in:sandbox,simulation,production',
        ];
    }

    /**
     * Get validation rules for reporting data
     */
    public function getReportingValidationRules(): array
    {
        return [
            'invoiceHash' => 'required|string',
            'uuid' => 'required|string|uuid',
            'invoice' => 'required|string', // Base64 encoded XML
            'csid' => 'required|string',
            'secretCsid' => 'required|string',
            'environment' => 'required|string|in:sandbox,simulation,production',
        ];
    }

    /**
     * Validate invoice data
     * 
     * @param array $data
     * @return array Validated data
     * @throws ValidationException
     */
    public function validateInvoiceData(array $data): array
    {
        $validator = Validator::make($data, $this->getInvoiceValidationRules(), $this->getValidationMessages());
        
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
        
        return $validator->validated();
    }

    /**
     * Validate compliance data
     * 
     * @param array $data
     * @return array Validated data
     * @throws ValidationException
     */
    public function validateComplianceData(array $data): array
    {
        $validator = Validator::make($data, $this->getComplianceValidationRules(), $this->getValidationMessages());
        
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
        
        return $validator->validated();
    }

    /**
     * Validate clearance data
     * 
     * @param array $data
     * @return array Validated data
     * @throws ValidationException
     */
    public function validateClearanceData(array $data): array
    {
        $validator = Validator::make($data, $this->getClearanceValidationRules(), $this->getValidationMessages());
        
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
        
        return $validator->validated();
    }

    /**
     * Validate reporting data
     * 
     * @param array $data
     * @return array Validated data
     * @throws ValidationException
     */
    public function validateReportingData(array $data): array
    {
        $validator = Validator::make($data, $this->getReportingValidationRules(), $this->getValidationMessages());
        
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
        
        return $validator->validated();
    }

    /**
     * Validate enhanced ZATCA data with business rules
     * 
     * @param array $data
     * @return array Validated data
     * @throws ValidationException
     */
    public function validateEnhancedZatcaData(array $data): array
    {
        // First run basic validation
        $validatedData = $this->validateInvoiceData($data);
        
        // Then run business rules validation
        // This would typically call ZatcaBusinessRulesService
        
        return $validatedData;
    }

    /**
     * Get custom validation messages
     */
    protected function getValidationMessages(): array
    {
        return [
            'uuid.uuid' => 'The UUID must be a valid UUID format.',
            'issueDate.date_format' => 'The issue date must be in Y-m-d format.',
            'issueTime.date_format' => 'The issue time must be in H:i:s format.',
            'documentCurrencyCode.size' => 'The document currency code must be exactly 3 characters.',
            'taxCurrencyCode.size' => 'The tax currency code must be exactly 3 characters.',
            'accountingSupplierParty.postalAddress.country.in' => 'The supplier country must be SA for ZATCA compliance.',
            'invoiceLines.min' => 'At least one invoice line is required.',
            'taxTotal.taxSubtotals.min' => 'At least one tax subtotal is required.',
            'taxTotal.taxSubtotals.*.taxCategory.id.in' => 'Tax category must be one of: S (Standard), Z (Zero), E (Exempt), G (Export).',
            'additionalDocumentReference.id.in' => 'Additional document reference ID must be ICV.',
        ];
    }

    /**
     * Get validation rules by type
     * 
     * @param string $type
     * @return array
     */
    public function getValidationRulesByType(string $type): array
    {
        return match ($type) {
            'invoice' => $this->getInvoiceValidationRules(),
            'compliance' => $this->getComplianceValidationRules(),
            'clearance' => $this->getClearanceValidationRules(),
            'reporting' => $this->getReportingValidationRules(),
            default => throw new \InvalidArgumentException("Unknown validation type: {$type}")
        };
    }

    /**
     * Validate data by type
     * 
     * @param string $type
     * @param array $data
     * @return array
     * @throws ValidationException
     */
    public function validateDataByType(string $type, array $data): array
    {
        return match ($type) {
            'invoice' => $this->validateInvoiceData($data),
            'compliance' => $this->validateComplianceData($data),
            'clearance' => $this->validateClearanceData($data),
            'reporting' => $this->validateReportingData($data),
            default => throw new \InvalidArgumentException("Unknown validation type: {$type}")
        };
    }
}
