<?php

namespace Modules\ZatcaValidations\Services;

use App\Constants\ZatcaConstants;

/**
 * ZATCA Business Rules Service
 * 
 * This service implements ZATCA-specific business rules validation
 * that go beyond basic field validation to ensure compliance with ZATCA requirements.
 */
class ZatcaBusinessRulesService
{
    /**
     * Validate business rules for invoice data
     * 
     * @param array $invoiceData
     * @return array
     */
    public function validateBusinessRules(array $invoiceData): array
    {
        $result = [
            'status' => 'PASS',
            'warnings' => [],
            'errors' => [],
            'rules_checked' => [],
        ];

        // BR-KSA-01: Invoice must have a valid profile ID
        $this->validateProfileId($invoiceData, $result);

        // BR-KSA-02: Validate currency consistency
        $this->validateCurrencyConsistency($invoiceData, $result);

        // BR-KSA-03: Validate tax calculations
        $this->validateTaxCalculations($invoiceData, $result);

        // BR-KSA-04: Validate supplier information
        $this->validateSupplierInformation($invoiceData, $result);

        // BR-KSA-05: Validate customer information
        $this->validateCustomerInformation($invoiceData, $result);

        // BR-KSA-06: Validate invoice totals
        $this->validateInvoiceTotals($invoiceData, $result);

        // BR-KSA-07: Validate line items
        $this->validateLineItems($invoiceData, $result);

        // BR-KSA-08: Validate invoice type specific rules
        $this->validateInvoiceTypeRules($invoiceData, $result);

        // Determine overall status
        if (!empty($result['errors'])) {
            $result['status'] = 'FAIL';
        } elseif (!empty($result['warnings'])) {
            $result['status'] = 'PASS_WITH_WARNINGS';
        }

        return $result;
    }

    /**
     * Validate profile ID business rules
     */
    private function validateProfileId(array $invoiceData, array &$result): void
    {
        $rule = 'BR-KSA-01';
        $result['rules_checked'][] = $rule;

        $profileId = $invoiceData['profileID'] ?? null;
        $invoiceTypeCode = $invoiceData['invoiceTypeCode'] ?? null;

        // Simplified invoices (381, 383) must use reporting profile
        if (in_array($invoiceTypeCode, ['381', '383'])) {
            if ($profileId !== ZatcaConstants::PROFILE_REPORTING) {
                $result['errors'][] = [
                    'rule' => $rule,
                    'message' => 'Simplified invoices must use reporting profile',
                    'field' => 'profileID'
                ];
            }
        }

        // Standard invoices (388, 386) can use either profile
        if (in_array($invoiceTypeCode, ['388', '386'])) {
            if (!in_array($profileId, [ZatcaConstants::PROFILE_REPORTING, ZatcaConstants::PROFILE_CLEARANCE])) {
                $result['errors'][] = [
                    'rule' => $rule,
                    'message' => 'Standard invoices must use reporting or clearance profile',
                    'field' => 'profileID'
                ];
            }
        }
    }

    /**
     * Validate currency consistency
     */
    private function validateCurrencyConsistency(array $invoiceData, array &$result): void
    {
        $rule = 'BR-KSA-02';
        $result['rules_checked'][] = $rule;

        $documentCurrency = $invoiceData['documentCurrencyCode'] ?? null;
        $taxCurrency = $invoiceData['taxCurrencyCode'] ?? null;

        // Both currencies must be SAR for domestic transactions
        if ($documentCurrency !== ZatcaConstants::CURRENCY_SAR) {
            $result['warnings'][] = [
                'rule' => $rule,
                'message' => 'Document currency should be SAR for domestic transactions',
                'field' => 'documentCurrencyCode'
            ];
        }

        if ($taxCurrency !== ZatcaConstants::CURRENCY_SAR) {
            $result['warnings'][] = [
                'rule' => $rule,
                'message' => 'Tax currency should be SAR for domestic transactions',
                'field' => 'taxCurrencyCode'
            ];
        }

        // Document and tax currencies should match
        if ($documentCurrency !== $taxCurrency) {
            $result['errors'][] = [
                'rule' => $rule,
                'message' => 'Document currency and tax currency must match',
                'field' => 'currencyCode'
            ];
        }
    }

    /**
     * Validate tax calculations
     */
    private function validateTaxCalculations(array $invoiceData, array &$result): void
    {
        $rule = 'BR-KSA-03';
        $result['rules_checked'][] = $rule;

        $taxTotal = $invoiceData['taxTotal'] ?? [];
        $taxSubtotals = $taxTotal['taxSubtotals'] ?? [];
        $totalTaxAmount = $taxTotal['taxAmount'] ?? 0;

        // Calculate sum of tax subtotals
        $calculatedTaxAmount = 0;
        foreach ($taxSubtotals as $subtotal) {
            $calculatedTaxAmount += $subtotal['taxAmount'] ?? 0;
        }

        // Tax total should equal sum of subtotals
        if (abs($totalTaxAmount - $calculatedTaxAmount) > 0.01) {
            $result['errors'][] = [
                'rule' => $rule,
                'message' => 'Tax total amount does not match sum of tax subtotals',
                'field' => 'taxTotal.taxAmount'
            ];
        }

        // Validate each tax subtotal calculation
        foreach ($taxSubtotals as $index => $subtotal) {
            $taxableAmount = $subtotal['taxableAmount'] ?? 0;
            $taxAmount = $subtotal['taxAmount'] ?? 0;
            $taxPercent = $subtotal['taxCategory']['percent'] ?? 0;

            $expectedTaxAmount = $taxableAmount * ($taxPercent / 100);

            if (abs($taxAmount - $expectedTaxAmount) > 0.01) {
                $result['errors'][] = [
                    'rule' => $rule,
                    'message' => "Tax calculation incorrect for subtotal {$index}",
                    'field' => "taxTotal.taxSubtotals.{$index}.taxAmount"
                ];
            }
        }
    }

    /**
     * Validate supplier information
     */
    private function validateSupplierInformation(array $invoiceData, array &$result): void
    {
        $rule = 'BR-KSA-04';
        $result['rules_checked'][] = $rule;

        $supplier = $invoiceData['accountingSupplierParty'] ?? [];

        // Supplier must have Saudi VAT number
        $vatNumber = $supplier['companyID'] ?? '';
        if (!$this->isValidSaudiVatNumber($vatNumber)) {
            $result['errors'][] = [
                'rule' => $rule,
                'message' => 'Supplier must have valid Saudi VAT number',
                'field' => 'accountingSupplierParty.companyID'
            ];
        }

        // Supplier address must be in Saudi Arabia
        $country = $supplier['postalAddress']['country'] ?? '';
        if ($country !== 'SA') {
            $result['errors'][] = [
                'rule' => $rule,
                'message' => 'Supplier address must be in Saudi Arabia',
                'field' => 'accountingSupplierParty.postalAddress.country'
            ];
        }
    }

    /**
     * Validate customer information
     */
    private function validateCustomerInformation(array $invoiceData, array &$result): void
    {
        $rule = 'BR-KSA-05';
        $result['rules_checked'][] = $rule;

        $invoiceTypeCode = $invoiceData['invoiceTypeCode'] ?? null;
        $customer = $invoiceData['accountingCustomerParty'] ?? null;

        // Standard invoices require customer information
        if (in_array($invoiceTypeCode, ['388', '386'])) {
            if (empty($customer)) {
                $result['errors'][] = [
                    'rule' => $rule,
                    'message' => 'Standard invoices require customer information',
                    'field' => 'accountingCustomerParty'
                ];
                return;
            }

            // Customer VAT number validation for B2B transactions
            $customerVat = $customer['companyID'] ?? '';
            if (!empty($customerVat) && !$this->isValidSaudiVatNumber($customerVat)) {
                $result['warnings'][] = [
                    'rule' => $rule,
                    'message' => 'Customer VAT number format should be validated',
                    'field' => 'accountingCustomerParty.companyID'
                ];
            }
        }
    }

    /**
     * Validate invoice totals
     */
    private function validateInvoiceTotals(array $invoiceData, array &$result): void
    {
        $rule = 'BR-KSA-06';
        $result['rules_checked'][] = $rule;

        $totals = $invoiceData['legalMonetaryTotal'] ?? [];
        $taxTotal = $invoiceData['taxTotal']['taxAmount'] ?? 0;

        $lineExtension = $totals['lineExtensionAmount'] ?? 0;
        $taxExclusive = $totals['taxExclusiveAmount'] ?? 0;
        $taxInclusive = $totals['taxInclusiveAmount'] ?? 0;
        $payable = $totals['payableAmount'] ?? 0;

        // Tax exclusive should equal line extension
        if (abs($taxExclusive - $lineExtension) > 0.01) {
            $result['errors'][] = [
                'rule' => $rule,
                'message' => 'Tax exclusive amount should equal line extension amount',
                'field' => 'legalMonetaryTotal.taxExclusiveAmount'
            ];
        }

        // Tax inclusive should equal tax exclusive plus tax
        if (abs($taxInclusive - ($taxExclusive + $taxTotal)) > 0.01) {
            $result['errors'][] = [
                'rule' => $rule,
                'message' => 'Tax inclusive amount should equal tax exclusive plus tax total',
                'field' => 'legalMonetaryTotal.taxInclusiveAmount'
            ];
        }

        // Payable should equal tax inclusive
        if (abs($payable - $taxInclusive) > 0.01) {
            $result['errors'][] = [
                'rule' => $rule,
                'message' => 'Payable amount should equal tax inclusive amount',
                'field' => 'legalMonetaryTotal.payableAmount'
            ];
        }
    }

    /**
     * Validate line items
     */
    private function validateLineItems(array $invoiceData, array &$result): void
    {
        $rule = 'BR-KSA-07';
        $result['rules_checked'][] = $rule;

        $lines = $invoiceData['invoiceLines'] ?? [];
        $totalLineExtension = 0;

        foreach ($lines as $index => $line) {
            $quantity = $line['invoicedQuantity'] ?? 0;
            $price = $line['price']['priceAmount'] ?? 0;
            $lineExtension = $line['lineExtensionAmount'] ?? 0;

            // Line extension should equal quantity * price
            $expectedLineExtension = $quantity * $price;
            if (abs($lineExtension - $expectedLineExtension) > 0.01) {
                $result['errors'][] = [
                    'rule' => $rule,
                    'message' => "Line {$index}: Extension amount should equal quantity × price",
                    'field' => "invoiceLines.{$index}.lineExtensionAmount"
                ];
            }

            $totalLineExtension += $lineExtension;
        }

        // Total line extension should match legal monetary total
        $legalLineExtension = $invoiceData['legalMonetaryTotal']['lineExtensionAmount'] ?? 0;
        if (abs($totalLineExtension - $legalLineExtension) > 0.01) {
            $result['errors'][] = [
                'rule' => $rule,
                'message' => 'Sum of line extensions should match legal monetary total',
                'field' => 'legalMonetaryTotal.lineExtensionAmount'
            ];
        }
    }

    /**
     * Validate invoice type specific rules
     */
    private function validateInvoiceTypeRules(array $invoiceData, array &$result): void
    {
        $rule = 'BR-KSA-08';
        $result['rules_checked'][] = $rule;

        $invoiceTypeCode = $invoiceData['invoiceTypeCode'] ?? null;

        // Credit note specific validations
        if (in_array($invoiceTypeCode, ['383', '386'])) {
            // Credit notes should have negative amounts or reference to original invoice
            $payableAmount = $invoiceData['legalMonetaryTotal']['payableAmount'] ?? 0;
            if ($payableAmount > 0) {
                $result['warnings'][] = [
                    'rule' => $rule,
                    'message' => 'Credit notes typically have negative payable amounts',
                    'field' => 'legalMonetaryTotal.payableAmount'
                ];
            }
        }

        // Debit note specific validations
        if (in_array($invoiceTypeCode, ['381', '388'])) {
            // Standard invoice validations
            $payableAmount = $invoiceData['legalMonetaryTotal']['payableAmount'] ?? 0;
            if ($payableAmount <= 0) {
                $result['warnings'][] = [
                    'rule' => $rule,
                    'message' => 'Standard invoices should have positive payable amounts',
                    'field' => 'legalMonetaryTotal.payableAmount'
                ];
            }
        }
    }

    /**
     * Validate Saudi VAT number format
     */
    private function isValidSaudiVatNumber(string $vatNumber): bool
    {
        // Saudi VAT numbers are 15 digits
        if (!preg_match('/^\d{15}$/', $vatNumber)) {
            return false;
        }

        // Implement checksum validation if needed
        return true;
    }
}
