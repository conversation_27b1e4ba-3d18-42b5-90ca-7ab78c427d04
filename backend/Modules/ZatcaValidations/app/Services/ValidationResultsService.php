<?php

namespace Modules\ZatcaValidations\Services;

use Illuminate\Support\Collection;

/**
 * ZATCA Validation Results Service
 * 
 * This service handles the processing and formatting of validation results
 * from ZATCA API responses and internal validations.
 */
class ValidationResultsService
{
    /**
     * Process ZATCA API validation response
     * 
     * @param array $response
     * @return array
     */
    public function processZatcaResponse(array $response): array
    {
        $result = [
            'success' => false,
            'status' => 'UNKNOWN',
            'validationResults' => [],
            'warnings' => [],
            'errors' => [],
            'clearanceStatus' => null,
            'qrCode' => null,
            'clearedInvoice' => null,
        ];

        // Check if response has validation results
        if (isset($response['validationResults'])) {
            $validationResults = $response['validationResults'];
            
            $result['status'] = $validationResults['status'] ?? 'UNKNOWN';
            $result['success'] = $result['status'] === 'PASS';
            
            // Process info messages (warnings)
            if (isset($validationResults['infoMessages'])) {
                $result['warnings'] = $this->processMessages($validationResults['infoMessages']);
            }
            
            // Process warning messages
            if (isset($validationResults['warningMessages'])) {
                $result['warnings'] = array_merge(
                    $result['warnings'],
                    $this->processMessages($validationResults['warningMessages'])
                );
            }
            
            // Process error messages
            if (isset($validationResults['errorMessages'])) {
                $result['errors'] = $this->processMessages($validationResults['errorMessages']);
            }
            
            $result['validationResults'] = $validationResults;
        }

        // Check for clearance status
        if (isset($response['clearanceStatus'])) {
            $result['clearanceStatus'] = $response['clearanceStatus'];
        }

        // Check for QR code
        if (isset($response['qrCode'])) {
            $result['qrCode'] = $response['qrCode'];
        }

        // Check for cleared invoice
        if (isset($response['clearedInvoice'])) {
            $result['clearedInvoice'] = $response['clearedInvoice'];
        }

        return $result;
    }

    /**
     * Process validation messages
     * 
     * @param array $messages
     * @return array
     */
    private function processMessages(array $messages): array
    {
        $processed = [];
        
        foreach ($messages as $message) {
            $processed[] = [
                'type' => $message['type'] ?? 'UNKNOWN',
                'code' => $message['code'] ?? null,
                'category' => $message['category'] ?? null,
                'message' => $message['message'] ?? 'Unknown message',
                'status' => $message['status'] ?? null,
            ];
        }
        
        return $processed;
    }

    /**
     * Create a success result
     * 
     * @param array $data
     * @return array
     */
    public function createSuccessResult(array $data = []): array
    {
        return array_merge([
            'success' => true,
            'status' => 'SUCCESS',
            'message' => 'Operation completed successfully',
            'data' => $data,
            'timestamp' => now()->toISOString(),
        ], $data);
    }

    /**
     * Create an error result
     * 
     * @param string $message
     * @param array $errors
     * @param int $code
     * @return array
     */
    public function createErrorResult(string $message, array $errors = [], int $code = 400): array
    {
        return [
            'success' => false,
            'status' => 'ERROR',
            'message' => $message,
            'errors' => $errors,
            'code' => $code,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Format validation result for API response
     * 
     * @param array $result
     * @return array
     */
    public function formatForApiResponse(array $result): array
    {
        $formatted = [
            'success' => $result['success'] ?? false,
            'status' => $result['status'] ?? 'UNKNOWN',
            'message' => $this->getStatusMessage($result['status'] ?? 'UNKNOWN'),
            'timestamp' => now()->toISOString(),
        ];

        // Add validation results if present
        if (isset($result['validationResults'])) {
            $formatted['validationResults'] = $result['validationResults'];
        }

        // Add warnings if present
        if (!empty($result['warnings'])) {
            $formatted['warnings'] = $result['warnings'];
        }

        // Add errors if present
        if (!empty($result['errors'])) {
            $formatted['errors'] = $result['errors'];
        }

        // Add clearance-specific data
        if (isset($result['clearanceStatus'])) {
            $formatted['clearanceStatus'] = $result['clearanceStatus'];
        }

        if (isset($result['qrCode'])) {
            $formatted['qrCode'] = $result['qrCode'];
        }

        if (isset($result['clearedInvoice'])) {
            $formatted['clearedInvoice'] = $result['clearedInvoice'];
        }

        return $formatted;
    }

    /**
     * Get status message based on status code
     * 
     * @param string $status
     * @return string
     */
    private function getStatusMessage(string $status): string
    {
        return match ($status) {
            'PASS' => 'Validation passed successfully',
            'FAIL' => 'Validation failed',
            'WARNING' => 'Validation passed with warnings',
            'ERROR' => 'Validation error occurred',
            'SUCCESS' => 'Operation completed successfully',
            default => 'Unknown status'
        };
    }

    /**
     * Process batch validation results
     * 
     * @param array $results
     * @return array
     */
    public function processBatchResults(array $results): array
    {
        $processed = [];
        $summary = [
            'total' => count($results),
            'successful' => 0,
            'failed' => 0,
            'warnings' => 0,
        ];

        foreach ($results as $index => $result) {
            $processedResult = $this->processZatcaResponse($result);
            $processed[] = array_merge($processedResult, ['index' => $index]);

            if ($processedResult['success']) {
                $summary['successful']++;
            } else {
                $summary['failed']++;
            }

            if (!empty($processedResult['warnings'])) {
                $summary['warnings']++;
            }
        }

        return [
            'success' => $summary['failed'] === 0,
            'status' => $summary['failed'] === 0 ? 'SUCCESS' : 'PARTIAL_SUCCESS',
            'message' => $this->getBatchStatusMessage($summary),
            'results' => $processed,
            'summary' => $summary,
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Get batch status message
     * 
     * @param array $summary
     * @return string
     */
    private function getBatchStatusMessage(array $summary): string
    {
        if ($summary['failed'] === 0) {
            return "All {$summary['total']} validations completed successfully";
        }

        return "{$summary['successful']} of {$summary['total']} validations successful, {$summary['failed']} failed";
    }

    /**
     * Merge multiple validation results
     * 
     * @param array $results
     * @return array
     */
    public function mergeResults(array $results): array
    {
        $merged = [
            'success' => true,
            'status' => 'SUCCESS',
            'message' => 'All validations completed',
            'results' => $results,
            'summary' => [
                'total' => count($results),
                'successful' => 0,
                'failed' => 0,
                'warnings' => 0,
            ],
            'timestamp' => now()->toISOString(),
        ];

        foreach ($results as $result) {
            if ($result['success'] ?? false) {
                $merged['summary']['successful']++;
            } else {
                $merged['summary']['failed']++;
                $merged['success'] = false;
                $merged['status'] = 'PARTIAL_SUCCESS';
            }

            if (isset($result['warnings']) && !empty($result['warnings'])) {
                $merged['summary']['warnings']++;
            }
        }

        if ($merged['summary']['failed'] > 0) {
            $merged['message'] = "Partial success: {$merged['summary']['successful']} passed, {$merged['summary']['failed']} failed";
        }

        return $merged;
    }
}
