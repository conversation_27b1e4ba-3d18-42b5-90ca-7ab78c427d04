<?php

namespace Modules\ZatcaValidations\Http\Middleware;

use Modules\ZatcaValidations\Services\ValidationRulesService;
use Modules\ZatcaValidations\Services\ValidationResultsService;
use Modules\ZatcaValidations\Services\ZatcaBusinessRulesService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

/**
 * ZATCA Pre-Validation Middleware
 * 
 * This middleware validates ZATCA data before it reaches the API endpoints,
 * providing early validation to prevent invalid data from being sent to ZATCA.
 */
class ZatcaPreValidationMiddleware
{
    protected ValidationRulesService $validationRulesService;
    protected ValidationResultsService $validationResultsService;
    protected ZatcaBusinessRulesService $businessRulesService;

    public function __construct(
        ValidationRulesService $validationRulesService,
        ValidationResultsService $validationResultsService,
        ZatcaBusinessRulesService $businessRulesService
    ) {
        $this->validationRulesService = $validationRulesService;
        $this->validationResultsService = $validationResultsService;
        $this->businessRulesService = $businessRulesService;
    }

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $validationType = 'invoice'): Response|JsonResponse
    {
        // Skip validation for GET requests
        if ($request->isMethod('GET')) {
            return $next($request);
        }

        // Get request data
        $data = $request->all();

        try {
            // Perform basic validation based on type
            switch ($validationType) {
                case 'invoice':
                    $this->validationRulesService->validateInvoiceData($data);
                    break;
                case 'compliance':
                    $this->validationRulesService->validateComplianceData($data);
                    break;
                case 'clearance':
                    $this->validationRulesService->validateClearanceData($data);
                    break;
                case 'reporting':
                    $this->validationRulesService->validateReportingData($data);
                    break;
                default:
                    // Skip validation for unknown types
                    return $next($request);
            }

            // Perform business rules validation for invoice data
            if ($validationType === 'invoice') {
                $businessRulesResult = $this->businessRulesService->validateBusinessRules($data);
                
                // Add business rules warnings to request for downstream processing
                if (!empty($businessRulesResult['warnings'])) {
                    $request->merge(['_zatca_warnings' => $businessRulesResult['warnings']]);
                }
            }

            // If validation passes, continue to the next middleware/controller
            return $next($request);

        } catch (ValidationException $e) {
            // Create standardized error response
            $errors = [];
            foreach ($e->errors() as $field => $messages) {
                foreach ($messages as $message) {
                    $errors[] = [
                        'field' => $field,
                        'message' => $message,
                        'type' => 'VALIDATION_ERROR',
                        'code' => 'PRE_VALIDATION_FAILED'
                    ];
                }
            }

            $result = $this->validationResultsService->createErrorResult(
                'Pre-validation failed: ' . implode(', ', array_column($errors, 'message')),
                $errors,
                422
            );

            return response()->json($result, 422);

        } catch (\Exception $e) {
            // Handle unexpected errors
            $result = $this->validationResultsService->createErrorResult(
                'Unexpected validation error: ' . $e->getMessage(),
                [],
                500
            );

            return response()->json($result, 500);
        }
    }

    /**
     * Determine if the request should be validated
     */
    protected function shouldValidate(Request $request): bool
    {
        // Skip validation for certain routes or conditions
        $skipRoutes = [
            'api/zatca-validations/rules/*',
            'api/zatca-validations/health',
        ];

        foreach ($skipRoutes as $pattern) {
            if ($request->is($pattern)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get validation type from request
     */
    protected function getValidationType(Request $request): string
    {
        // Determine validation type based on route
        $path = $request->path();

        if (str_contains($path, 'compliance')) {
            return 'compliance';
        }

        if (str_contains($path, 'clearance')) {
            return 'clearance';
        }

        if (str_contains($path, 'reporting')) {
            return 'reporting';
        }

        return 'invoice';
    }
}
