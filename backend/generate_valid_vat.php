<?php

/**
 * Generate valid Saudi VAT numbers using the official checksum algorithm
 * This script helps create test data with valid VAT numbers
 */

function generateValidSaudiVatNumber(string $baseNumber): string
{
    // Ensure base number is 14 digits (we'll calculate the 15th digit)
    $baseNumber = str_pad(substr($baseNumber, 0, 14), 14, '0', STR_PAD_LEFT);
    
    // Convert to array of digits
    $digits = str_split($baseNumber);
    
    $sum = 0;
    $multiplier = 2;
    
    // Process digits from right to left
    for ($i = count($digits) - 1; $i >= 0; $i--) {
        $digit = (int) $digits[$i];
        $product = $digit * $multiplier;
        
        // If product is greater than 9, subtract 9
        $sum += $product > 9 ? $product - 9 : $product;
        
        // Alternate multiplier between 2 and 1
        $multiplier = $multiplier === 2 ? 1 : 2;
    }
    
    // Calculate check digit
    $checkDigit = (10 - ($sum % 10)) % 10;
    
    return $baseNumber . $checkDigit;
}

function validateSaudiVatChecksum(string $vatNumber): bool
{
    if (strlen($vatNumber) !== 15 || !ctype_digit($vatNumber)) {
        return false;
    }
    
    // Convert VAT number to array of digits
    $digits = str_split($vatNumber);
    
    // Extract check digit (last digit)
    $checkDigit = (int) array_pop($digits);
    
    $sum = 0;
    $multiplier = 2;
    
    // Process digits from right to left (excluding check digit)
    for ($i = count($digits) - 1; $i >= 0; $i--) {
        $digit = (int) $digits[$i];
        $product = $digit * $multiplier;
        
        // If product is greater than 9, subtract 9
        $sum += $product > 9 ? $product - 9 : $product;
        
        // Alternate multiplier between 2 and 1
        $multiplier = $multiplier === 2 ? 1 : 2;
    }
    
    // Calculate expected check digit
    $calculatedCheckDigit = (10 - ($sum % 10)) % 10;
    
    return $calculatedCheckDigit === $checkDigit;
}

echo "=== Saudi VAT Number Generator ===\n\n";

// Generate valid VAT numbers for test data
$testNumbers = [
    '31009991580000', // Customer VAT base
    '30000000000000', // Simple test VAT base
    '31234567890123', // Another test VAT base
    '39999999999999', // High number test VAT base
];

echo "Generated Valid Saudi VAT Numbers:\n";
echo str_repeat('-', 40) . "\n";

foreach ($testNumbers as $base) {
    $validVat = generateValidSaudiVatNumber($base);
    $isValid = validateSaudiVatChecksum($validVat) ? '✅' : '❌';
    echo "Base: $base -> Valid VAT: $validVat $isValid\n";
}

echo "\n=== Current Invalid VAT Numbers ===\n";
$invalidNumbers = [
    '310099915800003', // Current customer VAT (invalid)
    '300000000000003', // Current supplier VAT (might be invalid)
];

foreach ($invalidNumbers as $vat) {
    $isValid = validateSaudiVatChecksum($vat) ? '✅ Valid' : '❌ Invalid';
    echo "VAT: $vat -> $isValid\n";
    
    if (!validateSaudiVatChecksum($vat)) {
        // Generate a corrected version
        $corrected = generateValidSaudiVatNumber(substr($vat, 0, 14));
        echo "  Corrected: $corrected\n";
    }
}

echo "\n=== Recommended VAT Numbers for Test Data ===\n";
echo "Supplier VAT: " . generateValidSaudiVatNumber('30000000000000') . "\n";
echo "Customer VAT: " . generateValidSaudiVatNumber('31009991580000') . "\n";
echo "Alternative Customer VAT: " . generateValidSaudiVatNumber('31234567890123') . "\n";
