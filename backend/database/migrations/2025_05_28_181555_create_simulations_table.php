<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('simulations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_location_id')->constrained()->onDelete('cascade');
            $table->text('private_key')->nullable();
            $table->text('public_key')->nullable();
            $table->text('csr')->nullable();
            $table->text('csid')->nullable();
            $table->text('csid_base64')->nullable();
            $table->text('secret_csid')->nullable();
            $table->string('csid_request', 100)->nullable();
            $table->text('x509_base64')->nullable();
            $table->text('x509_certificate')->nullable();
            $table->string('x509_secret', 500)->nullable();
            $table->string('x509_request', 100)->nullable();
            $table->timestamps();

            // Ensure one-to-one relationship
            $table->unique('business_location_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('simulations');
    }
};
