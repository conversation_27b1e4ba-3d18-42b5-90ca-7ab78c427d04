<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, validate and clean existing data
        $this->validateAndCleanExistingData();
        
        Schema::table('business_locations', function (Blueprint $table) {
            // Drop existing column and recreate with new constraints
            $table->dropColumn('tax_no');
        });
        
        Schema::table('business_locations', function (Blueprint $table) {
            // Add the new tax_no column with proper constraints
            $table->string('tax_no', 15)->nullable()->unique()->after('seller_name');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('business_locations', function (Blueprint $table) {
            // Drop the unique constraint and revert to old structure
            $table->dropColumn('tax_no');
        });
        
        Schema::table('business_locations', function (Blueprint $table) {
            // Restore original column structure
            $table->string('tax_no', 16)->nullable()->after('seller_name');
        });
    }
    
    /**
     * Validate and clean existing tax_no data
     */
    private function validateAndCleanExistingData(): void
    {
        // Get all records with tax_no values
        $records = DB::table('business_locations')
            ->whereNotNull('tax_no')
            ->where('tax_no', '!=', '')
            ->get();
            
        foreach ($records as $record) {
            $taxNo = $record->tax_no;
            
            // Check if tax_no is exactly 15 digits and starts/ends with 3
            if (!preg_match('/^\d{15}$/', $taxNo) || !preg_match('/^3.*3$/', $taxNo)) {
                // Log warning and clear invalid tax numbers
                \Log::warning("Invalid tax_no found for business_location ID {$record->id}: {$taxNo}. Must be 15 digits starting and ending with 3. Clearing value.");

                DB::table('business_locations')
                    ->where('id', $record->id)
                    ->update(['tax_no' => null]);
            }
        }
        
        // Check for duplicates and clear them
        $duplicates = DB::table('business_locations')
            ->select('tax_no', DB::raw('COUNT(*) as count'))
            ->whereNotNull('tax_no')
            ->where('tax_no', '!=', '')
            ->groupBy('tax_no')
            ->having('count', '>', 1)
            ->get();
            
        foreach ($duplicates as $duplicate) {
            \Log::warning("Duplicate tax_no found: {$duplicate->tax_no}. Clearing all but the first occurrence.");
            
            // Keep the first record, clear the rest
            $recordsWithDuplicate = DB::table('business_locations')
                ->where('tax_no', $duplicate->tax_no)
                ->orderBy('id')
                ->get();
                
            foreach ($recordsWithDuplicate->skip(1) as $record) {
                DB::table('business_locations')
                    ->where('id', $record->id)
                    ->update(['tax_no' => null]);
            }
        }
    }
};
