<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add ZATCA validations module enablement setting
        DB::table('settings')->insert([
            'key' => 'zatca_validations_enabled',
            'value' => 'true',
            'type' => 'boolean',
            'description' => 'Enable/disable ZATCA validations module',
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('settings')->where('key', 'zatca_validations_enabled')->delete();
    }
};
