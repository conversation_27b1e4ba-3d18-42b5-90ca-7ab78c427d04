<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // First, validate and clean existing data
        $this->validateAndCleanExistingSerialNumbers();
        
        Schema::table('business_locations', function (Blueprint $table) {
            // Add unique constraint to serial_number
            $table->unique('serial_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('business_locations', function (Blueprint $table) {
            // Drop the unique constraint
            $table->dropUnique(['serial_number']);
        });
    }
    
    /**
     * Validate and clean existing serial_number data
     */
    private function validateAndCleanExistingSerialNumbers(): void
    {
        // Check for duplicates and clear them
        $duplicates = DB::table('business_locations')
            ->select('serial_number', DB::raw('COUNT(*) as count'))
            ->whereNotNull('serial_number')
            ->where('serial_number', '!=', '')
            ->groupBy('serial_number')
            ->having('count', '>', 1)
            ->get();
            
        foreach ($duplicates as $duplicate) {
            \Log::warning("Duplicate serial_number found: {$duplicate->serial_number}. Clearing all but the first occurrence.");
            
            // Keep the first record, clear the rest
            $recordsWithDuplicate = DB::table('business_locations')
                ->where('serial_number', $duplicate->serial_number)
                ->orderBy('id')
                ->get();
                
            foreach ($recordsWithDuplicate->skip(1) as $record) {
                DB::table('business_locations')
                    ->where('id', $record->id)
                    ->update(['serial_number' => null]);
            }
        }
    }
};
