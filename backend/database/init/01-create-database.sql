-- Create database and user for ZATCA e-invoicing
CREATE DATABASE IF NOT EXISTS zatca_einvoicing CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Create user if not exists
CREATE USER IF NOT EXISTS 'zatca_user'@'%' IDENTIFIED BY 'zatca_password';

-- <PERSON> privileges
GRANT ALL PRIVILEGES ON zatca_einvoicing.* TO 'zatca_user'@'%';

-- Flush privileges
FLUSH PRIVILEGES;

-- Use the database
USE zatca_einvoicing;
