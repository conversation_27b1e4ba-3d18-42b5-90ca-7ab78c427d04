<?php

namespace Database\Seeders;

use App\Models\BusinessLocation;
use App\Models\Company;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class BusinessLocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first company (created by CompanySeeder)
        $company = Company::first();
        
        if (!$company) {
            $this->command->error('No companies found. Please run CompanySeeder first.');
            return;
        }

        // Create business locations with authentication tokens (using actual table columns)
        $locations = [
            [
                'id' => 123, // Fixed ID for testing
                'company_id' => $company->id,
                'authentication_token' => '2qrzJvUz9Cpn5C83XhqJUhKfqbILyDQ4tiPwqAQiIhOa99muNaSjyQ2Zw995UwyC', // Test secret
                'seller_name' => 'ZATCA Demo Company Ltd',
                'tax_no' => '300000000000003',
                'common_name' => 'ZATCA Demo Company Ltd',
                'organisation' => 'ZATCA Demo Company Ltd',
                'organisation_unit' => 'IT Department',
                'business_category' => 'Technology',
            ],
            [
                'id' => 124,
                'company_id' => $company->id,
                'authentication_token' => Str::random(64),
                'seller_name' => 'ZATCA Demo Company Ltd - Branch',
                'tax_no' => '300000000000004',
                'common_name' => 'ZATCA Demo Company Ltd - Branch',
                'organisation' => 'ZATCA Demo Company Ltd',
                'organisation_unit' => 'Sales Department',
                'business_category' => 'Technology',
            ]
        ];

        foreach ($locations as $locationData) {
            BusinessLocation::create($locationData);
        }

        $this->command->info('✅ Business locations seeded successfully!');
        $this->command->info('   - Location 123: Main Store (test authentication token)');
        $this->command->info('   - Location 124: Branch Store (random authentication token)');
    }
}
