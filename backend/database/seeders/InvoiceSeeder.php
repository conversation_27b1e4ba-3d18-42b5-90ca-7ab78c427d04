<?php

namespace Database\Seeders;

use App\Constants\ZatcaConstants;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Company;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use Carbon\Carbon;

class InvoiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $companies = Company::all();

        if ($companies->isEmpty()) {
            $this->command->error('No companies found. Please run CompanySeeder first.');
            return;
        }

        foreach ($companies as $company) {
            // Create 3 sample invoices for each company
            for ($i = 1; $i <= 3; $i++) {
                $invoice = Invoice::create([
                    'company_id' => $company->id,
                    'invoice_number' => 'INV-' . $company->id . '-2024-' . str_pad($i, 3, '0', STR_PAD_LEFT),
                    'invoice_type' => '01', // Standard Invoice
                    'invoice_subtype' => '01', // Tax Invoice
                    'issue_date' => Carbon::now()->subDays(rand(1, 30)),
                    'due_date' => Carbon::now()->addDays(rand(15, 45)),
                    'currency_code' => 'SAR',
                    'exchange_rate' => 1.0000,
                    'customer_name' => $this->getRandomCustomerName(),
                    'customer_vat_number' => '300000000000' . str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT),
                    'customer_commercial_registration' => '20' . rand(10000000, 99999999),
                    'customer_address' => $this->getRandomAddress(),
                    'customer_city' => $this->getRandomCity(),
                    'customer_postal_code' => rand(10000, 99999),
                    'customer_country_code' => 'SA',
                    'subtotal_amount' => 0, // Will be calculated after adding items
                    'tax_amount' => 0,
                    'discount_amount' => 0,
                    'total_amount' => 0,
                    'paid_amount' => 0,
                    'remaining_amount' => 0,
                    'status' => 'draft',
                    'notes' => 'Sample invoice for testing ZATCA e-invoicing system',
                ]);

                // Add 2-4 items to each invoice
                $itemCount = rand(2, 4);
                for ($j = 1; $j <= $itemCount; $j++) {
                    $quantity = rand(1, 10);
                    $unitPrice = rand(50, 500) + (rand(0, 99) / 100);
                    $lineTotal = $quantity * $unitPrice;
                    $discountAmount = $lineTotal * (rand(0, 10) / 100); // 0-10% discount
                    $netAmount = $lineTotal - $discountAmount;
                    $taxRate = 15.00;
                    $taxAmount = $netAmount * ($taxRate / 100);

                    InvoiceItem::create([
                        'invoice_id' => $invoice->id,
                        'line_number' => $j,
                        'item_name' => $this->getRandomItemName(),
                        'item_description' => $this->getRandomItemDescription(),
                        'item_code' => 'ITEM-' . str_pad($j, 3, '0', STR_PAD_LEFT),
                        'unit_of_measure' => 'PCE',
                        'quantity' => $quantity,
                        'unit_price' => $unitPrice,
                        'line_total' => $lineTotal,
                        'discount_amount' => $discountAmount,
                        'discount_percentage' => ($discountAmount / $lineTotal) * 100,
                        'net_amount' => $netAmount,
                        'tax_category' => 'S',
                        'tax_rate' => $taxRate,
                        'tax_amount' => $taxAmount,
                    ]);
                }

                // Calculate invoice totals
                $invoice->calculateTotals();
                $invoice->save();
            }
        }

        $this->command->info('Invoices seeded successfully!');
    }

    private function getRandomCustomerName(): string
    {
        $names = [
            'Al Faisaliah Group',
            'Saudi Aramco Trading',
            'SABIC Industries',
            'Al Rajhi Company',
            'Binladin Group',
            'Al Muhaidib Group',
            'Zamil Industrial',
            'Al Jomaih Group',
            'Olayan Group',
            'Al Khodari Sons'
        ];

        return $names[array_rand($names)];
    }

    private function getRandomAddress(): string
    {
        $addresses = [
            'King Fahd Road, Business District',
            'Prince Mohammed Bin Abdulaziz Road',
            'Olaya Street, Commercial Center',
            'King Abdulaziz Road',
            'Al Tahlia Street',
            'Imam Saud Bin Abdulaziz Road',
            'King Khalid Road',
            'Prince Sultan Road'
        ];

        return $addresses[array_rand($addresses)];
    }

    private function getRandomCity(): string
    {
        $cities = ['Riyadh', 'Jeddah', 'Dammam', 'Mecca', 'Medina', 'Khobar', 'Tabuk', 'Abha'];
        return $cities[array_rand($cities)];
    }

    private function getRandomItemName(): string
    {
        $items = [
            'Software License',
            'Consulting Services',
            'Hardware Equipment',
            'Maintenance Service',
            'Training Program',
            'Technical Support',
            'Cloud Storage',
            'Security System',
            'Network Equipment',
            'Mobile Device'
        ];

        return $items[array_rand($items)];
    }

    private function getRandomItemDescription(): string
    {
        $descriptions = [
            'Professional software license for business use',
            'Expert consulting services for digital transformation',
            'High-quality hardware equipment with warranty',
            'Comprehensive maintenance and support service',
            'Professional training program for staff development',
            'Technical support and troubleshooting service',
            'Secure cloud storage solution',
            'Advanced security system installation',
            'Enterprise-grade network equipment',
            'Latest mobile device with accessories'
        ];

        return $descriptions[array_rand($descriptions)];
    }
}
