<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\v1\CompanyController;
use App\Http\Controllers\Api\v1\BusinessLocationController;
use App\Http\Controllers\Api\v1\AuthController;
use App\Http\Controllers\Api\v1\InstallationController;
use App\Http\Controllers\Api\v1\ComplianceController;
use App\Http\Controllers\Api\v1\ClearanceController;
use App\Http\Controllers\Api\v1\ReportingController;



/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::prefix('v1')->group(function () {
// Installation routes (no authentication required)
Route::get('/installation/status', [InstallationController::class, 'status']);
Route::post('/installation/create-user', [InstallationController::class, 'createUser']);
Route::post('/installation/complete', [InstallationController::class, 'complete']);

// Authentication routes
Route::post('/token', [AuthController::class, 'login']);
Route::post('/token/refresh', [AuthController::class, 'refresh']);

Route::middleware(['auth.jwt'])->group(function () {
    // Protected authentication routes (installation not required for logout)
    Route::post('/token/logout', [AuthController::class, 'logout']);

    // Company routes (allowed during installation)
    Route::get('/company', [CompanyController::class, 'index']);
    Route::post('/company', [CompanyController::class, 'store']);
});

Route::middleware(['auth.jwt','auth.company', 'installation.required'])->group(function () {
    Route::get('/company/{slug}', [CompanyController::class, 'show']);
    Route::put('/company/{slug}', [CompanyController::class, 'update']);
    Route::delete('/company/{slug}', [CompanyController::class, 'destroy']);
    Route::post('/company/{slug}/regenerate-api-key', [CompanyController::class, 'regenerateApiKey']);
});



Route::middleware(['installation.required', 'auth.jwt', 'auth.company', 'auth.location'])->group(function () {

    Route::post('/{environment}/compliance', [ComplianceController::class, 'processCompliance'])
        ->where('environment', 'sandbox|simulation|production');

    Route::post('/{environment}/clearance/', [ClearanceController::class, 'processClearance'])
        ->where('environment', 'sandbox|simulation|production')
        ->middleware('zatca.prevalidation:invoice');
//    Route::post('/{environment}/clearance', [ClearanceController::class, 'processClearance'])
//        ->where('environment', 'sandbox|simulation|production')
//        ->middleware('zatca.prevalidation:invoice');

    Route::post('/{environment}/reporting/', [ReportingController::class, 'processReporting'])
        ->where('environment', 'sandbox|simulation|production');
//    Route::post('/{environment}/reporting', [ReportingController::class, 'processReporting'])
//        ->where('environment', 'sandbox|simulation|production');

});

Route::middleware(['auth.company','auth.jwt'])->group(function () {

    Route::get('/locations/', [BusinessLocationController::class, 'index']);
    Route::get('/locations', [BusinessLocationController::class, 'index']);
    Route::post('/locations/', [BusinessLocationController::class, 'store']);
    Route::post('/locations', [BusinessLocationController::class, 'store']);
    Route::get('/locations/{id}/', [BusinessLocationController::class, 'show']);
    Route::get('/locations/{id}', [BusinessLocationController::class, 'show']);
    Route::put('/locations/{id}/', [BusinessLocationController::class, 'update']);
    Route::put('/locations/{id}', [BusinessLocationController::class, 'update']);
    Route::patch('/locations/{id}/', [BusinessLocationController::class, 'update']);
    Route::patch('/locations/{id}', [BusinessLocationController::class, 'update']);
    Route::delete('/locations/{id}/', [BusinessLocationController::class, 'destroy']);
    Route::delete('/locations/{id}', [BusinessLocationController::class, 'destroy']);
    Route::patch('/{id}/{environment}/csid/', [ComplianceController::class, 'generateCsid'])
        ->where('environment', 'sandbox|simulation|production');
    Route::patch('/{id}/{environment}/csid', [ComplianceController::class, 'generateCsid'])
        ->where('environment', 'sandbox|simulation|production');

    Route::patch('/{id}/{environment}/x509/', [ComplianceController::class, 'generateX509'])
        ->where('environment', 'sandbox|simulation|production');
    Route::patch('/{id}/{environment}/x509', [ComplianceController::class, 'generateX509'])
        ->where('environment', 'sandbox|simulation|production');
});

// ZATCA Compliance API - Complete compliance check with certificate management
Route::middleware(['auth.jwt','auth.company','auth.location'])->group(function () {
    Route::post('/{locationId}/{environment}/compliance-check', [App\Http\Controllers\Api\v1\ComplianceController::class, 'complianceCheck'])
        ->where('locationId', '[0-9]+')
        ->where('environment', 'sandbox|simulation|production');
    Route::post('/locations/regenerate-secret/{id}', [BusinessLocationController::class, 'regenerateToken']);
});


Route::get('/{location_id}/sandbox/x509/', [App\Http\Controllers\Api\v1\X509CertificateController::class, 'getCertificate'])
    ->where('location_id', '[0-9]+');
Route::get('/{location_id}/sandbox/x509', [App\Http\Controllers\Api\v1\X509CertificateController::class, 'getCertificate'])
    ->where('location_id', '[0-9]+');
Route::get('/{location_id}/sandbox/x509/info', [App\Http\Controllers\Api\v1\X509CertificateController::class, 'getCertificateInfo'])
    ->where('location_id', '[0-9]+');

Route::prefix('zatca')->group(function () {
    Route::post('/generate-xml', [App\Http\Controllers\Api\v1\XmlGeneratorController::class, 'generateXml']);
    Route::get('/sample-payload', [App\Http\Controllers\Api\v1\XmlGeneratorController::class, 'generateSamplePayload']);
});

});


