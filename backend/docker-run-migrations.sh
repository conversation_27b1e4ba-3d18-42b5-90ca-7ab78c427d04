#!/bin/bash

# Run Laravel migrations in Docker container
docker run --rm \
  --network laravel-zatca_zatca-network \
  -v "$(pwd):/var/www/html" \
  -w /var/www/html \
  -e DB_CONNECTION=mysql \
  -e DB_HOST=mariadb \
  -e DB_PORT=3306 \
  -e DB_DATABASE=zatca_einvoicing \
  -e DB_USERNAME=zatca_user \
  -e DB_PASSWORD=zatca_password \
  php:8.4-cli \
  bash -c "
    # Install required PHP extensions
    apt-get update && apt-get install -y \
      libzip-dev \
      zip \
      unzip \
      git \
      curl \
      libpng-dev \
      libonig-dev \
      libxml2-dev \
      && docker-php-ext-install pdo_mysql zip mbstring \
    
    # Install Composer
    curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
    
    # Install dependencies
    composer install --no-dev --optimize-autoloader
    
    # Run migrations
    php artisan migrate --force
    
    # Generate Swagger documentation
    php artisan l5-swagger:generate
  "
