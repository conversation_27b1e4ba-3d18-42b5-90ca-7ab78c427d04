<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Rules\TaxNumberRule;

/**
 * Simple test script to demonstrate tax number validation
 * Run with: php test_tax_validation.php
 */

echo "=== Tax Number Validation Test ===\n\n";

$testCases = [
    // Valid cases
    ['312345678901233', 'Valid 15-digit number starting and ending with 3'],
    ['300000000000003', 'Valid with zeros in middle'],
    ['399999999999993', 'Valid with nines in middle'],
    [null, 'Null value (allowed)'],
    ['', 'Empty string (allowed)'],
    
    // Invalid cases with letters
    ['32345678901234a', 'Contains lowercase letter'],
    ['3234567890123A3', 'Contains uppercase letter'],
    ['3234ABC78901233', 'Contains multiple letters'],
    
    // Invalid cases with symbols/spaces
    ['323456789012-43', 'Contains hyphen'],
    ['323456789012 43', 'Contains space'],
    ['323456789012.43', 'Contains period'],
    ['323456789012#43', 'Contains hash symbol'],
    
    // Invalid length cases
    ['3234567890123', 'Too short (13 digits)'],
    ['32345678901234', 'Too short (14 digits)'],
    ['3234567890123456', 'Too long (16 digits)'],
    ['32345678901234567', 'Too long (17 digits)'],

    // Invalid start/end digit cases
    ['123456789012345', 'Does not start with 3'],
    ['312345678901234', 'Does not end with 3'],
    ['412345678901233', 'Starts with 4 instead of 3'],
    ['312345678901232', 'Ends with 2 instead of 3'],
    
    // Edge cases
    [312345678901233, 'Integer instead of string'],
    ['300000000000003', 'All zeros in middle (valid format)'],
    ['399999999999993', 'All nines in middle (valid format)'],
    ['333333333333333', 'All threes (valid format)'],
];

foreach ($testCases as [$value, $description]) {
    echo "Testing: $description\n";
    echo "Value: " . (is_null($value) ? 'NULL' : (is_string($value) ? "'$value'" : $value)) . "\n";
    
    $rule = new TaxNumberRule();
    $passed = true;
    $errorMessage = '';
    
    $rule->validate('tax_no', $value, function ($message) use (&$passed, &$errorMessage) {
        $passed = false;
        $errorMessage = $message;
    });
    
    if ($passed) {
        echo "Result: ✅ PASSED\n";
    } else {
        echo "Result: ❌ FAILED\n";
        echo "Error: $errorMessage\n";
    }
    
    echo str_repeat('-', 50) . "\n";
}

echo "\n=== Summary ===\n";
echo "The TaxNumberRule now provides specific error messages for:\n";
echo "1. Letters in tax number\n";
echo "2. Symbols and special characters\n";
echo "3. Incorrect length with current digit count\n";
echo "4. Must start and end with digit 3\n";
echo "5. Type validation\n";
echo "6. Uniqueness validation\n";
