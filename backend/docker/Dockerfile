# Use official PHP 8.4 with Apache
FROM php:8.4-apache

# Set working directory
WORKDIR /var/www/html

# Install system dependencies (NO CACHE PLUGINS) + Java 11 for ZATCA SDK
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    build-essential \
    libpq-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    locales \
    jpegoptim optipng pngquant gifsicle \
    vim \
    nano \
    supervisor \
    wget \
    gnupg \
    jq

# Install Java 11 from Eclipse Adoptium (ZATCA SDK requirement: JDK 11-14)
RUN wget -qO - https://packages.adoptium.net/artifactory/api/gpg/key/public | gpg --dearmor | tee /etc/apt/trusted.gpg.d/adoptium.gpg > /dev/null \
    && echo "deb https://packages.adoptium.net/artifactory/deb $(awk -F= '/^VERSION_CODENAME/{print$2}' /etc/os-release) main" | tee /etc/apt/sources.list.d/adoptium.list \
    && apt-get update \
    && apt-get install -y temurin-11-jdk

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install PHP extensions (NO CACHE EXTENSIONS)
RUN docker-php-ext-install \
    pdo_mysql \
    mbstring \
    exif \
    pcntl \
    bcmath \
    gd \
    zip \
    intl

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Enable Apache modules
RUN a2enmod rewrite headers ssl

# Copy Apache configuration
COPY apache-config/000-default.conf /etc/apache2/sites-available/000-default.conf
COPY apache-config/apache2.conf /etc/apache2/apache2.conf

# Set proper permissions for Apache
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Copy application code
COPY . /var/www/html

# Install PHP dependencies
RUN composer update --no-dev --optimize-autoloader

# Create necessary directories
RUN mkdir -p /var/www/html/storage/api-docs \
    && mkdir -p /var/www/html/storage/logs \
    && mkdir -p /var/www/html/bootstrap/cache

# Set ultra-efficient permissions - only on directories that need special permissions
RUN chown -R www-data:www-data storage bootstrap/cache \
    && find storage -type d -exec chmod 755 {} \; \
    && find storage -type f -exec chmod 644 {} \; \
    && find bootstrap/cache -type d -exec chmod 755 {} \; \
    && find bootstrap/cache -type f -exec chmod 644 {} \; \
    && chmod -R 775 storage \
    && chmod -R 775 bootstrap/cache

# Create PHP configuration for Laravel performance (NO CACHE CONFIGS)
RUN echo "memory_limit = 512M" >> /usr/local/etc/php/conf.d/docker-php-memlimit.ini \
    && echo "upload_max_filesize = 100M" >> /usr/local/etc/php/conf.d/docker-php-uploads.ini \
    && echo "post_max_size = 100M" >> /usr/local/etc/php/conf.d/docker-php-uploads.ini \
    && echo "max_execution_time = 300" >> /usr/local/etc/php/conf.d/docker-php-time.ini \
    && echo "max_input_vars = 3000" >> /usr/local/etc/php/conf.d/docker-php-vars.ini

# Professional ZATCA FATOORA SDK Setup (Java 11 as per ZATCA requirements)
ENV FATOORA_HOME=/var/www/html/storage/app/zatca-sdk/zatca-einvoicing-sdk-Java-238-R3.4.1/Apps
ENV PATH="${PATH}:${FATOORA_HOME}"
ENV JAVA_HOME=/usr/lib/jvm/temurin-11-jdk-amd64

# Setup FATOORA SDK with proper permissions and validation
RUN if [ -d "/var/www/html/storage/app/zatca-sdk/zatca-einvoicing-sdk-Java-238-R3.4.1" ]; then \
        echo "Setting up ZATCA FATOORA SDK..."; \
        cd /var/www/html/storage/app/zatca-sdk/zatca-einvoicing-sdk-Java-238-R3.4.1; \
        chmod +x Apps/fatoora; \
        chmod +x install.sh; \
        echo "FATOORA SDK setup completed"; \
        echo "Java version: $(java -version 2>&1 | head -1)"; \
        echo "FATOORA_HOME: ${FATOORA_HOME}"; \
        echo "Testing FATOORA command availability..."; \
        if [ -f "${FATOORA_HOME}/fatoora" ]; then \
            echo "✅ FATOORA command found at ${FATOORA_HOME}/fatoora"; \
        else \
            echo "❌ FATOORA command not found"; \
        fi; \
    else \
        echo "⚠️  ZATCA SDK directory not found - will be mounted at runtime"; \
    fi

# Create ultra-fast startup script with minimal permission operations
RUN echo '#!/bin/bash\n\
# Ensure critical directories exist\n\
mkdir -p /var/www/html/storage/api-docs\n\
mkdir -p /var/www/html/bootstrap/cache\n\
\n\
# Permission fix \n\
cd /var/www/html\n\
chown -R www-data:www-data storage bootstrap/cache\n\
find storage -type d -exec chmod 755 {} \\;\n\
find storage -type f -exec chmod 644 {} \\;\n\
chmod -R 775 storage\n\
\n\
# Start Apache\n\
exec apache2-foreground\n\
' > /usr/local/bin/start-laravel.sh \
    && chmod +x /usr/local/bin/start-laravel.sh

# Expose port 80
EXPOSE 80

# Use our startup script
CMD ["/usr/local/bin/start-laravel.sh"]
