# ZATCA Module Enablement

## Overview

The ZATCA validations module can now be enabled or disabled at runtime using a database setting. When disabled, all ZATCA validations are skipped, allowing requests to proceed directly to controllers without validation overhead.

## Implementation

### 1. Database Setting

The module enablement is controlled by the `zatca_validations_enabled` setting in the `settings` table:

```sql
INSERT INTO settings (key, value, type, description) VALUES 
('zatca_validations_enabled', 'true', 'boolean', 'Enable/disable ZATCA validations module');
```

### 2. Setting Model Methods

Added convenience methods to the `Setting` model:

```php
// Check if ZATCA validations module is enabled
Setting::isZatcaValidationsEnabled(): bool

// Enable/disable ZATCA validations module  
Setting::setZatcaValidationsEnabled(bool $enabled): void
```

### 3. Middleware Integration

The `ZatcaPreValidationMiddleware` now checks the module status before performing validations:

```php
public function handle(Request $request, Closure $next, string $validationType = 'invoice')
{
    // Check if ZATCA validations module is enabled
    if (!Setting::isZatcaValidationsEnabled()) {
        return $next($request);
    }
    
    // Continue with normal validation logic...
}
```

### 4. Controller Integration

Controllers that perform enhanced ZATCA validation also check the module status:

```php
// Enhanced ZATCA validation before processing (only if module is enabled)
if (Setting::isZatcaValidationsEnabled()) {
    $this->performEnhancedValidation($request);
}
```

## API Endpoints

### Get ZATCA Validations Status
```http
GET /api/settings/zatca-validations
Authorization: Bearer {jwt_token}
x-api-key: {company_api_key}
```

**Response:**
```json
{
    "status": "success",
    "httpStatus": 200,
    "statusCode": 20001,
    "data": {
        "zatca_validations_enabled": true,
        "description": "ZATCA validations module status"
    }
}
```

### Set ZATCA Validations Status
```http
PUT /api/settings/zatca-validations
Authorization: Bearer {jwt_token}
x-api-key: {company_api_key}
Content-Type: application/json

{
    "enabled": false
}
```

**Response:**
```json
{
    "status": "success",
    "httpStatus": 200,
    "statusCode": 20002,
    "message": "ZATCA validations module disabled successfully",
    "data": {
        "zatca_validations_enabled": false
    }
}
```

## Behavior

### When ZATCA Validations are ENABLED (default)
- ✅ Full ZATCA validation rules are applied
- ✅ Business rules validation is performed
- ✅ VAT number checksum validation occurs
- ✅ Invoice structure validation is enforced
- ❌ Invalid requests are rejected with detailed error messages

### When ZATCA Validations are DISABLED
- ✅ Requests bypass ZATCA validation middleware
- ✅ Requests proceed directly to controllers
- ✅ Basic Laravel validation still applies (if any)
- ✅ Faster processing for non-ZATCA use cases
- ⚠️ No ZATCA compliance validation

## Use Cases

### Development/Testing
```bash
# Disable validations for faster testing
curl -X PUT /api/settings/zatca-validations \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "x-api-key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"enabled": false}'
```

### Production Deployment
```bash
# Ensure validations are enabled for compliance
curl -X PUT /api/settings/zatca-validations \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -H "x-api-key: $API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"enabled": true}'
```

## Security Considerations

- ⚠️ **Important**: Only disable ZATCA validations in non-production environments or during maintenance
- ✅ API endpoints require JWT authentication and company API key
- ✅ Setting changes are logged in the database with timestamps
- ✅ Default state is ENABLED for compliance

## Files Modified

1. **Middleware**: `ZatcaPreValidationMiddleware.php`
2. **Controller**: `ClearanceController.php`
3. **Model**: `Setting.php`
4. **Routes**: `api.php`
5. **Migration**: `2025_06_28_000003_add_zatca_validations_setting.php`
6. **New Controller**: `SettingsController.php`

The ZATCA validations module can now be controlled dynamically without code changes or server restarts.
