<?php

namespace $CLASS_NAMESPACE$;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class $CLASS$ extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return view('$LOWER_NAME$::index');
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('$LOWER_NAME$::create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request) {}

    /**
     * Show the specified resource.
     */
    public function show($id)
    {
        return view('$LOWER_NAME$::show');
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($id)
    {
        return view('$LOWER_NAME$::edit');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id) {}

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id) {}
}
