<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\BusinessLocation;

/**
 * Tax Number Validation Rule
 *
 * Validates tax numbers for business locations according to requirements:
 * - Must be exactly 15 digits
 * - Must be numeric only
 * - Must start and end with the digit 3
 * - Must be unique across all business locations
 */
class TaxNumberRule implements ValidationRule
{
    private ?int $excludeId;
    
    /**
     * Create a new rule instance.
     * 
     * @param int|null $excludeId ID to exclude from uniqueness check (for updates)
     */
    public function __construct(?int $excludeId = null)
    {
        $this->excludeId = $excludeId;
    }
    
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Allow null/empty values since tax_no is nullable
        if (is_null($value) || $value === '') {
            return;
        }
        
        // Check if value is a string
        if (!is_string($value)) {
            $fail('The :attribute must be a string.');
            return;
        }
        
        // Check for specific types of invalid characters
        if (preg_match('/[a-zA-Z]/', $value)) {
            $fail('The :attribute cannot contain letters. Only numeric digits (0-9) are allowed.');
            return;
        }

        // Check for other non-numeric characters (symbols, spaces, etc.)
        if (!ctype_digit($value)) {
            $fail('The :attribute must contain only numeric digits (0-9). No spaces, symbols, or special characters are allowed.');
            return;
        }

        // Check if tax number is exactly 15 digits
        if (strlen($value) !== 15) {
            if (strlen($value) < 15) {
                $fail('The :attribute must be exactly 15 digits. Currently has ' . strlen($value) . ' digits.');
            } else {
                $fail('The :attribute must be exactly 15 digits. Currently has ' . strlen($value) . ' digits.');
            }
            return;
        }

        // Check if tax number starts and ends with 3
        if (!preg_match('/^3.*3$/', $value)) {
            $fail('The :attribute must start and end with the digit 3.');
            return;
        }

        // Check uniqueness across business locations
        if (!$this->isUnique($value)) {
            $fail('The :attribute has already been taken.');
            return;
        }
    }
    
    /**
     * Check if the tax number is unique
     */
    private function isUnique(string $taxNumber): bool
    {
        $query = BusinessLocation::where('tax_no', $taxNumber);
        
        // Exclude current record if updating
        if ($this->excludeId) {
            $query->where('id', '!=', $this->excludeId);
        }
        
        return !$query->exists();
    }
}
