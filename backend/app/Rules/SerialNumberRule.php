<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use App\Models\BusinessLocation;

/**
 * Serial Number Validation Rule
 * 
 * Validates serial numbers for business locations according to requirements:
 * - Must be unique across all business locations
 * - Can be nullable/empty
 * - String validation with max length
 */
class SerialNumberRule implements ValidationRule
{
    private ?int $excludeId;
    
    /**
     * Create a new rule instance.
     * 
     * @param int|null $excludeId ID to exclude from uniqueness check (for updates)
     */
    public function __construct(?int $excludeId = null)
    {
        $this->excludeId = $excludeId;
    }
    
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // Allow null/empty values since serial_number is nullable
        if (is_null($value) || $value === '') {
            return;
        }
        
        // Check if value is a string
        if (!is_string($value)) {
            $fail('The :attribute must be a string.');
            return;
        }
        
        // Check maximum length (230 characters as per database schema)
        if (strlen($value) > 230) {
            $fail('The :attribute must not exceed 230 characters.');
            return;
        }
        
        // Check uniqueness across business locations
        if (!$this->isUnique($value)) {
            $fail('The :attribute has already been taken.');
            return;
        }
    }
    
    /**
     * Check if the serial number is unique
     */
    private function isUnique(string $serialNumber): bool
    {
        $query = BusinessLocation::where('serial_number', $serialNumber);
        
        // Exclude current record if updating
        if ($this->excludeId) {
            $query->where('id', '!=', $this->excludeId);
        }
        
        return !$query->exists();
    }
}
