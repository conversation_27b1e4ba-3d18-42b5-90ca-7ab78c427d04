<?php

/*
ZatcaNext - ZATCA e-Invoicing Middleware for Phase 2 Compliance

Copyright (c) 2025 ZatcaNext. All rights reserved.

This software is the proprietary property of ZatcaNext and is protected
by copyright and other intellectual property laws. Unauthorized use,
reproduction, modification, distribution, or reverse-engineering of this
software, in whole or in part, is strictly prohibited without express
written permission from ZatcaNext.

This software is provided "as is" without warranties of any kind, express
or implied. ZatcaNext disclaims all liability for damages arising from its use.

For support or licensing inquiries, contact: <EMAIL>
Website: https://zatcanext.com
Terms of Use: https://zatcanext.com/terms
*/

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

/**
 *
 *
 * @property string $id UUID primary key
 * @property string $name Company name (max 100 chars)
 * @property string|null $api_key Auto-generated API key
 * @property string|null $slug Auto-generated slug
 */
class Company extends Model
{
    use HasFactory;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'name',
        'api_key',
        'slug',
        'vat_number',
    ];

    protected $casts = [
        'id' => 'string',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
            if (empty($model->slug)) {
                $model->slug = (string) Str::uuid();
            }
            if (empty($model->api_key)) {
                $model->api_key = Str::random(32);
            }
        });
    }

    /**
     * Get the business locations for the company
     */
    public function locations(): HasMany
    {
        return $this->hasMany(BusinessLocation::class, 'company_id', 'id');
    }

    /**
     * Get the business locations for the company
     */
    public function location(): HasMany
    {
        return $this->locations();
    }

    /**
     * String representation
     */
    public function __toString(): string
    {
        return $this->name;
    }
    
    public function regenerateApiKey(): string
    {
        $newApiKey = Str::random(32);
        $this->update(['api_key' => $newApiKey]);
        return $newApiKey;
    }
}
