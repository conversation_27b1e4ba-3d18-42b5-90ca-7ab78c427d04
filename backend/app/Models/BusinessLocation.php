<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

/**
 * BusinessLocation Model
 *
 *
 * @property int $id
 * @property string $company_id Foreign key to Company
 * @property string|null $authentication_token Auto-generated token
 * @property string|null $seller_name
 * @property string|null $tax_no
 * @property string|null $common_name
 * @property string|null $organisation
 * @property string|null $organisation_unit
 * @property string|null $serial_number
 * @property string|null $title
 * @property string|null $registered_address
 * @property string|null $business_category
 * @property string|null $otp
 */
class BusinessLocation extends Model
{
    use HasFactory;

    protected $fillable = [
        'company_id',
        'authentication_token',
        'seller_name',
        'tax_no',
        'common_name',
        'organisation',
        'organisation_unit',
        'serial_number',
        'title',
        'registered_address',
        'business_category',
        'otp',
    ];

    protected $casts = [
        'company_id' => 'string', // UUID foreign key
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->authentication_token)) {
                $model->authentication_token = Str::random(64);
            }
        });

        static::created(function ($model) {
            $model->createEnvironmentRecords();
        });
    }

    /**
     * Get the company that owns this location
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    /**
     * Get the sandbox environment for this location
     */
    public function sandbox(): HasOne
    {
        return $this->hasOne(Sandbox::class, 'business_location_id', 'id');
    }

    /**
     * Get the simulation environment for this location
     */
    public function simulation(): HasOne
    {
        return $this->hasOne(Simulation::class, 'business_location_id', 'id');
    }

    /**
     * Get the production environment for this location
     */
    public function production(): HasOne
    {
        return $this->hasOne(Production::class, 'business_location_id', 'id');
    }

    /**
     * Create environment records (Sandbox, Simulation, Production) with CSR data

     */
    public function createEnvironmentRecords(): void
    {
        try {
            $csrService = app(\App\Services\CsrGenerationService::class);

            // Prepare location data for CSR generation
            $locationData = [
                'organisation' => $this->organisation ?? 'Default Organization',
                'organisation_unit' => $this->organisation_unit ?? 'Default Unit',
                'common_name' => $this->common_name ?? 'Default Common Name',
                'serial_number' => $this->serial_number ?? '1-default|2-version|3-uuid',
                'tax_no' => $this->tax_no ?? '000000000000000',
                'title' => $this->title ?? '1100',
                'registered_address' => $this->registered_address ?? 'Default Address',
                'business_category' => $this->business_category ?? 'Default Category',
            ];

            // Generate CSR data for each environment
            $sandboxCsr = $csrService->generateCsr($locationData, 'ZATCA-Code-Signing');
            $simulationCsr = $csrService->generateCsr($locationData, 'ZATCA-Code-Signing');
            $productionCsr = $csrService->generateCsr($locationData, 'ZATCA-Code-Signing');

            // Create Sandbox record
            Sandbox::create([
                'business_location_id' => $this->id,
                'private_key' => $sandboxCsr['pvt'],
                'public_key' => $sandboxCsr['pbl'],
                'csr' => $sandboxCsr['csr']
            ]);

            // Create Simulation record
            Simulation::create([
                'business_location_id' => $this->id,
                'private_key' => $simulationCsr['pvt'],
                'public_key' => $simulationCsr['pbl'],
                'csr' => $simulationCsr['csr']
            ]);

            // Create Production record
            Production::create([
                'business_location_id' => $this->id,
                'private_key' => $productionCsr['pvt'],
                'public_key' => $productionCsr['pbl'],
                'csr' => $productionCsr['csr']
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to create environment records for location: ' . $e->getMessage());
            // Don't throw exception to avoid breaking location creation
        }
    }

    /**
     * String representation
     */
    public function __toString(): string
    {
        return $this->common_name ?? 'Business Location';
    }

    public function regenerateApiKey(): string
    {
        $newApiKey = Str::random(64);
        $this->update(['authentication_token' => $newApiKey]);
        return $newApiKey;
    }
}
