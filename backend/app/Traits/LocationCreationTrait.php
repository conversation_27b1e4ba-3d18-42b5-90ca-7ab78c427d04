<?php

namespace App\Traits;

use App\Models\BusinessLocation;
use App\Models\Company;
use Illuminate\Support\Facades\Validator;
use Exception;

trait LocationCreationTrait
{
    /**
     * Create a new business location with validation
     *
     * @param array $locationData
     * @param Company $company
     * @return BusinessLocation
     * @throws Exception
     */
    protected function createLocation(array $locationData, Company $company): BusinessLocation
    {
        // Validate location data
        $validator = Validator::make($locationData, [
            'seller_name' => 'nullable|string|max:230',
            'tax_no' => 'nullable|string|max:16',
            'common_name' => 'nullable|string|max:230',
            'organisation' => 'nullable|string|max:230',
            'organisation_unit' => 'nullable|string|max:230',
            'serial_number' => 'nullable|string|max:230',
            'title' => 'nullable|string|max:230',
            'registered_address' => 'nullable|string|max:230',
            'business_category' => 'nullable|string|max:230',
            'otp' => 'nullable|string|max:230',
        ]);

        if ($validator->fails()) {
            throw new Exception('Location validation failed: ' . $validator->errors()->first());
        }

        try {
            // Create the business location
            $location = BusinessLocation::create([
                'company_id' => $company->id,
                'seller_name' => $locationData['seller_name'] ?? null,
                'tax_no' => $locationData['tax_no'] ?? null,
                'common_name' => $locationData['common_name'] ?? null,
                'organisation' => $locationData['organisation'] ?? null,
                'organisation_unit' => $locationData['organisation_unit'] ?? null,
                'serial_number' => $locationData['serial_number'] ?? null,
                'title' => $locationData['title'] ?? null,
                'registered_address' => $locationData['registered_address'] ?? null,
                'business_category' => $locationData['business_category'] ?? null,
                'otp' => $locationData['otp'] ?? null,
            ]);

            return $location;

        } catch (Exception $e) {
            throw new Exception('Failed to create location: ' . $e->getMessage());
        }
    }

    /**
     * Validate location creation data
     *
     * @param array $data
     * @return array
     */
    protected function validateLocationData(array $data): array
    {
        // All location fields are optional, so we just filter and return valid fields
        $validFields = [
            'seller_name',
            'tax_no',
            'common_name',
            'organisation',
            'organisation_unit',
            'serial_number',
            'title',
            'registered_address',
            'business_category',
            'otp',
        ];

        $locationData = [];
        foreach ($validFields as $field) {
            if (isset($data[$field])) {
                $locationData[$field] = $data[$field];
            }
        }

        return $locationData;
    }

    /**
     * Format location response data
     *
     * @param BusinessLocation $location
     * @return array
     */
    protected function formatLocationResponse(BusinessLocation $location): array
    {
        return [
            'id' => $location->id,
            'company_id' => $location->company_id,
            'authentication_token' => $location->authentication_token,
            'seller_name' => $location->seller_name,
            'tax_no' => $location->tax_no,
            'common_name' => $location->common_name,
            'organisation' => $location->organisation,
            'organisation_unit' => $location->organisation_unit,
            'serial_number' => $location->serial_number,
            'title' => $location->title,
            'registered_address' => $location->registered_address,
            'business_category' => $location->business_category,
            'created_at' => $location->created_at,
        ];
    }
}
