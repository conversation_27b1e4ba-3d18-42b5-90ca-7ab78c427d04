<?php

namespace App\Traits;

use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Exception;

trait UserCreationTrait
{
    /**
     * Create a new user with validation
     *
     * @param array $userData
     * @return User
     * @throws Exception
     */
    protected function createUserRecord(array $userData): User
    {
        // Validate user data
        $validator = Validator::make($userData, [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            throw new Exception('User validation failed: ' . $validator->errors()->first());
        }

        // Check if user already exists (additional safety check)
        if (User::count() > 0) {
            throw new Exception('User already exists in the system');
        }

        try {
            // Create the user
            $user = User::create([
                'name' => $userData['name'],
                'email' => $userData['email'],
                'password' => Hash::make($userData['password']),
                'email_verified_at' => now(),
            ]);

            return $user;

        } catch (Exception $e) {
            throw new Exception('Failed to create user: ' . $e->getMessage());
        }
    }

    /**
     * Validate user creation data
     *
     * @param array $data
     * @return array
     * @throws Exception
     */
    protected function validateUserData(array $data): array
    {
        $requiredFields = ['name', 'email', 'password', 'password_confirmation'];
        
        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                throw new Exception("User field '{$field}' is required");
            }
        }

        // Validate password confirmation
        if ($data['password'] !== $data['password_confirmation']) {
            throw new Exception('Password confirmation does not match');
        }

        return [
            'name' => $data['name'],
            'email' => $data['email'],
            'password' => $data['password'],
            'password_confirmation' => $data['password_confirmation'],
        ];
    }

    /**
     * Format user response data
     *
     * @param User $user
     * @return array
     */
    protected function formatUserResponse(User $user): array
    {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email,
            'created_at' => $user->created_at,
        ];
    }
}
