<?php

namespace App\Traits;

use App\Models\Company;
use Illuminate\Support\Facades\Validator;
use Exception;

trait CompanyCreationTrait
{
    /**
     * Create a new company with validation
     *
     * @param array $companyData
     * @return Company
     * @throws Exception
     */
    protected function createCompany(array $companyData): Company
    {
        // Validate company data
        $validator = Validator::make($companyData, [
            'name' => 'required|string|max:100',
        ]);

        if ($validator->fails()) {
            throw new Exception('Company validation failed: ' . $validator->errors()->first());
        }

        try {
            // Create the company
            $company = Company::create([
                'name' => $companyData['name'],
            ]);

            return $company;

        } catch (Exception $e) {
            throw new Exception('Failed to create company: ' . $e->getMessage());
        }
    }

    /**
     * Validate company creation data
     *
     * @param array $data
     * @return array
     * @throws Exception
     */
    protected function validateCompanyData(array $data): array
    {
        if (!isset($data['name']) || empty($data['name'])) {
            throw new Exception("Company field 'name' is required");
        }

        return [
            'name' => $data['name'],
        ];
    }

    /**
     * Format company response data
     *
     * @param Company $company
     * @return array
     */
    protected function formatCompanyResponse(Company $company): array
    {
        return [
            'id' => $company->id,
            'name' => $company->name,
            'slug' => $company->slug,
            'api_key' => $company->api_key,
            'created_at' => $company->created_at,
        ];
    }
}
