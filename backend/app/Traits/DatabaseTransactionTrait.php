<?php

namespace App\Traits;

use Illuminate\Support\Facades\DB;
use Exception;

trait DatabaseTransactionTrait
{
    /**
     * Execute a callback within a database transaction
     * Automatically rolls back on any exception
     *
     * @param callable $callback
     * @return mixed
     * @throws Exception
     */
    protected function executeInTransaction(callable $callback)
    {
        DB::beginTransaction();

        try {
            $result = $callback();
            DB::commit();
            return $result;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Execute multiple operations in a transaction with detailed error handling
     *
     * @param array $operations Array of callable operations
     * @param string $operationName Name for error reporting
     * @return array Results from all operations
     * @throws Exception
     */
    protected function executeMultipleInTransaction(array $operations, string $operationName = 'operation'): array
    {
        return $this->executeInTransaction(function () use ($operations, $operationName) {
            $results = [];
            
            foreach ($operations as $index => $operation) {
                try {
                    $results[] = $operation();
                } catch (Exception $e) {
                    throw new Exception("Failed at step " . ($index + 1) . " of {$operationName}: " . $e->getMessage());
                }
            }
            
            return $results;
        });
    }

    /**
     * Safely execute an operation with transaction support
     *
     * @param callable $operation
     * @param string $errorMessage
     * @return mixed
     * @throws Exception
     */
    protected function safeExecute(callable $operation, string $errorMessage = 'Operation failed')
    {
        try {
            return $this->executeInTransaction($operation);
        } catch (Exception $e) {
            throw new Exception($errorMessage . ': ' . $e->getMessage());
        }
    }
}
