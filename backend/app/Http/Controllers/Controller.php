<?php

namespace App\Http\Controllers;

/**
 * @OA\Info(
 *     title="Laravel ZATCA API",
 *     version="1.0.0",
 *     description="Comprehensive Laravel ZATCA API",
 *     @OA\Contact(
 *         email="<EMAIL>"
 *     )
 * )
 * @OA\Server(
 *     url="http://localhost:8080",
 *     description="Laravel ZATCA Development Server"
 * )
 * @OA\SecurityScheme(
 *     securityScheme="BearerAuth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     description="JWT Bearer token authentication"
 * )
 * @OA\SecurityScheme(
 *     securityScheme="ApiKeyAuth",
 *     type="apiKey",
 *     in="header",
 *     name="Authorization",
 *     description="API Key authentication using 'Api-Key {key}' format"
 * )
 * @OA\Tag(
 *     name="🔐 Authentication",
 *     description="JWT token authentication endpoints"
 * )
 * @OA\Tag(
 *     name="🏢 Company Management",
 *     description="Company creation and management endpoints"
 * )
 * @OA\Tag(
 *     name="📍 Location Management",
 *     description="Business location management endpoints"
 * )
 * @OA\Tag(
 *     name="🔒 ZATCA Compliance API",
 *     description="ZATCA e-invoicing compliance endpoints"
 * )
 *
 * Base Controller Class
 */
abstract class Controller
{
    //
}
