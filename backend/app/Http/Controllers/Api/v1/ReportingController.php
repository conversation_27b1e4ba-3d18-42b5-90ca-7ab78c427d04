<?php

namespace App\Http\Controllers\Api\v1;

use App\Constants\ZatcaConstants;
use App\Http\Controllers\Controller;
use App\Models\BusinessLocation;
use App\Services\ZatcaComplianceService;
use App\Services\ZatcaXmlSigningService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Exception;
use RuntimeException;

/**
 * ZATCA Reporting API Controller
 *
 * Professional implementation for ZATCA reporting operations.
 * Supports all environments (sandbox/simulation/production).
 * Uses X509 certificates for authentication .
 *
 * @OA\Tag(
 *     name="ZATCA Reporting",
 *     description="ZATCA Reporting API endpoints"
 * )
 */
class ReportingController extends Controller
{
    private ZatcaComplianceService $complianceService;
    private ZatcaXmlSigningService $signingService;

    public function __construct(
        ZatcaComplianceService $complianceService,
        ZatcaXmlSigningService $signingService
    ) {
        $this->complianceService = $complianceService;
        $this->signingService = $signingService;
    }

    /**
     * Universal Reporting Endpoint
     *
     * Processes simplified invoice reporting for all environments.
     *
     * @OA\Post(
     *     path="/api/{environment}/reporting/",
     *     summary="Process ZATCA Reporting",
     *     description="Submit simplified invoices for ZATCA reporting",
     *     operationId="processReporting",
     *     tags={"ZATCA Reporting"},
     *     security={{"ApiKeyAuth": {}}},
     *     @OA\Parameter(
     *         name="environment",
     *         in="path",
     *         required=true,
     *         description="ZATCA Environment",
     *         @OA\Schema(type="string", enum={"sandbox", "simulation", "production"})
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Invoice data for reporting",
     *         @OA\JsonContent(
     *             required={"invoice", "profileID", "id", "uuid"},
     *             @OA\Property(
     *                 property="invoice",
     *                 type="object",
     *                 required={"invoiceType", "documentType"},
     *                 @OA\Property(property="invoiceType", type="string", enum={"simplified"}, example="simplified"),
     *                 @OA\Property(property="documentType", type="string", enum={"invoice", "credit_note", "debit_note"}, example="invoice")
     *             ),
     *             @OA\Property(property="profileID", type="string", example="reporting:1.0"),
     *             @OA\Property(property="id", type="string", example="12345"),
     *             @OA\Property(property="uuid", type="string", example="cc1572b1-798b-429e-9604-d2bf63b2233b")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Reporting successful",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string", example="200"),
     *             @OA\Property(property="Message", type="string", example="Success"),
     *             @OA\Property(property="invoiceHash", type="string"),
     *             @OA\Property(property="qrcode", type="string"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="reportingStatus", type="string", example="REPORTED")
     *             )
     *         )
     *     )
     * )
     */
    public function processReporting(Request $request, string $environment): JsonResponse
    {
        try {
            $this->validateEnvironment($environment);
            $this->validateReportingRequest($request);

            $location = $this->getAuthenticatedLocation($request);
            $invoiceType = $request->input('invoice.invoiceType');
            $documentType = $request->input('invoice.documentType');

            if ($invoiceType !== 'simplified') {
                throw new RuntimeException('Reporting is only available for simplified invoices');
            }

            return $this->executeReporting($request, $environment, $invoiceType, $documentType, $location);

        } catch (Exception $e) {
            return response()->json(['status'=>'error', 'httpStatus'=>400,'statusCode'=>10024,
                'message' => $e->getMessage()], 400);
        }
    }

    /**
     * Validate environment parameter
     */
    private function validateEnvironment(string $environment): void
    {
        if (!in_array($environment, ZatcaConstants::ENVIRONMENTS)) {
            throw new RuntimeException('Invalid environment: ' . $environment);
        }
    }

    /**
     * Validate reporting request
     */
    private function validateReportingRequest(Request $request): void
    {
        $documentType = $request->input('invoice.documentType');

        // Base validation rules for all simplified invoices
        $rules = [
            'invoice.invoiceType' => 'required|string|in:simplified',
            'invoice.documentType' => 'required|string|in:invoice,credit_note,debit_note',
            'profileID' => 'required|string',
            'id' => 'required|string',
            'uuid' => 'required|string',
            'issueDate' => 'required|string',
            'issueTime' => 'required|string',
            'additionalDocumentReference' => 'required|array',
            'accountingSupplierParty' => 'required|array',
            'paymentMeansCode' => 'required|string',
            'actualDeliveryDate' => 'required|string',
            'latestDeliveryDate' => 'required|string',
            'allowanceCharge' => 'required|array',
            'taxAmount' => 'required|string',
            'taxTotal' => 'required|array',
            'legalMonetaryTotal' => 'required|array',
            'invoiceLines' => 'required|array|min:1',
        ];

        // For simplified invoices, accountingCustomerParty is optional (B2C transactions)
        // Only add validation if the field is present
        if ($request->has('accountingCustomerParty')) {
            $rules['accountingCustomerParty'] = 'array';
        }

        // Additional validation for credit/debit notes
        if (in_array($documentType, ['credit_note', 'debit_note'])) {
            $rules['billingReference'] = 'required|array';
            $rules['creditDebitReason'] = 'required|string';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            throw new RuntimeException('Invoice validation failed: ' . implode(', ', $validator->errors()->all()));
        }
    }

    /**
     * Get authenticated location from request
     */
    private function getAuthenticatedLocation(Request $request): BusinessLocation
    {

        $location = $request->attributes->get('authenticated_location');

        if ($location) {
            return $location;
        }

        // Fallback to manual authentication
        $company = $request->attributes->get('authenticated_company');
        $secret = $request->header('secret');

        if (!$company || !$secret) {
            throw new RuntimeException('Location not found');
        }

        $location = $company->locations()
            ->where('authentication_token', $secret)
            ->first();

        if (!$location) {
            throw new RuntimeException('Location not found');
        }

        return $location;
    }

    /**
     * Execute the actual reporting process
     */
    private function executeReporting(Request $request, string $environment, string $invoiceType, string $documentType, BusinessLocation $location): JsonResponse
    {
        try {

            $environmentData = $location->{$environment};

            if (!$environmentData) {
                throw new RuntimeException(ucfirst($environment) . ' environment not found for this location');
            }

            if (!$environmentData->x509_certificate || !$environmentData->x509_secret) {
                throw new RuntimeException('X509 certificates not found for ' . $environment . ' environment');
            }

            $invoiceData = $request->all();
            $invoiceData['profileID'] = ZatcaConstants::PROFILE_REPORTING;

            $xmlContent = $this->complianceService->generateInvoiceXml($invoiceData, $invoiceType, $documentType);

            $signingResult = $this->signingService->signXmlDocument(
                $xmlContent,
                $environmentData->private_key,
                $environmentData->x509_base64
            );

            $reportingData = [
                'invoiceHash' => $signingResult['invoiceHash'],
                'uuid' => $invoiceData['uuid'],
                'invoice' => $signingResult['invoiceXml']
            ];

            $reportingResponse = $this->complianceService->processReporting(
                $reportingData,
                $environmentData->x509_certificate,
                $environmentData->x509_secret,
                $environment
            );

            return response()->json([
                'status' => ZatcaConstants::HTTP_STATUS_SUCCESS,
                'Message' => ZatcaConstants::MESSAGE_SUCCESS,
                'invoiceHash' => $signingResult['invoiceHash'],
                'qrcode' => $signingResult['invoiceQRCode'],
                'data' => $reportingResponse
            ], 200);

        } catch (Exception $e) {
            throw new RuntimeException('Reporting failed: ' . $e->getMessage());
        }
    }
}
