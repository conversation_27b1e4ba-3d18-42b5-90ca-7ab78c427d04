<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\BusinessLocation;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Exception;

/**
 * @OA\Tag(
 *     name="Company Management",
 *     description="Company CRUD operations"
 * )
 */
class CompanyController extends Controller
{
    /**
     * @OA\Get(     *     path="/api/company",
     *     summary="List all companies",
     *     tags={"Company Management"},
     *     security={{"BearerAuth": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="List of companies",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 @OA\Property(property="id", type="string"),
     *                 @OA\Property(property="name", type="string"),
     *                 @OA\Property(property="slug", type="string"),
     *                 @OA\Property(property="api_key", type="string")
     *             )
     *         )
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        $query = Company::query();


        if ($request->has('search')) {
            $search = $request->search;
            $query->where('name', 'like', '%' . $search . '%');
        }

        $companies = $query->get();

        $results = $companies->map(function ($company) {
            return [
                'id' => $company->id,
                'name' => $company->name,
                'slug' => $company->slug,
                'api_key' => $company->api_key,
            ];
        });
        return response()->json(['status'=>'success', 'httpStatus'=>200,'statusCode'=>10008,'data'=>[
            $results
        ]
        ]);

    }

    /**
     * @OA\Post(
     *     path="/api/company",
     *     summary="Create new company",
     *     description="Create a new company with auto-generated UUID and API key",
     *     tags={"Company Management"},
     *     security={{"BearerAuth": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name"},
     *             @OA\Property(property="name", type="string", maxLength=100, example="My Test Company", description="Company name (max 100 characters)")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Company created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="string", format="uuid", example="*************-4b3d-b3eb-59733229ee6f"),
     *             @OA\Property(property="name", type="string", example="My Test Company"),
     *             @OA\Property(property="slug", type="string", format="uuid", example="9b9a8fc3-567d-41cb-a919-8626c5cdf5da"),
     *             @OA\Property(property="api_key", type="string", example="CIcJrc9frcWnqLzb1ojlopT0jtxJvbc7")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="detail", type="string", example="Validation failed"),
     *             @OA\Property(property="errors", type="object", example={"name": {"This field is required."}})
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="detail", type="string", example="Authentication credentials were not provided.")
     *         )
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'detail' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {

            $company = Company::create([
                'name' => $request->name,
            ]);

            // Check if this is during installation process
            $isInstallationCompleted = \App\Models\Setting::isInstallationCompleted();
            $message = $isInstallationCompleted
                ? 'Company created successfully.'
                : 'Success! Company created successfully. Please create a business location now.';

            

             return response()->json(['status'=>'success', 'httpStatus'=>201,'statusCode'=>10009,
                'message'=>'Company created successfully',
                'data'=>[
            'id' => $company->id,
                'name' => $company->name,
                'slug' => $company->slug,
                'api_key' => $company->api_key,
                'message' => $message,
                'next_step' => $isInstallationCompleted ? null : 'Create a business location using POST /api/business-location'
        ]
        ]);

        } catch (Exception $e) {
            return response()->json(['status'=>'error', 'httpStatus'=>500,'statusCode'=>10012,
                'message' => 'Failed to create company: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/company/{slug}",
     *     summary="Get company by slug",
     *     description="Retrieve a specific company by its slug using API key authentication",
     *     tags={"Company Management"},
     *     security={{"ApiKeyAuth": {}}},
     *     @OA\Parameter(
     *         name="slug",
     *         in="path",
     *         required=true,
     *         description="Company slug (UUID)",
     *         @OA\Schema(type="string", format="uuid", example="9b9a8fc3-567d-41cb-a919-8626c5cdf5da")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Company details",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="string", format="uuid", example="*************-4b3d-b3eb-59733229ee6f"),
     *             @OA\Property(property="name", type="string", example="My Test Company"),
     *             @OA\Property(property="slug", type="string", format="uuid", example="9b9a8fc3-567d-41cb-a919-8626c5cdf5da"),
     *             @OA\Property(property="api_key", type="string", example="CIcJrc9frcWnqLzb1ojlopT0jtxJvbc7")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Company not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="detail", type="string", example="Not found.")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="detail", type="string", example="Invalid API key or company not found.")
     *         )
     *     )
     * )
     */
    public function show($slug): JsonResponse
    {

        $company = Company::where('slug', $slug)->first();

        if (!$company) {
            return response()->json(['status'=>'error', 'httpStatus'=>404,'statusCode'=>10006,
                'message' => 'Company details not found. Please check and try again.',
            ], 404);
        }

        return response()->json(['status'=>'success', 'httpStatus'=>200,'statusCode'=>10007,'data'=>[
            'id' => $company->id,
            'name' => $company->name,
            'slug' => $company->slug,
            'api_key' => $company->api_key,
        ]
        ]);
    }

    /**
     * @OA\Put(
     *     path="/api/company/{slug}",
     *     summary="Update company",
     *     description="Update a company's information using API key authentication",
     *     tags={"Company Management"},
     *     security={{"ApiKeyAuth": {}}},
     *     @OA\Parameter(
     *         name="slug",
     *         in="path",
     *         required=true,
     *         description="Company slug (UUID)",
     *         @OA\Schema(type="string", format="uuid", example="9b9a8fc3-567d-41cb-a919-8626c5cdf5da")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="name", type="string", maxLength=100, example="Updated Company Name", description="Updated company name")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Company updated successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="string", format="uuid", example="*************-4b3d-b3eb-59733229ee6f"),
     *             @OA\Property(property="name", type="string", example="Updated Company Name"),
     *             @OA\Property(property="slug", type="string", format="uuid", example="9b9a8fc3-567d-41cb-a919-8626c5cdf5da"),
     *             @OA\Property(property="api_key", type="string", example="CIcJrc9frcWnqLzb1ojlopT0jtxJvbc7")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Company not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="detail", type="string", example="Not found.")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="detail", type="string", example="Validation failed"),
     *             @OA\Property(property="errors", type="object", example={"name": {"This field is required."}})
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="detail", type="string", example="Invalid API key or company not found.")
     *         )
     *     )
     * )
     */
    public function update(Request $request, $slug): JsonResponse
    {
        $company = Company::where('slug', $slug)->first();

        if (!$company) {
             return response()->json(['status'=>'error', 'httpStatus'=>404,'statusCode'=>10006,
                'message' => 'Company details not found. Please check and try again.',
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'detail' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {

            if ($request->has('name')) {
                $company->update(['name' => $request->name]);
            }

        
             return response()->json(['status'=>'success', 'httpStatus'=>200,'statusCode'=>10010,
                'message' => 'Company details updated successfully.','data'=>[
                'id' => $company->id,
                'name' => $company->name,
                'slug' => $company->slug,
                'api_key' => $company->api_key,
            ]
            ], 200);


        } catch (Exception $e) {
            return response()->json(['status'=>'error', 'httpStatus'=>500,'statusCode'=>10011,
                'message' => 'Failed to update company: ' . $e->getMessage()], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/company/{slug}",
     *     summary="Delete company",
     *     description="Delete a company using API key authentication",
     *     tags={"Company Management"},
     *     security={{"ApiKeyAuth": {}}},
     *     @OA\Parameter(
     *         name="slug",
     *         in="path",
     *         required=true,
     *         description="Company slug (UUID)",
     *         @OA\Schema(type="string", format="uuid", example="9b9a8fc3-567d-41cb-a919-8626c5cdf5da")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Company deleted successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Company deleted successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Company not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="detail", type="string", example="Not found.")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="detail", type="string", example="Invalid API key or company not found.")
     *         )
     *     )
     * )
     */
    public function destroy($slug): JsonResponse
    {

        $company = Company::where('slug', $slug)->first();
        if (!$company) {
             return response()->json(['status'=>'error', 'httpStatus'=>404,'statusCode'=>10006,
                'message' => 'Company details not found. Please check and try again.',
            ], 404);
        }

        try {
            $location = BusinessLocation::where('company_id', $company->id)->first();
            if($location) {
                return response()->json(['status'=>'success', 'httpStatus'=>409,'statusCode'=>10026,
                'message' => 'Company cannot be deleted as linked locations are active.'], 409);
            }
            $company->delete();
            return response()->json(['status'=>'success', 'httpStatus'=>200,'statusCode'=>10013,
                'message' => 'Company deleted successfully.'], 200);

        } catch (Exception $e) {
            return response()->json(['status'=>'error', 'httpStatus'=>500,'statusCode'=>10014,
                'message' => 'Failed to delete company: ' . $e->getMessage()], 500);
        }
    }

    public function regenerateApiKey(Request $request, string $slug): JsonResponse {
        try {
            $company = Company::where('slug', $slug)->first();
            if (!$company) {
                return response()->json(['status'=>'error', 'httpStatus'=>404,'statusCode'=>10006,
                'message' => 'Company details not found. Please check and try again.',], 404);
            }
            $previousApiKey = $company->api_key;
            $newApiKey = $company->regenerateApiKey();
            return response()->json(['status'=>'success','httpStatus'=>200,'statusCode'=>10025,
                'message' => 'API key regenerated successfully',
                'company' => [
                    'id' => $company->id,
                    'name' => $company->name,
                    'slug' => $company->slug,
                    'api_key' => $newApiKey,
                ],
                'previous_api_key' => $previousApiKey,
                'regenerated_at' => now()->toISOString(),
                'security_notice' => 'Please update all applications and integrations with the new API key. The previous API key is now invalid.',
            ], 200);
        } catch (Exception $e) {
             return response()->json(['status'=>'error', 'httpStatus'=>500,'statusCode'=>10016,
                'message' => 'Failed to regenerate API key: ' . $e->getMessage()], 500);
        }
    }
}
