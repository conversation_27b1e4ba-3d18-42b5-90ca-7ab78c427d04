<?php

/*
ZatcaNext - ZATCA e-Invoicing Middleware for Phase 2 Compliance

Copyright (c) 2025 ZatcaNext. All rights reserved.

This software is the proprietary property of ZatcaNext and is protected
by copyright and other intellectual property laws. Unauthorized use,
reproduction, modification, distribution, or reverse-engineering of this
software, in whole or in part, is strictly prohibited without express
written permission from ZatcaNext.

This software is provided "as is" without warranties of any kind, express
or implied. ZatcaNext disclaims all liability for damages arising from its use.

For support or licensing inquiries, contact: <EMAIL>
Website: https://zatcanext.com
Terms of Use: https://zatcanext.com/terms
*/

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use App\Models\User;
use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Traits\UserCreationTrait;
use App\Traits\CompanyCreationTrait;
use App\Traits\LocationCreationTrait;
use App\Traits\DatabaseTransactionTrait;
use App\Http\Requests\IntegratedRequest;

/**
 * @OA\Tag(
 *     name="🔧 Installation",
 *     description="Installation wizard endpoints"
 * )
 */
class InstallationController extends Controller
{
    use UserCreationTrait, CompanyCreationTrait, LocationCreationTrait, DatabaseTransactionTrait;
    /**
     * @OA\Get(
     *     path="/api/installation/status",
     *     summary="Check Installation Status",
     *     tags={"🔧 Installation"},
     *     @OA\Response(
     *         response=200,
     *         description="Installation status",
     *         @OA\JsonContent(
     *             @OA\Property(property="installation_completed", type="boolean"),
     *             @OA\Property(property="message", type="string")
     *         )
     *     )
     * )
     */
    public function status(): JsonResponse
    {
        $isCompleted = Setting::isInstallationCompleted();
        
        return response()->json([
            'installation_completed' => $isCompleted,
            'message' => $isCompleted 
                ? 'Installation completed. You may login now.' 
                : 'Installation setup required.'
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/installation/create-user",
     *     summary="Step 1: Create Initial Superadmin User",
     *     tags={"🔧 Installation"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"name", "email", "password"},
     *             @OA\Property(property="name", type="string", example="Super Admin"),
     *             @OA\Property(property="email", type="string", format="email", example="<EMAIL>"),
     *             @OA\Property(property="password", type="string", example="SecurePassword123!"),
     *             @OA\Property(property="password_confirmation", type="string", example="SecurePassword123!")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="User created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string"),
     *             @OA\Property(property="user", type="object"),
     *             @OA\Property(property="next_step", type="string")
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Installation already completed or validation failed"
     *     )
     * )
     */
    public function createUser(IntegratedRequest $request): JsonResponse
    {
        try {
            // Check if installation is already completed
            if (Setting::isInstallationCompleted()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Installation already completed. Cannot create new integrated setup.',
                    'error_code' => 'INSTALLATION_ALREADY_COMPLETED'
                ], 400);
            }

            // Check if user already exists
            if (User::count() > 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User already exists. Cannot create new integrated setup.',
                    'error_code' => 'USER_ALREADY_EXISTS'
                ], 400);
            }

            // Get organized data from request
            $data = $request->getOrganizedData();

            // Execute all operations in a single transaction
            $result = $this->executeInTransaction(function () use ($data) {
                // Step 1: Create User
                $userData = $this->validateUserData($data['user']);
                $user = $this->createUserRecord($userData);

                // Step 2: Create Company
                $companyData = $this->validateCompanyData($data['company']);
                $company = $this->createCompany($companyData);

                // Step 3: Create Location
                $locationData = $this->validateLocationData($data['location']);
                $location = $this->createLocation($locationData, $company);

                return [
                    'user' => $user,
                    'company' => $company,
                    'location' => $location,
                ];
            });

            // Format response data
            $responseData = [
                'status' => 'success',
                'message' => 'Integrated setup completed successfully. User, company, and location created.',
                'data' => [
                    'user' => $this->formatUserResponse($result['user']),
                    'company' => $this->formatCompanyResponse($result['company']),
                    'location' => $this->formatLocationResponse($result['location']),
                ],
                'next_steps' => [
                    'authentication' => 'Use POST /api/token with email and password to get JWT token',
                    'company_access' => 'Use the company API key for location-based operations',
                    'location_access' => 'Use the location authentication token for ZATCA operations',
                    'complete_installation' => 'Call POST /api/installation/complete to finalize setup'
                ]
            ];

            return response()->json($responseData, 201);

        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Integrated setup failed: ' . $e->getMessage(),
                'error_code' => 'INTEGRATED_SETUP_FAILED'
            ], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/installation/complete",
     *     summary="Step 3: Complete Installation Process",
     *     tags={"🔧 Installation"},
     *     @OA\Response(
     *         response=200,
     *         description="Installation completed successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string"),
     *             @OA\Property(property="installation_completed", type="boolean")
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="Installation requirements not met"
     *     )
     * )
     */
    public function complete(): JsonResponse
    {
        // Check if installation is already completed
        if (Setting::isInstallationCompleted()) {
            return response()->json([
                'message' => 'Installation already completed. You may login now.',
                'installation_completed' => true
            ], 200);
        }

        // Check if all required steps are completed
        $userExists = User::count() > 0;
        $companyExists = Company::count() > 0;
        $locationExists = \App\Models\BusinessLocation::count() > 0;

        if (!$userExists) {
            return response()->json([
                'message' => 'User not created yet. Please create a user first.',
                'error' => 'USER_NOT_CREATED',
                'next_step' => 'Create user using POST /api/installation/create-user'
            ], 400);
        }

        if (!$companyExists) {
            return response()->json([
                'message' => 'Company not created yet. Please create a company.',
                'error' => 'COMPANY_NOT_CREATED',
                'next_step' => 'Create company using POST /api/company'
            ], 400);
        }

        if (!$locationExists) {
            return response()->json([
                'message' => 'Business location not created yet. Please create a location.',
                'error' => 'LOCATION_NOT_CREATED',
                'next_step' => 'Create location using POST /api/business-location'
            ], 400);
        }

        // Mark installation as completed
        Setting::markInstallationCompleted();

        return response()->json([
            'message' => 'Success! Installation completed successfully. You can now use all application features.',
            'installation_completed' => true,
            'next_step' => 'Login using POST /api/token'
        ], 200);
    }
}
