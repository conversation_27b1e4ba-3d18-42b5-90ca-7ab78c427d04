<?php

namespace App\Http\Controllers\Api\v1;

use App\Constants\ZatcaConstants;
use App\Http\Controllers\Controller;
use App\Models\BusinessLocation;
use App\Services\ZatcaComplianceService;
use App\Services\ZatcaXmlSigningService;
use Modules\ZatcaValidations\Services\ValidationRulesService;
use Modules\ZatcaValidations\Services\ValidationResultsService;
use Modules\ZatcaValidations\Services\ZatcaBusinessRulesService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Exception;
use RuntimeException;

/**
 * ZATCA Clearance API Controller
 *
 * Professional implementation for ZATCA clearance operations.
 * Supports all environments (sandbox/simulation/production).
 * Uses X509 certificates for authentication.
 *
 * @OA\Tag(
 *     name="ZATCA Clearance",
 *     description="ZATCA Clearance API endpoints"
 * )
 */
class ClearanceController extends Controller
{
    private ZatcaComplianceService $complianceService;
    private ZatcaXmlSigningService $signingService;
    private ValidationRulesService $validationRulesService;
    private ValidationResultsService $validationResultsService;
    private ZatcaBusinessRulesService $businessRulesService;

    public function __construct(
        ZatcaComplianceService $complianceService,
        ZatcaXmlSigningService $signingService,
        ValidationRulesService $validationRulesService,
        ValidationResultsService $validationResultsService,
        ZatcaBusinessRulesService $businessRulesService
    ) {
        $this->complianceService = $complianceService;
        $this->signingService = $signingService;
        $this->validationRulesService = $validationRulesService;
        $this->validationResultsService = $validationResultsService;
        $this->businessRulesService = $businessRulesService;
    }

    /**
     * Universal Clearance Endpoint
     *
     * Processes standard invoice clearance for all environments.
     *
     * @OA\Post(
     *     path="/api/{environment}/clearance/",
     *     summary="Process ZATCA Clearance",
     *     description="Submit standard invoices for ZATCA clearance",
     *     operationId="processClearance",
     *     tags={"ZATCA Clearance"},
     *     security={{"ApiKeyAuth": {}}},
     *     @OA\Parameter(
     *         name="environment",
     *         in="path",
     *         required=true,
     *         description="ZATCA Environment",
     *         @OA\Schema(type="string", enum={"sandbox", "simulation", "production"})
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         description="Invoice data for clearance",
     *         @OA\JsonContent(
     *             required={"invoice", "profileID", "id", "uuid"},
     *             @OA\Property(
     *                 property="invoice",
     *                 type="object",
     *                 required={"invoiceType", "documentType"},
     *                 @OA\Property(property="invoiceType", type="string", enum={"standard"}, example="standard"),
     *                 @OA\Property(property="documentType", type="string", enum={"invoice", "credit_note", "debit_note"}, example="invoice")
     *             ),
     *             @OA\Property(property="profileID", type="string", example="reporting:1.0"),
     *             @OA\Property(property="id", type="string", example="12345"),
     *             @OA\Property(property="uuid", type="string", example="cc1572b1-798b-429e-9604-d2bf63b2233b")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Clearance successful",
     *         @OA\JsonContent(
     *             @OA\Property(property="status", type="string", example="200"),
     *             @OA\Property(property="Message", type="string", example="Success"),
     *             @OA\Property(property="invoiceHash", type="string"),
     *             @OA\Property(property="qrcode", type="string"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="clearanceStatus", type="string", example="CLEARED")
     *             )
     *         )
     *     )
     * )
     */
    public function processClearance(Request $request, string $environment): JsonResponse
    {
        try {
            $this->validateEnvironment($environment);

            // Enhanced ZATCA validation before processing
            // $this->performEnhancedValidation($request);

            // $this->validateClearanceRequest($request);

            $location = $this->getAuthenticatedLocation($request);
            $invoiceType = $request->input('invoice.invoiceType');
            $documentType = $request->input('invoice.documentType');

            // Validate that this is for standard invoices (clearance is for standard only)
            if ($invoiceType !== 'standard') {
                throw new RuntimeException('Clearance is only available for standard invoices');
            }

            return $this->executeClearance($request, $environment, $invoiceType, $documentType, $location);

        } catch (ValidationException $e) {
            // Handle validation errors with detailed response
            $result = $this->validationResultsService->createErrorResult(
                'Clearance validation failed: ' . implode(', ', $e->validator->errors()->all()),
                'CLEARANCE_VALIDATION_ERROR'
            );

            // Add detailed field errors
            $result['field_errors'] = [];
            foreach ($e->errors() as $field => $messages) {
                foreach ($messages as $message) {
                    $result['field_errors'][] = [
                        'field' => $field,
                        'message' => $message,
                        'type' => 'VALIDATION_ERROR'
                    ];
                }
            }

            return response()->json($result, 422);

        } catch (Exception $e) {
            return response()->json([
                'status' => '400',
                'Message' => $e->getMessage()
            ], 400);
        }
    }

    /**
     * Perform enhanced ZATCA validation before processing clearance
     */
    private function performEnhancedValidation(Request $request): void
    {
        try {
            // Validate invoice data structure and business rules
            $invoiceData = $request->all();

            // Perform comprehensive invoice validation
            $validatedData = $this->validationRulesService->validateInvoiceData($invoiceData);

            // Perform ZATCA business rules validation
            $businessRulesResult = $this->businessRulesService->validateBusinessRules($validatedData);

            // If there are business rule errors, throw validation exception
            if (!empty($businessRulesResult['errors'])) {
                $validator = Validator::make([], []);
                $validator->errors()->add('business_rules', implode(', ', $businessRulesResult['errors']));
                throw new ValidationException($validator);
            }

            // Log warnings if any (but don't fail the request)
            if (!empty($businessRulesResult['warnings'])) {
                \Log::warning('ZATCA Clearance Validation Warnings', [
                    'warnings' => $businessRulesResult['warnings'],
                    'invoice_id' => $invoiceData['id'] ?? 'unknown'
                ]);
            }

        } catch (ValidationException $e) {
            // Re-throw validation exceptions
            throw $e;
        } catch (Exception $e) {
            // Convert other exceptions to validation exceptions
            $validator = Validator::make([], []);
            $validator->errors()->add('validation_error', 'Enhanced validation failed: ' . $e->getMessage());
            throw new ValidationException($validator);
        }
    }

    /**
     * Validate environment parameter
     */
    private function validateEnvironment(string $environment): void
    {
        if (!in_array($environment, ZatcaConstants::ENVIRONMENTS)) {
            throw new RuntimeException('Invalid environment: ' . $environment);
        }
    }

    /**
     * Validate clearance request (basic structure validation)
     * Note: Enhanced validation is performed separately in performEnhancedValidation()
     */
    private function validateClearanceRequest(Request $request): void
    {
        $validator = Validator::make($request->all(), [
            'invoice.invoiceType' => 'required|string|in:standard',
            'invoice.documentType' => 'required|string|in:invoice,credit_note,debit_note',
            'profileID' => 'required|string',
            'id' => 'required|string',
            'uuid' => 'required|string|uuid',
            'issueDate' => 'required|string|date_format:Y-m-d',
            'issueTime' => 'required|string|date_format:H:i:s',
            'additionalDocumentReference' => 'required|array',
            'accountingSupplierParty' => 'required|array',
            'accountingCustomerParty' => 'required|array',
            'paymentMeansCode' => 'required|string',
            'actualDeliveryDate' => 'required|string|date_format:Y-m-d',
            'latestDeliveryDate' => 'required|string|date_format:Y-m-d',
            'allowanceCharge' => 'required|array',
            'taxAmount' => 'required|numeric|min:0',
            'taxTotal' => 'required|array',
            'legalMonetaryTotal' => 'required|array',
            'invoiceLines' => 'required|array|min:1',
        ]);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Get authenticated location from request
     */
    private function getAuthenticatedLocation(Request $request): BusinessLocation
    {
        // Try to get from middleware first
        $location = $request->attributes->get('authenticated_location');

        if ($location) {
            return $location;
        }

        // Fallback to manual authentication
        $company = $request->attributes->get('authenticated_company');
        $secret = $request->header('secret');

        if (!$company || !$secret) {
            throw new RuntimeException('Location not found');
        }

        $location = $company->locations()
            ->where('authentication_token', $secret)
            ->first();

        if (!$location) {
            throw new RuntimeException('Location not found');
        }

        return $location;
    }

    /**
     * Execute the actual clearance process
     */
    private function executeClearance(Request $request, string $environment, string $invoiceType, string $documentType, BusinessLocation $location): JsonResponse
    {
        try {
            // Get environment configuration
            $environmentData = $location->{$environment};

            if (!$environmentData) {
                throw new RuntimeException(ucfirst($environment) . ' environment not found for this location');
            }

            // Check if X509 certificates are available
            if (!$environmentData->x509_certificate || !$environmentData->x509_secret) {
                throw new RuntimeException('X509 certificates not found for ' . $environment . ' environment');
            }

            // Set profile ID for clearance
            $invoiceData = $request->all();
            $invoiceData['profileID'] = ZatcaConstants::PROFILE_REPORTING;

            // Generate XML using the compliance service
            $xmlContent = $this->complianceService->generateInvoiceXml($invoiceData, $invoiceType, $documentType);

            // Sign XML using private key and X509 certificate
            $signingResult = $this->signingService->signXmlDocument(
                $xmlContent,
                $environmentData->private_key,
                $environmentData->x509_certificate
            );

            // Prepare clearance data
            $clearanceData = [
                'invoiceHash' => $signingResult['invoiceHash'],
                'uuid' => $invoiceData['uuid'],
                'invoice' => $signingResult['invoiceXml']
            ];

            // Call ZATCA clearance API using X509 credentials
            $clearanceResponse = $this->complianceService->processClearance(
                $clearanceData,
                $environmentData->x509_certificate,
                $environmentData->x509_secret,
                $environment
            );

            return response()->json([
                'status' => ZatcaConstants::HTTP_STATUS_SUCCESS,
                'Message' => ZatcaConstants::MESSAGE_SUCCESS,
                'invoiceHash' => $signingResult['invoiceHash'],
                'qrcode' => $signingResult['invoiceQRCode'],
                'data' => $clearanceResponse
            ], 200);

        } catch (Exception $e) {
            throw new RuntimeException('Clearance failed: ' . $e->getMessage());
        }
    }
}
