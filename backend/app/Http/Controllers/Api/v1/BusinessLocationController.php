<?php

/*
ZatcaNext - ZATCA e-Invoicing Middleware for Phase 2 Compliance

Copyright (c) 2025 ZatcaNext. All rights reserved.

This software is the proprietary property of ZatcaNext and is protected
by copyright and other intellectual property laws. Unauthorized use,
reproduction, modification, distribution, or reverse-engineering of this
software, in whole or in part, is strictly prohibited without express
written permission from ZatcaNext.

This software is provided "as is" without warranties of any kind, express
or implied. ZatcaNext disclaims all liability for damages arising from its use.

For support or licensing inquiries, contact: <EMAIL>
Website: https://zatcanext.com
Terms of Use: https://zatcanext.com/terms
*/

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Models\BusinessLocation;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use App\Rules\TaxNumberRule;
use Exception;

/**
 * BusinessLocationController
 *
 * Handles business location management operations
 */
class BusinessLocationController extends Controller
{
    /**
     * @OA\Get(
     *     path="/api/locations",
     *     summary="List all business locations",
     *     description="Get all business locations for the authenticated company",
     *     tags={"Location Management"},
     *     security={{"ApiKeyAuth": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="List of business locations",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 @OA\Property(property="id", type="integer", example=1),
     *                 @OA\Property(property="authentication_token", type="string", example="auth_token_123"),
     *                 @OA\Property(property="seller_name", type="string", example="Safa cold store for foodstuff Co."),
     *                 @OA\Property(property="tax_no", type="string", example="311253816600003"),
     *                 @OA\Property(property="common_name", type="string", example="Safa Cold Store"),
     *                 @OA\Property(property="organisation", type="string", example="Safa cold store for foodstuff Co."),
     *                 @OA\Property(property="organisation_unit", type="string", example="Store"),
     *                 @OA\Property(property="serial_number", type="string", example="1-nomizo.com|2-version 2.0|3-ed22f1d8-e6a2-1118-9b58-d9a8f11e445f"),
     *                 @OA\Property(property="title", type="string", example="1100"),
     *                 @OA\Property(property="registered_address", type="string", example="Mihar Al-Daylami 2682 , Al salamah 7864 , Makkah"),
     *                 @OA\Property(property="business_category", type="string", example="Store")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="detail", type="string", example="Authentication credentials were not provided.")
     *         )
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        $company = $request->attributes->get('authenticated_company');

        $locations = BusinessLocation::where('company_id', $company->id)->get();

        $results = $locations->map(function ($location) {
            return [
                'id' => $location->id,
                'authentication_token' => $location->authentication_token,
                'seller_name' => $location->seller_name,
                'tax_no' => $location->tax_no,
                'common_name' => $location->common_name,
                'organisation' => $location->organisation,
                'organisation_unit' => $location->organisation_unit,
                'serial_number' => $location->serial_number,
                'title' => $location->title,
                'registered_address' => $location->registered_address,
                'business_category' => $location->business_category,
            ];
        });

        return response()->json(['status'=>'success', 'httpStatus'=>200,'statusCode'=>10015,'data' => $results,], 200);
    }

    /**
     * @OA\Post(
     *     path="/api/locations",
     *     summary="Create new business location",
     *     description="Create a new business location for the authenticated company",
     *     tags={"Location Management"},
     *     security={{"ApiKeyAuth": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="seller_name", type="string", example="Safa cold store for foodstuff Co.", description="Seller name"),
     *             @OA\Property(property="tax_no", type="string", example="311253816600003", description="Tax number"),
     *             @OA\Property(property="organisation", type="string", example="Safa cold store for foodstuff Co.", description="Organisation name"),
     *             @OA\Property(property="serial_number", type="string", example="1-nomizo.com|2-version 2.0|3-ed22f1d8-e6a2-1118-9b58-d9a8f11e445f", description="Serial number"),
     *             @OA\Property(property="organisation_unit", type="string", example="Store", description="Organisation unit"),
     *             @OA\Property(property="registered_address", type="string", example="Mihar Al-Daylami 2682 , Al salamah 7864 , Makkah", description="Registered address"),
     *             @OA\Property(property="business_category", type="string", example="Store", description="Business category"),
     *             @OA\Property(property="title", type="string", example="1100", description="Title"),
     *             @OA\Property(property="common_name", type="string", example="Safa Cold Store", description="Common name")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Location created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="authentication_token", type="string", example="auth_token_123"),
     *             @OA\Property(property="seller_name", type="string", example="Safa cold store for foodstuff Co."),
     *             @OA\Property(property="tax_no", type="string", example="311253816600003"),
     *             @OA\Property(property="common_name", type="string", example="Safa Cold Store"),
     *             @OA\Property(property="organisation", type="string", example="Safa cold store for foodstuff Co."),
     *             @OA\Property(property="organisation_unit", type="string", example="Store"),
     *             @OA\Property(property="serial_number", type="string", example="1-nomizo.com|2-version 2.0|3-ed22f1d8-e6a2-1118-9b58-d9a8f11e445f"),
     *             @OA\Property(property="title", type="string", example="1100"),
     *             @OA\Property(property="registered_address", type="string", example="Mihar Al-Daylami 2682 , Al salamah 7864 , Makkah"),
     *             @OA\Property(property="business_category", type="string", example="Store")
     *         )
     *     ),
     *     @OA\Response(
     *         response=422,
     *         description="Validation error",
     *         @OA\JsonContent(
     *             @OA\Property(property="detail", type="string", example="Validation failed"),
     *             @OA\Property(property="errors", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=401,
     *         description="Unauthorized",
     *         @OA\JsonContent(
     *             @OA\Property(property="detail", type="string", example="Authentication credentials were not provided.")
     *         )
     *     )
     * )
     */
    public function store(Request $request): JsonResponse
    {

        $validator = Validator::make($request->all(), [
            'seller_name' => 'nullable|string|max:230',
            'tax_no' => ['nullable', new TaxNumberRule()],
            'common_name' => 'nullable|string|max:230',
            'organisation' => 'nullable|string|max:230',
            'organisation_unit' => 'nullable|string|max:230',
            'serial_number' => 'nullable|string|max:230',
            'title' => 'nullable|string|max:230',
            'registered_address' => 'nullable|string|max:230',
            'business_category' => 'nullable|string|max:230',
            'otp' => 'nullable|string|max:230',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'detail' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {

            $company = $request->attributes->get('authenticated_company');

            $locationData = $request->all();
            $locationData['company_id'] = $company->id;
            $location = BusinessLocation::create($locationData);

            // Check if this is during installation process
            $isInstallationCompleted = \App\Models\Setting::isInstallationCompleted();
            $message = $isInstallationCompleted
                ? 'Business location created successfully.'
                : 'Success! Business location created successfully. Please complete the installation now.';

            return response()->json(['status'=>'success', 'httpStatus'=>201,'statusCode'=>10017,'data'=>[
                'id' => $location->id,
                'authentication_token' => $location->authentication_token,
                'seller_name' => $location->seller_name,
                'tax_no' => $location->tax_no,
                'common_name' => $location->common_name,
                'organisation' => $location->organisation,
                'organisation_unit' => $location->organisation_unit,
                'serial_number' => $location->serial_number,
                'title' => $location->title,
                'registered_address' => $location->registered_address,
                'business_category' => $location->business_category,
                'message' => $message,
                'next_step' => $isInstallationCompleted ? null : 'Complete installation using POST /api/installation/complete'
            ]], 201);

        } catch (Exception $e) {
            return response()->json(['status'=>'error', 'httpStatus'=>500,'statusCode'=>10016,
                'message' => 'Failed to  create location: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Display the specified business location
     */
    public function show($id, Request $request): JsonResponse
    {

        $company = $request->attributes->get('authenticated_company');

        $location = BusinessLocation::where('id', $id)
            ->where('company_id', $company->id)
            ->first();

        if (!$location) {
            return response()->json(['status'=>'error', 'httpStatus'=>404,'statusCode'=>10018,
                'message' => 'Location details not found. Please check and try again.',
            ], 404);
        }

        return response()->json(['status'=>'success', 'httpStatus'=>200,'statusCode'=>10019,'data'=>[
            'id' => $location->id,
            'authentication_token' => $location->authentication_token,
            'seller_name' => $location->seller_name,
            'tax_no' => $location->tax_no,
            'common_name' => $location->common_name,
            'organisation' => $location->organisation,
            'organisation_unit' => $location->organisation_unit,
            'serial_number' => $location->serial_number,
            'title' => $location->title,
            'registered_address' => $location->registered_address,
            'business_category' => $location->business_category,
        ]]);
    }

    /**
     * Update the specified business location
     */
    public function update(Request $request, $id): JsonResponse
    {

        $company = $request->attributes->get('authenticated_company');

        $location = BusinessLocation::where('id', $id)
            ->where('company_id', $company->id)
            ->first();

        if (!$location) {
            return response()->json(['status'=>'error', 'httpStatus'=>404,'statusCode'=>10018,
                'message' => 'Location details not found. Please check and try again.',
            ], 404);
        }

        $validator = Validator::make($request->all(), [
            'seller_name' => 'nullable|string|max:230',
            'tax_no' => ['nullable', new TaxNumberRule($location->id)],
            'common_name' => 'nullable|string|max:230',
            'organisation' => 'nullable|string|max:230',
            'organisation_unit' => 'nullable|string|max:230',
            'serial_number' => 'nullable|string|max:230',
            'title' => 'nullable|string|max:230',
            'registered_address' => 'nullable|string|max:230',
            'business_category' => 'nullable|string|max:230',
            'otp' => 'nullable|string|max:230',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'detail' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $location->update($request->all());

            return response()->json(['status'=>'success', 'httpStatus'=>200,'statusCode'=>10018,'data'=>[
                'id' => $location->id,
                'authentication_token' => $location->authentication_token,
                'seller_name' => $location->seller_name,
                'tax_no' => $location->tax_no,
                'common_name' => $location->common_name,
                'organisation' => $location->organisation,
                'organisation_unit' => $location->organisation_unit,
                'serial_number' => $location->serial_number,
                'title' => $location->title,
                'registered_address' => $location->registered_address,
                'business_category' => $location->business_category,
            ]],200);

        } catch (Exception $e) {
            return response()->json(['status'=>'error', 'httpStatus'=>500,'statusCode'=>10020,
                'message' => 'Failed to update location: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Remove the specified business location
     */
    public function destroy($id, Request $request): JsonResponse
    {
        $company = $request->attributes->get('authenticated_company');
        $location = BusinessLocation::where('id', $id)
            ->where('company_id', $company->id)
            ->first();
        if (!$location) {
            return response()->json(['status'=>'error', 'httpStatus'=>404,'statusCode'=>10018,
                'message' => 'Location details not found. Please check and try again.',
            ], 404);
        }
        try {
            $simulations = Simulation::where('business_location_id',$id)->first();
            if($simulations) {
                return response()->json(['status'=>'success', 'httpStatus'=>409,'statusCode'=>10026,
                'message' => 'Location cannot be deleted as simulations are available.'], 409);
            }
            $location->delete();
            return response()->json(['status'=>'success', 'httpStatus'=>200,'statusCode'=>10020,
                'message' => 'Location deleted successfully.'], 200);
        } catch (Exception $e) {
            return response()->json(['status'=>'error', 'httpStatus'=>500,'statusCode'=>10021,
                'message' => 'Failed to delete location: ' . $e->getMessage()], 500);
        }
    }

    public function regenerateToken(Request $request, string $id): JsonResponse {
        try {
            $location = BusinessLocation::where('id', $id)->first();

        if (!$location) {
            return response()->json(['status'=>'error', 'httpStatus'=>404,'statusCode'=>10018,
                'message' => 'Location details not found. Please check and try again.',
            ], 404);
        }
        $previousApiKey = $location->authentication_token;
        $newApiKey = $location->regenerateApiKey();
            return response()->json(['status'=>'success','httpStatus'=>200,'statusCode'=>10025,
                'message' => 'API key regenerated successfully',
                'location' => [
                     'id' => $location->id,
                    'authentication_token' => $newApiKey,
                    'seller_name' => $location->seller_name,
                    'tax_no' => $location->tax_no,
                    'common_name' => $location->common_name,
                    'organisation' => $location->organisation,
                    'organisation_unit' => $location->organisation_unit,
                    'serial_number' => $location->serial_number,
                    'title' => $location->title,
                    'registered_address' => $location->registered_address,
                    'business_category' => $location->business_category,
                    ],
                'previous_api_key' => $previousApiKey,
                'regenerated_at' => now()->toISOString(),
                'security_notice' => 'Please update all applications and integrations with the new token. The previous API key is now invalid.',
            ], 200);
        } catch (Exception $e) {
             return response()->json(['status'=>'error', 'httpStatus'=>500,'statusCode'=>10016,
                'message' => 'Failed to regenerate API key: ' . $e->getMessage()], 500);
        }
    }


}
