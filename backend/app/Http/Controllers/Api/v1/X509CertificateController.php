<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Services\ZatcaX509Service;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;

/**
 * X.509 Certificate Controller for ZATCA E-Invoicing
 * API for X.509 certificate generation and retrieval
 */
class X509CertificateController extends Controller
{
    private ZatcaX509Service $x509Service;

    public function __construct(ZatcaX509Service $x509Service)
    {
        $this->x509Service = $x509Service;
    }

    /**
     * Generate or retrieve X.509 certificate for ZATCA e-invoicing
     *
     * @param Request $request
     * @param string $locationId
     * @return JsonResponse
     */
    public function getCertificate(Request $request, string $locationId): JsonResponse
    {
        try {

            if (empty($locationId) || !is_numeric($locationId)) {
                return response()->json([
                    'error' => 'Invalid location ID format',
                    'message' => 'Location ID must be a valid numeric value',
                    'code' => 'INVALID_LOCATION_ID'
                ], 400);
            }

            $secret = $request->header('secret');
            $authorization = $request->header('Authorization');

            if (empty($secret) || empty($authorization)) {
                return response()->json([
                    'error' => 'Missing authentication headers',
                    'message' => 'Both secret and Authorization headers are required',
                    'code' => 'MISSING_AUTH_HEADERS'
                ], 401);
            }

            $certificateData = $this->x509Service->getCertificateForLocation($locationId, $secret, $authorization);

            return response()->json([
                'x509_certificate' => $certificateData['x509_certificate']
            ], 200);

        } catch (Exception $e) {

            $statusCode = 500;
            $errorCode = 'CERTIFICATE_GENERATION_FAILED';

            if (str_contains($e->getMessage(), 'authentication')) {
                $statusCode = 401;
                $errorCode = 'AUTHENTICATION_FAILED';
            } elseif (str_contains($e->getMessage(), 'location')) {
                $statusCode = 404;
                $errorCode = 'LOCATION_NOT_FOUND';
            } elseif (str_contains($e->getMessage(), 'validation')) {
                $statusCode = 400;
                $errorCode = 'VALIDATION_FAILED';
            }

            return response()->json([
                'error' => 'X.509 certificate generation failed',
                'message' => $e->getMessage(),
                'code' => $errorCode
            ], $statusCode);
        }
    }

    /**
     * Get certificate information
     *
     * @param Request $request
     * @param string $locationId
     * @return JsonResponse
     */
    public function getCertificateInfo(Request $request, string $locationId): JsonResponse
    {
        try {


            $secret = $request->header('secret');
            $authorization = $request->header('Authorization');

            if (empty($secret) || empty($authorization)) {
                return response()->json([
                    'error' => 'Missing authentication headers',
                    'code' => 'MISSING_AUTH_HEADERS'
                ], 401);
            }

            $certificateInfo = $this->x509Service->getCertificateInfo($locationId, $secret, $authorization);

            return response()->json([
                'location_id' => $locationId,
                'certificate_info' => $certificateInfo,
                'generated_at' => now()->toISOString()
            ], 200);

        } catch (Exception $e) {
            return response()->json([
                'error' => 'Certificate info retrieval failed',
                'message' => $e->getMessage(),
                'code' => 'CERTIFICATE_INFO_FAILED'
            ], 500);
        }
    }
}
