<?php

namespace App\Http\Controllers\Api\v1;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * Settings Controller
 * 
 * Manages application settings including module enablement
 */
class SettingsController extends Controller
{
    /**
     * Get ZATCA validations module status
     */
    public function getZatcaValidationsStatus(): JsonResponse
    {
        try {
            $enabled = Setting::isZatcaValidationsEnabled();
            
            return response()->json([
                'status' => 'success',
                'httpStatus' => 200,
                'statusCode' => 20001,
                'data' => [
                    'zatca_validations_enabled' => $enabled,
                    'description' => 'ZATCA validations module status'
                ]
            ], 200);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'httpStatus' => 500,
                'statusCode' => 50001,
                'message' => 'Failed to get ZATCA validations status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Enable/disable ZATCA validations module
     */
    public function setZatcaValidationsStatus(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'enabled' => 'required|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'httpStatus' => 422,
                    'statusCode' => 42201,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $enabled = $request->input('enabled');
            Setting::setZatcaValidationsEnabled($enabled);
            
            return response()->json([
                'status' => 'success',
                'httpStatus' => 200,
                'statusCode' => 20002,
                'message' => 'ZATCA validations module ' . ($enabled ? 'enabled' : 'disabled') . ' successfully',
                'data' => [
                    'zatca_validations_enabled' => $enabled
                ]
            ], 200);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'httpStatus' => 500,
                'statusCode' => 50002,
                'message' => 'Failed to update ZATCA validations status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all application settings
     */
    public function getAllSettings(): JsonResponse
    {
        try {
            $settings = [
                'installation_completed' => Setting::isInstallationCompleted(),
                'zatca_validations_enabled' => Setting::isZatcaValidationsEnabled(),
            ];
            
            return response()->json([
                'status' => 'success',
                'httpStatus' => 200,
                'statusCode' => 20003,
                'data' => $settings
            ], 200);
            
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'httpStatus' => 500,
                'statusCode' => 50003,
                'message' => 'Failed to get settings: ' . $e->getMessage()
            ], 500);
        }
    }
}
