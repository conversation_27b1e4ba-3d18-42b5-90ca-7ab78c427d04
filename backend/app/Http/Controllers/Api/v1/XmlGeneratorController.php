<?php

namespace App\Http\Controllers\Api\v1;

use App\Constants\ZatcaConstants;
use App\Http\Controllers\Controller;
use App\Services\ZatcaComplianceService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Exception;
use Illuminate\Support\Str;

/**
 * XML Generator Controller for ZATCA Invoice XML Generation
 * Provides an API endpoint to generate ZATCA-compliant XML from JSON payload
 */
class XmlGeneratorController extends Controller
{
    private ZatcaComplianceService $complianceService;

    public function __construct(ZatcaComplianceService $complianceService)
    {
        $this->complianceService = $complianceService;
    }

    /**
     * Generate ZATCA-compliant XML from JSON payload
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function generateXml(Request $request): JsonResponse
    {
        try {

            $payload = $request->all();

            if (empty($payload)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Empty payload provided',
                    'code' => 'EMPTY_PAYLOAD'
                ], 400);
            }

            $requiredFields = ['id', 'issueDate', 'issueTime', 'accountingSupplierParty', 'legalMonetaryTotal'];
            foreach ($requiredFields as $field) {
                if (!isset($payload[$field])) {
                    return response()->json([
                        'status' => 'error',
                        'message' => "Required field missing: $field",
                        'code' => 'MISSING_REQUIRED_FIELD'
                    ], 400);
                }
            }

            if (!isset($payload['invoice']['invoiceType'], $payload['invoice']['documentType'])) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invoice type and document type are required in payload.invoice',
                    'code' => 'MISSING_INVOICE_TYPE'
                ], 400);
            }

            $invoiceType = $payload['invoice']['invoiceType'];
            $documentType = $payload['invoice']['documentType'];

            $generatedXml = $this->complianceService->generateInvoiceXml($payload, $invoiceType, $documentType);

            $xmlLength = strlen($generatedXml);
            $hasRequiredElements = $this->validateXmlStructure($generatedXml);

            return response()->json([
                'status' => 'success',
                'message' => 'ZATCA XML generated successfully',
                'data' => [
                    'xml' => $generatedXml,
                    'metadata' => [
                        'xml_length' => $xmlLength,
                        'invoice_type' => $invoiceType,
                        'document_type' => $documentType,
                        'invoice_id' => $payload['id'],
                        'issue_date' => $payload['issueDate'],
                        'supplier_name' => $payload['accountingSupplierParty']['registrationName'],
                        'total_amount' => $payload['legalMonetaryTotal']['payableAmount'],
                        'currency' => $payload['documentCurrencyCode'],
                        'has_required_elements' => $hasRequiredElements,
                        'generated_at' => now()->toISOString()
                    ]
                ]
            ], 200);

        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'XML generation failed: ' . $e->getMessage(),
                'code' => 'XML_GENERATION_FAILED'
            ], 500);
        }
    }

    /**
     * Generate sample payload with current date and realistic data
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function generateSamplePayload(Request $request): JsonResponse
    {
        try {
            $invoiceType = $request->get('type', ZatcaConstants::INVOICE_TYPE_STANDARD);

            if (!in_array($invoiceType, [ZatcaConstants::INVOICE_TYPE_STANDARD, ZatcaConstants::INVOICE_TYPE_SIMPLIFIED], true)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid invoice type. Must be: ' . ZatcaConstants::INVOICE_TYPE_STANDARD . ' or ' . ZatcaConstants::INVOICE_TYPE_SIMPLIFIED,
                    'code' => 'INVALID_INVOICE_TYPE'
                ], 400);
            }

            $currentDate = now()->format('Y-m-d');
            $currentTime = now()->format('H:i:s');
            $invoiceNumber = 'INV-' . now()->format('Ymd') . '-' . random_int(1000, 9999);
            $uuid = Str::uuid()->toString();

            $samplePayload = [
                "invoice" => [
                    "invoiceType" => $invoiceType,
                    "documentType" => ZatcaConstants::DOCUMENT_TYPE_INVOICE
                ],
                "profileID" => ZatcaConstants::PROFILE_REPORTING,
                "id" => $invoiceNumber,
                "uuid" => $uuid,
                "issueDate" => $currentDate,
                "issueTime" => $currentTime,
                "documentCurrencyCode" => ZatcaConstants::CURRENCY_SAR,
                "taxCurrencyCode" => ZatcaConstants::CURRENCY_SAR,
                "additionalDocumentReference" => [
                    "id" => "ICV",
                    "uuid" => (string)random_int(100, 999),
                    "pih" => "NWZlY2ViNjZmZmM4NmYzOGQ5NTI3ODZjNmQ2OTZjNzljMmRiYzIzOWRkNGU5MWI0NjcyOWQ3M2EyN2ZiNTdlOQ=="
                ],
                "billingReference" => [
                    "invoiceDate" => $currentDate,
                    "invoiceNo" => 'INV-' . now()->subDay()->format('Ymd') . '-' . random_int(1000, 9999)
                ],
                "accountingSupplierParty" => [
                    "id" => "**********",
                    "schema" => "CRN",
                    "streetName" => "King Fahd Road",
                    "buildingNumber" => "1234",
                    "plotIdentification" => "5678",
                    "citySubdivisionName" => "Al Olaya",
                    "cityName" => "Riyadh",
                    "postalZone" => "12345",
                    "companyID" => "***************",
                    "taxID" => "VAT",
                    "registrationName" => "Test Company for ZATCA Compliance"
                ],
                "accountingCustomerParty" => [
                    "id" => "**********",
                    "schema" => "NAT",
                    "streetName" => "Prince Mohammed Bin Abdulaziz Street",
                    "additionalStreetName" => "Building 456",
                    "buildingNumber" => "456",
                    "plotIdentification" => "789",
                    "citySubdivisionName" => "Al Malaz",
                    "cityName" => "Riyadh",
                    "postalZone" => "54321",
                    "companyID" => "***************",
                    "taxID" => "VAT",
                    "registrationName" => "Customer Company Ltd",
                    "countryCode" => "SA"
                ],
                "paymentMeansCode" => "10",
                "actualDeliveryDate" => $currentDate,
                "latestDeliveryDate" => now()->addDays(7)->format('Y-m-d'),
                "allowanceCharge" => [
                    "chargeIndicator" => "false",
                    "allowanceChargeReason" => "discount",
                    "amount" => "0",
                    "taxId" => "S",
                    "taxPercentage" => "15",
                    "taxScheme" => "VAT"
                ],
                "taxAmount" => "15.00",
                "taxTotal" => [
                    "taxAmount" => "15.00",
                    "tsttaxableAmount" => "100.00",
                    "tsttaxAmount" => "15.00",
                    "taxId" => "S",
                    "taxPercentage" => "15.00",
                    "taxScheme" => "VAT"
                ],
                "legalMonetaryTotal" => [
                    "lineExtensionAmount" => "100.00",
                    "taxExclusiveAmount" => "100.00",
                    "taxInclusiveAmount" => "115.00",
                    "allowanceTotalAmount" => "0.00",
                    "prepaidAmount" => "0.00",
                    "payableAmount" => "115.00"
                ],
                "invoiceLines" => [
                    [
                        "id" => "1",
                        "invoicedQuantity" => "1.0000",
                        "lineExtensionAmount" => "100.00",
                        "taxAmount" => "15.00",
                        "roundingAmount" => "115.00",
                        "itemName" => "Professional Software License",
                        "taxId" => "S",
                        "taxPercentage" => "15.00",
                        "taxScheme" => "VAT",
                        "priceAmount" => "100.00",
                        "allowanceChargeReason" => "discount",
                        "allowanceChargeAmount" => "0.00"
                    ]
                ]
            ];



            return response()->json([
                'status' => 'success',
                'message' => 'Sample payload generated successfully',
                'data' => [
                    'payload' => $samplePayload,
                    'metadata' => [
                        'invoice_type' => $invoiceType,
                        'generated_at' => now()->toISOString(),
                        'invoice_number' => $invoiceNumber,
                        'uuid' => $uuid,
                        'current_date' => $currentDate,
                        'total_amount' => '115.00',
                        'currency' => 'SAR'
                    ]
                ]
            ], 200);

        } catch (Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Sample payload generation failed: ' . $e->getMessage(),
                'code' => 'SAMPLE_GENERATION_FAILED'
            ], 500);
        }
    }

    /**
     * Validate XML structure for required ZATCA elements
     */
    private function validateXmlStructure(string $xml): array
    {
        return [
            'has_additional_doc_ref' => str_contains($xml, 'AdditionalDocumentReference'),
            'has_tax_subtotal' => str_contains($xml, 'TaxSubtotal'),
            'has_classified_tax_category' => str_contains($xml, 'ClassifiedTaxCategory'),
            'has_pih_element' => str_contains($xml, '<cbc:ID>PIH</cbc:ID>'),
            'has_icv_element' => str_contains($xml, '<cbc:ID>ICV</cbc:ID>'),
            'has_proper_namespaces' => str_contains($xml, 'urn:oasis:names:specification:ubl:schema:xsd')
        ];
    }
}
