<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Company;

class CompanyApiKeyAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get API key from Authorization header
        $apiKey  = $request->header('x-api-key');
        if (!$apiKey) {
            return response()->json(['status'=>'error', 'httpStatus'=>401,'statusCode'=>10002,
                'message' => "The request is missing the required 'x-api-key' header",
            ], 401);
        }

        // Find company by API key
        $company = Company::where('api_key', $apiKey)->first();

        if (!$company) {
            return response()->json(['status'=>'error', 'httpStatus'=>401,'statusCode'=>10003,
                'message' => 'The provided API key is invalid. Please check and try again.',
            ], 401);
        }

        // Add company to request for use in controllers
        $request->attributes->set('authenticated_company', $company);

        return $next($request);
    }
}
