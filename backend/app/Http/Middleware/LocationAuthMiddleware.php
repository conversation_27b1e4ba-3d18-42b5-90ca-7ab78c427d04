<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\BusinessLocation;

/**
 * Location Authentication Middleware
 * 
 * Authenticates requests using location authentication token (secret header)
 * Used for compliance, clearance, and reporting endpoints
 */
class LocationAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Get the secret from headers
        $secret = $request->header('x-secret');
        
        if (!$secret) {
             return response()->json(['status'=>'error', 'httpStatus'=>401,'statusCode'=>10004,
                'message' => "The request is missing the required 'x-secret' header",
            ], 401);
        }
        
        // Find location by authentication token
        $location = BusinessLocation::where('authentication_token', $secret)->first();
        
        if (!$location) {
            return response()->json(['status'=>'error', 'httpStatus'=>401,'statusCode'=>10005,
                'message' => 'The provided secret is invalid. Please check and try again.',
            ], 401);
        }
        
        // Add location to request attributes for use in controllers
        $request->attributes->set('authenticated_location', $location);
        
        // Also add the company for convenience
        $request->attributes->set('authenticated_company', $location->company);
        
        return $next($request);
    }
}
