<?php

/*
ZatcaNext - ZATCA e-Invoicing Middleware for Phase 2 Compliance

Copyright (c) 2025 ZatcaNext. All rights reserved.

This software is the proprietary property of ZatcaNext and is protected
by copyright and other intellectual property laws. Unauthorized use,
reproduction, modification, distribution, or reverse-engineering of this
software, in whole or in part, is strictly prohibited without express
written permission from ZatcaNext.

This software is provided "as is" without warranties of any kind, express
or implied. ZatcaNext disclaims all liability for damages arising from its use.

For support or licensing inquiries, contact: <EMAIL>
Website: https://zatcanext.com
Terms of Use: https://zatcanext.com/terms
*/

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\Setting;
use Symfony\Component\HttpFoundation\Response;

class InstallationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if installation is completed
        if (!Setting::isInstallationCompleted()) {
            return response()->json([
                'message' => 'Application installation is not completed. Please complete the installation wizard first.',
                'error' => 'INSTALLATION_REQUIRED',
                'installation_status_url' => '/api/installation/status',
                'installation_setup_url' => '/api/installation/setup'
            ], 403);
        }

        return $next($request);
    }
}
