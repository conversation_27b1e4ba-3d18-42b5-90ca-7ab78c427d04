<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\User;

class JwtAuthMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $token = $this->extractToken($request);

        if (!$token) {
            return response()->json(['status'=>'error', 'httpStatus'=>401,'statusCode'=>10000,
                'message' => 'Access denied. Authorization header not provided'
            ], 401);
        }

        $user = $this->validateToken($token);

        if (!$user) {
            return response()->json(['status'=>'error', 'httpStatus'=>401,'statusCode'=>10001,
                'message' => 'The access token is either invalid or has expired. Please login again.'
            ], 401);
        }

        // Set the authenticated user
        auth()->setUser($user);
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        return $next($request);
    }

    /**
     * Extract token from request
     */
    private function extractToken(Request $request): ?string
    {
        // Check Authorization header
        $authHeader = $request->header('Authorization');
        if ($authHeader && str_starts_with($authHeader, 'Bearer ')) {
            return substr($authHeader, 7);
        }

        // Check query parameter
        if ($request->has('token')) {
            return $request->get('token');
        }

        return null;
    }

    /**
     * Validate JWT token and return user
     */
    private function validateToken(string $token): ?User
    {
        try {
            // Split token into payload and signature
            $parts = explode('.', $token);
            if (count($parts) !== 2) {
                return null;
            }

            [$payloadBase64, $signature] = $parts;

            // Verify signature
            $expectedSignature = hash_hmac('sha256', $payloadBase64, config('app.key'));
            if (!hash_equals($expectedSignature, $signature)) {
                return null; // Invalid signature
            }

            // Decode the payload
            $decoded = base64_decode($payloadBase64);
            if (!$decoded) {
                return null;
            }

            $payload = json_decode($decoded, true);
            if (!$payload || !isset($payload['user_id']) || !isset($payload['exp'])) {
                return null;
            }

            // Check if token is expired
            if ($payload['exp'] < time()) {
                return null;
            }

            // Find user
            $user = User::find($payload['user_id']);
            if (!$user) {
                return null;
            }

            return $user;

        } catch (\Exception $e) {
            // Log the exception but don't expose details in response
            \Log::error('Token validation error: ' . $e->getMessage());
            return null;
        }
    }

}
