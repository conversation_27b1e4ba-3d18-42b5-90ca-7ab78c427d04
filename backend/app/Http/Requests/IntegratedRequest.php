<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class IntegratedRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // User validation rules
            'user.name' => 'required|string|max:255',
            'user.email' => 'required|string|email|max:255|unique:users,email',
            'user.password' => 'required|string|min:8|confirmed',
            'user.password_confirmation' => 'required|string|min:8',

            // Company validation rules
            'company.name' => 'required|string|max:100',

            // Location validation rules (all optional)
            'location.seller_name' => 'nullable|string|max:230',
            'location.tax_no' => 'nullable|string|max:16',
            'location.common_name' => 'nullable|string|max:230',
            'location.organisation' => 'nullable|string|max:230',
            'location.organisation_unit' => 'nullable|string|max:230',
            'location.serial_number' => 'nullable|string|max:230',
            'location.title' => 'nullable|string|max:230',
            'location.registered_address' => 'nullable|string|max:230',
            'location.business_category' => 'nullable|string|max:230',
            'location.otp' => 'nullable|string|max:230',
        ];
    }

    /**
     * Get custom error messages for validation rules.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'user.name.required' => 'User name is required',
            'user.email.required' => 'User email is required',
            'user.email.email' => 'User email must be a valid email address',
            'user.email.unique' => 'User email already exists',
            'user.password.required' => 'User password is required',
            'user.password.min' => 'User password must be at least 8 characters',
            'user.password.confirmed' => 'User password confirmation does not match',
            'user.password_confirmation.required' => 'User password confirmation is required',
            
            'company.name.required' => 'Company name is required',
            'company.name.max' => 'Company name must not exceed 100 characters',
            
            'location.seller_name.max' => 'Seller name must not exceed 230 characters',
            'location.tax_no.max' => 'Tax number must not exceed 16 characters',
            'location.common_name.max' => 'Common name must not exceed 230 characters',
            'location.organisation.max' => 'Organisation must not exceed 230 characters',
            'location.organisation_unit.max' => 'Organisation unit must not exceed 230 characters',
            'location.serial_number.max' => 'Serial number must not exceed 230 characters',
            'location.title.max' => 'Title must not exceed 230 characters',
            'location.registered_address.max' => 'Registered address must not exceed 230 characters',
            'location.business_category.max' => 'Business category must not exceed 230 characters',
            'location.otp.max' => 'OTP must not exceed 230 characters',
        ];
    }

    /**
     * Handle a failed validation attempt.
     *
     * @param  \Illuminate\Contracts\Validation\Validator  $validator
     * @return void
     *
     * @throws \Illuminate\Http\Exceptions\HttpResponseException
     */
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(
            response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422)
        );
    }

    /**
     * Get the validated data organized by entity type
     *
     * @return array
     */
    public function getOrganizedData(): array
    {
        $validated = $this->validated();
        
        return [
            'user' => $validated['user'] ?? [],
            'company' => $validated['company'] ?? [],
            'location' => $validated['location'] ?? [],
        ];
    }
}
