<?php

namespace App\Services;

use App\Constants\ZatcaConstants;
use App\Models\BusinessLocation;
use App\Models\Sandbox;
use App\Models\Simulation;
use App\Models\Production;
use App\Services\ZatcaCsidService;
use Exception;
use RuntimeException;

/**
 * ZATCA X.509 Certificate Service
 * Handles X.509 certificate generation and management for ZATCA e-invoicing
 */
class ZatcaX509Service
{
    /**
     * Get X.509 certificate for a specific location
     *
     * @param string $locationId
     * @param string $secret
     * @param string $authorization
     * @return array
     * @throws Exception
     */
    public function getCertificateForLocation(string $locationId, string $secret, string $authorization): array
    {
        $this->validateAuthentication($secret, $authorization);


        $businessLocation = BusinessLocation::where('authentication_token', $secret)->first();

        if (!$businessLocation) {
            throw new RuntimeException('account not found with current secret key');
        }


        $sandbox = Sandbox::where('business_location_id', $businessLocation->id)->first();

        if (!$sandbox) {
            throw new RuntimeException('Sandbox environment not found for this location');
        }

        // Return the actual x509_certificate from database
        $certificate = $sandbox->x509_certificate;

        // If no certificate exists, return empty string
        if (empty($certificate)) {
            $certificate = '';
        }

        return [
            'x509_certificate' => $certificate,
            'certificate_type' => 'sandbox',
            'location_id' => $locationId,
            'generated_at' => now()->toISOString()
        ];
    }

    /**
     * Get certificate information for debugging
     *
     * @param string $locationId
     * @param string $secret
     * @param string $authorization
     * @return array
     * @throws Exception
     */
    public function getCertificateInfo(string $locationId, string $secret, string $authorization): array
    {
        $this->validateAuthentication($secret, $authorization);

        // Find the business location by authentication token
        $businessLocation = BusinessLocation::where('authentication_token', $secret)->first();

        if (!$businessLocation) {
            throw new RuntimeException('account not found with current secret key');
        }

        // Get the sandbox certificate from database
        $sandbox = Sandbox::where('business_location_id', $businessLocation->id)->first();

        if (!$sandbox) {
            throw new RuntimeException('Sandbox environment not found for this location');
        }

        $certificate = $sandbox->x509_certificate ?? '';
        $certificateInfo = $this->parseCertificateInfo($certificate);

        return [
            'location_id' => $locationId,
            'certificate_length' => strlen($certificate),
            'certificate_type' => 'sandbox',
            'certificate_format' => 'base64',
            'certificate_info' => $certificateInfo,
            'valid_for_zatca' => true,
            'environment' => 'sandbox'
        ];
    }

    /**
     * Validate authentication headers (same as compliance endpoint)
     *
     * @param string $secret
     * @param string $authorization
     * @throws Exception
     */
    private function validateAuthentication(string $secret, string $authorization): void
    {

        if (empty($secret) || strlen($secret) < 32) {
            throw new RuntimeException('Invalid secret header format');
        }

        if (empty($authorization) || !str_starts_with($authorization, 'Api-Key ')) {
            throw new RuntimeException('Invalid authorization header format');
        }

        $apiKey = substr($authorization, 8);
        if (empty($apiKey) || strlen($apiKey) < 16) {
            throw new RuntimeException('Invalid API key format');
        }


    }



    /**
     * Generate X509 certificate for compliance controller
     *
     * @param string $locationId
     * @param string $environment
     * @return array
     */
    public function generateX509Certificate(string $locationId, string $environment): array
    {
        try {
            $businessLocation = BusinessLocation::find($locationId);

            if (!$businessLocation) {
                throw new RuntimeException('Location not found');
            }

            // Get the appropriate environment model
            $environmentModel = $this->getEnvironmentModel($businessLocation, $environment);

            if (!$environmentModel) {
                throw new RuntimeException(ucfirst($environment) . ' environment not found for this location');
            }

            // Check if CSID exists (required for X.509 generation)
            if (empty($environmentModel->csid) || empty($environmentModel->secret_csid) || empty($environmentModel->csid_request)) {
                throw new RuntimeException('CSID must be generated first before X.509 certificate generation');
            }

            // Call ZATCA API to generate X.509 certificate
            $csidService = app(ZatcaCsidService::class);
            $result = $csidService->generateX509(
                $environmentModel->csid,
                $environmentModel->secret_csid,
                $environmentModel->csid_request,
                $environment
            );

            $environmentModel->update([
                'x509_certificate' => $result['binarySecurityToken'],
                'x509_base64' => base64_decode($result['binarySecurityToken']),
                'x509_secret' => $result['secret'],
                'x509_request' => $result['requestID'] ?? null,
            ]);

            return [
                'success' => true,
                'data' => [
                    'x509_certificate' => $result['binarySecurityToken'],
                    'environment' => $environment,
                    'location_id' => $locationId,
                    'generated_at' => now()->toISOString()
                ]
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'data' => [
                    'message' => 'Failed to generate X509 certificate: ' . $e->getMessage()
                ]
            ];
        }
    }

    /**
     * Get the appropriate environment model
     *
     * @param BusinessLocation $businessLocation
     * @param string $environment
     * @return Sandbox|Simulation|Production|null
     */
    private function getEnvironmentModel(BusinessLocation $businessLocation, string $environment)
    {
        return match($environment) {
            'sandbox' => Sandbox::where('business_location_id', $businessLocation->id)->first(),
            'simulation' => Simulation::where('business_location_id', $businessLocation->id)->first(),
            'production' => Production::where('business_location_id', $businessLocation->id)->first(),
            default => null
        };
    }

    /**
     * Parse certificate information for debugging
     *
     * @param string $certificate
     * @return array
     */
    private function parseCertificateInfo(string $certificate): array
    {
        try {

            $decodedCert = base64_decode($certificate);

            if ($decodedCert === false) {
                throw new RuntimeException('Invalid base64 certificate format');
            }

            $info = [
                'format' => 'X.509',
                'encoding' => 'base64',
                'size_bytes' => strlen($decodedCert),
                'size_base64' => strlen($certificate),
                'valid_base64' => true,
                'certificate_type' => 'ZATCA Sandbox Certificate',
                'environment' => 'sandbox',
                'compatible_with_zatca' => true
            ];

            if (function_exists('openssl_x509_parse')) {
                $tempFile = tempnam(sys_get_temp_dir(), 'zatca_cert_');
                file_put_contents($tempFile, "-----BEGIN CERTIFICATE-----\n" .
                    chunk_split($certificate, 64, "\n") .
                    "-----END CERTIFICATE-----\n");

                $certResource = openssl_x509_read(file_get_contents($tempFile));
                if ($certResource) {
                    $certInfo = openssl_x509_parse($certResource);
                    if ($certInfo) {
                        $info['subject'] = $certInfo['subject'] ?? [];
                        $info['issuer'] = $certInfo['issuer'] ?? [];
                        $info['valid_from'] = isset($certInfo['validFrom_time_t']) ?
                            date('Y-m-d H:i:s', $certInfo['validFrom_time_t']) : null;
                        $info['valid_to'] = isset($certInfo['validTo_time_t']) ?
                            date('Y-m-d H:i:s', $certInfo['validTo_time_t']) : null;
                        $info['serial_number'] = $certInfo['serialNumber'] ?? null;
                    }
                    openssl_x509_free($certResource);
                }
                unlink($tempFile);
            }

            return $info;

        } catch (Exception $e) {
            return [
                'format' => 'X.509',
                'encoding' => 'base64',
                'size_base64' => strlen($certificate),
                'valid_base64' => base64_decode($certificate) !== false,
                'parse_error' => $e->getMessage()
            ];
        }
    }
}
