<?php

namespace App\Services;

use App\Constants\ZatcaConstants;
use DOMDocument;
use DOMXPath;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use RuntimeException;

/**
 * ZATCA Compliance Service
 *
 * Handles ZATCA compliance API integration
 */
class ZatcaComplianceService
{
    private array $endpoints;

    public function __construct()
    {
        $this->endpoints = ZatcaConstants::ZATCA_COMPLIANCE_URLS;
    }

    /**
     * Process compliance check with ZATCA API
     */
    public function processCompliance(array $invoiceData, string $csid, string $secretCsid, string $environment = 'sandbox'): array
    {
        try {

            if (!isset($invoiceData['uuid'])) {
                throw new RuntimeException('Invoice UUID is required for compliance check');
            }

            $requestData = [
                'invoiceHash' => $invoiceData['invoiceHash'],
                'uuid' => $invoiceData['uuid'],
                'invoice' => base64_encode($invoiceData['invoice'])  // ZATCA expects base64 encoded XML
            ];

            // Log request details for debugging
            Log::info("$environment Compliance: Request details", [
                'url' => $this->endpoints[$environment],
                'csid' => substr($csid, 0, 20) . '...',
                'secret_csid' => substr($secretCsid, 0, 20) . '...',
                'request_data_keys' => array_keys($requestData),
                'invoice_hash_length' => strlen($invoiceData['invoiceHash']),
                'invoice_length' => strlen($invoiceData['invoice'])
            ]);

            $response = Http::withHeaders([
                'Accept-Language' => 'en',
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Accept-Version' => 'V2',
            ])
            ->withBasicAuth($csid, $secretCsid)
            ->timeout(30)
            ->post($this->endpoints[$environment], $requestData);

            // Handle both JSON and non-JSON responses
            $responseData = $response->json();

            // If response is not successful, throw error with actual response
            if (!$response->successful()) {
                $errorMessage = $responseData ? json_encode($responseData) : $response->body();
                throw new RuntimeException('ZATCA compliance check failed: ' . $errorMessage);
            }

            // Check if ZATCA returned validation errors
            if ($responseData && isset($responseData['validationResults']['status']) && $responseData['validationResults']['status'] === 'ERROR') {
                throw new RuntimeException('ZATCA compliance check failed: ' . json_encode($responseData));
            }

            return $responseData;

        } catch (Exception $e) {
            throw new RuntimeException('ZATCA compliance check failed: ' . $e->getMessage());
        }
    }

    /**
     * Process clearance with ZATCA API
     */
    public function processClearance(array $invoiceData, string $csid, string $secretCsid, string $environment = 'sandbox'): array
    {
        try {

            if (!isset($invoiceData['uuid'])) {
                throw new RuntimeException('Invoice UUID is required for clearance');
            }

            $requestData = [
                'invoiceHash' => $invoiceData['invoiceHash'],
                'uuid' => $invoiceData['uuid'],
                'invoice' => base64_encode($invoiceData['invoice'])  // ZATCA expects base64 encoded XML
            ];

            // Get clearance URL for environment
            $clearanceUrl = ZatcaConstants::getApiUrl($environment, 'clearance');

            $response = Http::withHeaders([
                'Accept-Language' => 'en',
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Accept-Version' => 'V2',
                'Clearance-Status' => '1',  // Required for clearance API
            ])
            ->withBasicAuth($csid, $secretCsid)
            ->timeout(30)
            ->post($clearanceUrl, $requestData);

            // Handle both JSON and non-JSON responses
            $responseData = $response->json();

            // If response is not successful, throw error with actual response
            if (!$response->successful()) {
                $errorMessage = $responseData ? json_encode($responseData) : $response->body();
                throw new RuntimeException('ZATCA clearance failed: ' . $errorMessage);
            }

            // Check if ZATCA returned validation errors
            if ($responseData &&
                ((isset($responseData['validationResults']['status']) && $responseData['validationResults']['status'] === 'ERROR') ||
                (isset($responseData['clearanceStatus']) && $responseData['clearanceStatus'] === 'NOT_CLEARED'))) {
                throw new RuntimeException('ZATCA clearance failed: ' . json_encode($responseData));
            }

            return $responseData;

        } catch (Exception $e) {
            throw new RuntimeException('ZATCA clearance failed: ' . $e->getMessage());
        }
    }

    /**
     * Process reporting with ZATCA API
     */
    public function processReporting(array $invoiceData, string $csid, string $secretCsid, string $environment = 'sandbox'): array
    {
        try {

            if (!isset($invoiceData['uuid'])) {
                throw new RuntimeException('Invoice UUID is required for reporting');
            }

            $requestData = [
                'invoiceHash' => $invoiceData['invoiceHash'],
                'uuid' => $invoiceData['uuid'],
                'invoice' => base64_encode($invoiceData['invoice'])  // ZATCA expects base64 encoded XML
            ];

            // Get reporting URL for environment
            $reportingUrl = ZatcaConstants::getApiUrl($environment, 'reporting');

            $response = Http::withHeaders([
                'Accept-Language' => 'en',
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'Accept-Version' => 'V2',
                'Clearance-Status' => '1',  // Required for reporting API
            ])
            ->withBasicAuth($csid, $secretCsid)
            ->timeout(30)
            ->post($reportingUrl, $requestData);

            // Handle both JSON and non-JSON responses
            $responseData = $response->json();

            // If response is not successful, throw error with actual response
            if (!$response->successful()) {
                $errorMessage = $responseData ? json_encode($responseData) : $response->body();
                throw new RuntimeException('ZATCA reporting failed: ' . $errorMessage);
            }

            // Check if ZATCA returned validation errors
            if ($responseData &&
                ((isset($responseData['validationResults']['status']) && $responseData['validationResults']['status'] === 'ERROR') ||
                (isset($responseData['reportingStatus']) && $responseData['reportingStatus'] === 'NOT_REPORTED'))) {
                throw new RuntimeException('ZATCA reporting failed: ' . json_encode($responseData));
            }

            return $responseData;

        } catch (Exception $e) {
            throw new RuntimeException('ZATCA reporting failed: ' . $e->getMessage());
        }
    }

    /**
     * Generate ZATCA-compliant UBL XML using template approach
     * Uses official ZATCA sample as template and replaces values with payload data
     * @throws Exception
     */
    public function generateInvoiceXml(array $invoiceData, string $invoiceType = 'standard', string $documentType = 'invoice'): string
    {

        if (!isset($invoiceData['id'])) {
            throw new RuntimeException('Invoice ID is required for XML generation');
        }

        // Select appropriate template based on document type
        $templateFile = $this->getTemplateForDocumentType($invoiceType, $documentType);

        if (!file_exists($templateFile)) {
            throw new RuntimeException(ZatcaConstants::ERROR_TEMPLATE_NOT_FOUND . ": $templateFile");
        }

        if ($invoiceType === 'simplified') {
            return $this->generateSimplifiedInvoiceXml($invoiceData, $documentType);
        }

        $templateXml = file_get_contents($templateFile);

        $dom = new DOMDocument();
        $dom->loadXML($templateXml);
        $xpath = new DOMXPath($dom);

        $xpath->registerNamespace('cbc', ZatcaConstants::NAMESPACE_CBC);
        $xpath->registerNamespace('cac', ZatcaConstants::NAMESPACE_CAC);

        $validationErrors = ZatcaConstants::validateInvoiceData($invoiceData);
        if (!empty($validationErrors)) {
            throw new RuntimeException('Invoice validation failed: ' . implode(', ', $validationErrors));
        }

        $invoiceId = $invoiceData['id'];
        $uuid = $invoiceData['uuid'];
        $issueDate = $invoiceData['issueDate'];
        $issueTime = $invoiceData['issueTime'];
        $profileID = $invoiceData['profileID'];
        $additionalDocRef = $invoiceData['additionalDocumentReference'];
        $icv = $additionalDocRef['uuid'];
        $pih = $additionalDocRef['pih'];

        $supplier = $invoiceData['accountingSupplierParty'];
        $supplierName = $supplier['registrationName'];
        $supplierVat = $supplier['companyID'];

        $customer = $invoiceData['accountingCustomerParty'];
        $customerName = $customer['registrationName'];

        $taxTotal = $invoiceData['taxTotal'];
        $taxAmount = $taxTotal['taxAmount'];

        $monetary = $invoiceData['legalMonetaryTotal'];
        $lineExtension = $monetary['lineExtensionAmount'];

        $lineExtensionFloat = (float)$lineExtension;
        $taxAmountFloat = (float)$taxAmount;
        $taxInclusiveFloat = $lineExtensionFloat + $taxAmountFloat;
        $payableAmountFloat = $taxInclusiveFloat;

        $lineExtensionFormatted = number_format($lineExtensionFloat, 2, '.', '');
        $taxExclusiveFormatted = $lineExtensionFormatted;
        $taxAmountFormatted = number_format($taxAmountFloat, 2, '.', '');
        $taxInclusiveFormatted = number_format($taxInclusiveFloat, 2, '.', '');
        $payableAmountFormatted = number_format($payableAmountFloat, 2, '.', '');

        $invoiceTypeCode = ZatcaConstants::getInvoiceTypeCode($invoiceType, $documentType);
        $invoiceTypeCodeName = ZatcaConstants::getInvoiceTypeCodeName($invoiceType, $documentType);

        $invoiceTypeCodeNode = $xpath->query('//cbc:InvoiceTypeCode')->item(0);
        if ($invoiceTypeCodeNode) {
            $invoiceTypeCodeNode->nodeValue = $invoiceTypeCode;
            $invoiceTypeCodeNode->setAttribute('name', $invoiceTypeCodeName);
        }

        if ($invoiceType === 'standard') {
            $this->updateXmlNode($xpath, '//cbc:ID', $invoiceId);
            $this->updateXmlNode($xpath, '//cbc:UUID', $uuid);
            $this->updateXmlNode($xpath, '//cbc:IssueDate', $issueDate);
            $this->updateXmlNode($xpath, '//cbc:IssueTime', $issueTime);
            $this->updateXmlNode($xpath, '//cbc:ProfileID', $profileID);
        }

        // Only update additional document references for standard invoices
        if ($invoiceType === 'standard') {
            $this->updateXmlNode($xpath, '//cac:AdditionalDocumentReference[cbc:ID="ICV"]/cbc:UUID', $icv);
            $this->updateXmlNode($xpath, '//cac:AdditionalDocumentReference[cbc:ID="PIH"]//cbc:EmbeddedDocumentBinaryObject', $pih);
        }

        // Only update supplier/customer information for standard invoices
        if ($invoiceType === 'standard') {
            // Update supplier information
            $this->updateXmlNode($xpath, '//cac:AccountingSupplierParty//cbc:RegistrationName', $supplierName);
            $this->updateXmlNode($xpath, '//cac:AccountingSupplierParty//cac:PartyTaxScheme/cbc:CompanyID', $supplierVat);
            $this->updateXmlNode($xpath, '//cac:AccountingSupplierParty//cac:PartyIdentification/cbc:ID', $supplier['id']);
            $this->updateXmlNode($xpath, '//cac:AccountingSupplierParty//cbc:StreetName', $supplier['streetName']);
            $this->updateXmlNode($xpath, '//cac:AccountingSupplierParty//cbc:BuildingNumber', $supplier['buildingNumber']);
            $this->updateXmlNode($xpath, '//cac:AccountingSupplierParty//cbc:CitySubdivisionName', $supplier['citySubdivisionName']);
            $this->updateXmlNode($xpath, '//cac:AccountingSupplierParty//cbc:CityName', $supplier['cityName']);
            $this->updateXmlNode($xpath, '//cac:AccountingSupplierParty//cbc:PostalZone', $supplier['postalZone']);

            // Update customer information
            $this->updateXmlNode($xpath, '//cac:AccountingCustomerParty//cbc:RegistrationName', $customerName);
            $this->updateXmlNode($xpath, '//cac:AccountingCustomerParty//cac:PartyTaxScheme/cbc:CompanyID', $customer['companyID']);
            $this->updateXmlNode($xpath, '//cac:AccountingCustomerParty//cbc:StreetName', $customer['streetName']);
            $this->updateXmlNode($xpath, '//cac:AccountingCustomerParty//cbc:BuildingNumber', $customer['buildingNumber']);
            $this->updateXmlNode($xpath, '//cac:AccountingCustomerParty//cbc:CitySubdivisionName', $customer['citySubdivisionName']);
            $this->updateXmlNode($xpath, '//cac:AccountingCustomerParty//cbc:CityName', $customer['cityName']);
            $this->updateXmlNode($xpath, '//cac:AccountingCustomerParty//cbc:PostalZone', $customer['postalZone']);
        }

        // Only update delivery date and payment means for standard invoices
        if ($invoiceType === 'standard') {
            // Update delivery date
            if (isset($invoiceData['actualDeliveryDate'])) {
                $this->updateXmlNode($xpath, '//cac:Delivery/cbc:ActualDeliveryDate', $invoiceData['actualDeliveryDate']);
            }

            // Update payment means code
            if (isset($invoiceData['paymentMeansCode'])) {
                $this->updateXmlNode($xpath, '//cac:PaymentMeans/cbc:PaymentMeansCode', $invoiceData['paymentMeansCode']);
            }
        }

        // Handle billing reference for credit/debit notes
        if (isset($invoiceData['billingReference'])) {
            $billingRef = $invoiceData['billingReference']['invoiceDocumentReference'];
            $this->updateXmlNode($xpath, '//cac:BillingReference/cac:InvoiceDocumentReference/cbc:ID', $billingRef['id']);
            $this->updateXmlNode($xpath, '//cac:BillingReference/cac:InvoiceDocumentReference/cbc:IssueDate', $billingRef['issueDate']);
        }

        // Handle instruction note for credit/debit notes
        if (isset($invoiceData['instructionNote'])) {
            $this->updateXmlNode($xpath, '//cbc:Note', $invoiceData['instructionNote']);
        }

        // Handle credit/debit reason (KSA-10)
        if (isset($invoiceData['creditDebitReason'])) {
            // For credit/debit notes, the reason goes in PaymentMeans InstructionNote
            $this->updateXmlNode($xpath, '//cac:PaymentMeans/cbc:InstructionNote', $invoiceData['creditDebitReason']);
        }

        // Handle supply dates for simplified invoices (KSA-5 and KSA-24)
        if ($invoiceType === 'simplified') {
            $supplyDate = $invoiceData['actualDeliveryDate'] ?? $invoiceData['issueDate'];
            $supplyEndDate = $invoiceData['latestDeliveryDate'] ?? $invoiceData['issueDate'];

            $this->updateXmlNode($xpath, '//cac:Delivery/cbc:ActualDeliveryDate', $supplyDate);
            $this->updateXmlNode($xpath, '//cac:Delivery/cbc:LatestDeliveryDate', $supplyEndDate);
        }

        // Only update tax amounts for standard invoices, keep simplified template values
        if ($invoiceType === 'standard') {
            $taxAmountNodes = $xpath->query('//cac:TaxTotal/cbc:TaxAmount');
            foreach ($taxAmountNodes as $node) {
                $node->nodeValue = $taxAmountFormatted;
            }
        }

        // Only update monetary amounts for standard invoices, keep simplified template values
        if ($invoiceType === 'standard') {
            $this->updateXmlNode($xpath, '//cac:TaxSubtotal/cbc:TaxableAmount', $lineExtensionFormatted);
            $this->updateXmlNode($xpath, '//cac:TaxSubtotal/cbc:TaxAmount', $taxAmountFormatted);

            $this->updateXmlNode($xpath, '//cac:LegalMonetaryTotal/cbc:LineExtensionAmount', $lineExtensionFormatted);
            $this->updateXmlNode($xpath, '//cac:LegalMonetaryTotal/cbc:TaxExclusiveAmount', $taxExclusiveFormatted);
            $this->updateXmlNode($xpath, '//cac:LegalMonetaryTotal/cbc:TaxInclusiveAmount', $taxInclusiveFormatted);
            $this->updateXmlNode($xpath, '//cac:LegalMonetaryTotal/cbc:PayableAmount', $payableAmountFormatted);
        }

        $invoiceLines = $invoiceData['invoiceLines'];
        if (empty($invoiceLines)) {
            throw new RuntimeException('Invoice lines are required');
        }

        $firstLine = $invoiceLines[0];
        $quantity = (float)$firstLine['invoicedQuantity'];
        $unitPrice = $lineExtensionFloat / $quantity;
        $unitPriceFormatted = number_format($unitPrice, 2, '.', '');

        // Only update invoice line amounts for standard invoices
        if ($invoiceType === 'standard') {
            $this->updateXmlNode($xpath, '//cac:InvoiceLine/cbc:InvoicedQuantity', number_format($quantity, 4, '.', ''));
            $this->updateXmlNode($xpath, '//cac:InvoiceLine/cbc:LineExtensionAmount', $lineExtensionFormatted);
            $this->updateXmlNode($xpath, '//cac:InvoiceLine/cac:TaxTotal/cbc:TaxAmount', $taxAmountFormatted);
            $this->updateXmlNode($xpath, '//cac:InvoiceLine/cac:TaxTotal/cbc:RoundingAmount', $taxInclusiveFormatted);
            $this->updateXmlNode($xpath, '//cac:InvoiceLine/cac:Price/cbc:PriceAmount', $unitPriceFormatted);

            $itemName = $firstLine['itemName'];
            $this->updateXmlNode($xpath, '//cac:InvoiceLine/cac:Item/cbc:Name', $itemName);
            
            // Fix BR-KSA-EN16931-06: Charge on price level (BG-29) ChargeIndicator must be 'false'
            $this->updateXmlNode($xpath, '//cac:InvoiceLine/cac:Price/cac:AllowanceCharge/cbc:ChargeIndicator', 'false');
        }

        // Return raw XML string (not base64 encoded)
        $xmlString = $dom->saveXML();
        return $xmlString;
    }

    /**
     * Get the appropriate ZATCA template file based on document type
     */
    private function getTemplateForDocumentType(string $invoiceType, string $documentType): string
    {
        $basePath = '/var/www/html/storage/app/zatca-sdk/zatca-einvoicing-sdk-Java-238-R3.4.1/Data/Samples';

        // Determine invoice type folder
        $typeFolder = ucfirst($invoiceType); // 'Standard' or 'Simplified'

        // Determine document type folder and file
        switch ($documentType) {
            case 'credit_note':
                return "$basePath/$typeFolder/Credit/{$typeFolder}_Credit_Note.xml";
            case 'debit_note':
                return "$basePath/$typeFolder/Debit/{$typeFolder}_Debit_Note.xml";
            case 'invoice':
            default:
                return "$basePath/$typeFolder/Invoice/{$typeFolder}_Invoice.xml";
        }
    }

    /**
     * Helper method to update XML node value safely
     */
    private function updateXmlNode($xpath, $query, $value)
    {
        $node = $xpath->query($query)->item(0);
        if ($node) {
            $node->nodeValue = htmlspecialchars($value);
            return true;
        }
        return false;
    }

    /**
     * Generate simplified invoice XML
     * This ensures proper hash calculation and QR code generation
     */
    private function generateSimplifiedInvoiceXml(array $invoiceData, string $documentType): string
    {
        // For simplified invoices, validate only required fields (accountingCustomerParty is optional)
        $requiredFields = [
            'id', 'uuid', 'issueDate', 'issueTime', 'profileID',
            'documentCurrencyCode', 'taxCurrencyCode', 'additionalDocumentReference',
            'accountingSupplierParty', 'taxTotal', 'legalMonetaryTotal', 'invoiceLines'
        ];

        $validationErrors = [];
        foreach ($requiredFields as $field) {
            if (empty($invoiceData[$field])) {
                $validationErrors[] = "Missing required field: $field";
            }
        }

        if (!empty($validationErrors)) {
            throw new RuntimeException('Invoice validation failed: ' . implode(', ', $validationErrors));
        }

        // Extract data
        $invoiceId = $invoiceData['id'];
        $uuid = $invoiceData['uuid'];
        $issueDate = $invoiceData['issueDate'];
        $issueTime = $invoiceData['issueTime'];
        $profileID = $invoiceData['profileID'];
        $additionalDocRef = $invoiceData['additionalDocumentReference'];
        $icv = $additionalDocRef['uuid'];
        $pih = $additionalDocRef['pih'];

        $supplier = $invoiceData['accountingSupplierParty'];
        $supplierName = htmlspecialchars($supplier['registrationName']);
        $supplierVat = $supplier['companyID'];
        $supplierStreet = htmlspecialchars($supplier['streetName']);
        $supplierBuilding = $supplier['buildingNumber'];
        $supplierSubdivision = htmlspecialchars($supplier['citySubdivisionName']);
        $supplierCity = htmlspecialchars($supplier['cityName']);
        $supplierPostal = $supplier['postalZone'];

        $customer = $invoiceData['accountingCustomerParty'] ?? [];
        $customerName = isset($customer['registrationName']) ? htmlspecialchars($customer['registrationName']) : '';
        $customerVat = $customer['companyID'] ?? '';
        $customerStreet = isset($customer['streetName']) ? htmlspecialchars($customer['streetName']) : '';
        $customerBuilding = $customer['buildingNumber'] ?? '';
        $customerSubdivision = isset($customer['citySubdivisionName']) ? htmlspecialchars($customer['citySubdivisionName']) : '';
        $customerCity = isset($customer['cityName']) ? htmlspecialchars($customer['cityName']) : '';
        $customerPostal = $customer['postalZone'] ?? '';

        $taxTotal = $invoiceData['taxTotal'];
        $taxAmount = $taxTotal['taxAmount'];
        $monetary = $invoiceData['legalMonetaryTotal'];
        $lineExtension = $monetary['lineExtensionAmount'];

        // Calculate amounts
        $lineExtensionFloat = (float)$lineExtension;
        $taxAmountFloat = (float)$taxAmount;
        $taxInclusiveFloat = $lineExtensionFloat + $taxAmountFloat;
        $payableAmountFloat = $taxInclusiveFloat;

        $lineExtensionFormatted = number_format($lineExtensionFloat, 2, '.', '');
        $taxExclusiveFormatted = $lineExtensionFormatted;
        $taxAmountFormatted = number_format($taxAmountFloat, 2, '.', '');
        $taxInclusiveFormatted = number_format($taxInclusiveFloat, 2, '.', '');
        $payableAmountFormatted = number_format($payableAmountFloat, 2, '.', '');

        // Set invoice type code based on document type
        $invoiceTypeCode = ZatcaConstants::getInvoiceTypeCode('simplified', $documentType);
        $invoiceTypeCodeName = ZatcaConstants::getInvoiceTypeCodeName('simplified', $documentType);

        // Handle supply dates for simplified invoices
        $supplyDate = $invoiceData['actualDeliveryDate'] ?? $issueDate;
        $supplyEndDate = $invoiceData['latestDeliveryDate'] ?? $issueDate;

        // Handle payment means code
        $paymentMeansCode = $invoiceData['paymentMeansCode'];

        // Handle billing reference for credit/debit notes
        $billingReferenceXml = '';
        if (isset($invoiceData['billingReference'])) {
            $billingRef = $invoiceData['billingReference']['invoiceDocumentReference'];
            $billingReferenceXml = "
    <cac:BillingReference>
        <cac:InvoiceDocumentReference>
            <cbc:ID>" . htmlspecialchars($billingRef['id']) . "</cbc:ID>
            <cbc:IssueDate>" . htmlspecialchars($billingRef['issueDate']) . "</cbc:IssueDate>
        </cac:InvoiceDocumentReference>
    </cac:BillingReference>";
        }

        // Handle instruction note for credit/debit notes
        $instructionNote = $invoiceData['instructionNote'] ?? 'Invoice note';

        // Handle credit/debit reason
        $creditDebitReasonXml = '';
        if (isset($invoiceData['creditDebitReason'])) {
            $creditDebitReasonXml = "
        <cbc:InstructionNote>" . htmlspecialchars($invoiceData['creditDebitReason']) . "</cbc:InstructionNote>";
        }

        // Generate invoice lines
        $invoiceLines = $invoiceData['invoiceLines'];
        if (empty($invoiceLines)) {
            throw new RuntimeException('Invoice lines are required');
        }

        $invoiceLinesXml = '';
        foreach ($invoiceLines as $index => $line) {
            $lineId = $index + 1;
            $quantity = (float)$line['invoicedQuantity'];
            $unitPrice = $lineExtensionFloat / $quantity;
            $unitPriceFormatted = number_format($unitPrice, 2, '.', '');
            $itemName = htmlspecialchars($line['itemName']);

            $invoiceLinesXml .= "
    <cac:InvoiceLine>
        <cbc:ID>$lineId</cbc:ID>
        <cbc:InvoicedQuantity unitCode=\"PCE\">" . number_format($quantity, 6, '.', '') . "</cbc:InvoicedQuantity>
        <cbc:LineExtensionAmount currencyID=\"SAR\">$lineExtensionFormatted</cbc:LineExtensionAmount>
        <cac:TaxTotal>
            <cbc:TaxAmount currencyID=\"SAR\">$taxAmountFormatted</cbc:TaxAmount>
            <cbc:RoundingAmount currencyID=\"SAR\">$taxInclusiveFormatted</cbc:RoundingAmount>
        </cac:TaxTotal>
        <cac:Item>
            <cbc:Name>$itemName</cbc:Name>
            <cac:ClassifiedTaxCategory>
                <cbc:ID>S</cbc:ID>
                <cbc:Percent>15.00</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
        </cac:Item>
        <cac:Price>
            <cbc:PriceAmount currencyID=\"SAR\">$unitPriceFormatted</cbc:PriceAmount>
            <cac:AllowanceCharge>
                <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
                <cbc:AllowanceChargeReason>discount</cbc:AllowanceChargeReason>
                <cbc:Amount currencyID=\"SAR\">0.00</cbc:Amount>
            </cac:AllowanceCharge>
        </cac:Price>
    </cac:InvoiceLine>";
        }


        $xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>
<Invoice xmlns=\"urn:oasis:names:specification:ubl:schema:xsd:Invoice-2\" xmlns:cac=\"urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2\" xmlns:cbc=\"urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2\" xmlns:ext=\"urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2\">
<ext:UBLExtensions>
</ext:UBLExtensions>
    <cbc:ProfileID>$profileID</cbc:ProfileID>
    <cbc:ID>$invoiceId</cbc:ID>
    <cbc:UUID>$uuid</cbc:UUID>
    <cbc:IssueDate>$issueDate</cbc:IssueDate>
    <cbc:IssueTime>$issueTime</cbc:IssueTime>
    <cbc:InvoiceTypeCode name=\"$invoiceTypeCodeName\">$invoiceTypeCode</cbc:InvoiceTypeCode>
    <cbc:Note languageID=\"ar\">$instructionNote</cbc:Note>
    <cbc:DocumentCurrencyCode>SAR</cbc:DocumentCurrencyCode>
    <cbc:TaxCurrencyCode>SAR</cbc:TaxCurrencyCode>";

        // Add billing reference right after document header (UBL 2.1 order)
        $xml .= $billingReferenceXml;

        $xml .= "
    <cac:AdditionalDocumentReference>
        <cbc:ID>ICV</cbc:ID>
        <cbc:UUID>$icv</cbc:UUID>
    </cac:AdditionalDocumentReference>
    <cac:AdditionalDocumentReference>
        <cbc:ID>PIH</cbc:ID>
        <cac:Attachment>
            <cbc:EmbeddedDocumentBinaryObject mimeCode=\"text/plain\">$pih</cbc:EmbeddedDocumentBinaryObject>
        </cac:Attachment>
    </cac:AdditionalDocumentReference>
    <cac:AdditionalDocumentReference>
        <cbc:ID>QR</cbc:ID>
        <cac:Attachment>
            <cbc:EmbeddedDocumentBinaryObject mimeCode=\"text/plain\">PLACEHOLDER_QR_CODE</cbc:EmbeddedDocumentBinaryObject>
        </cac:Attachment>
    </cac:AdditionalDocumentReference>
    <cac:Signature>
        <cbc:ID>urn:oasis:names:specification:ubl:signature:Invoice</cbc:ID>
        <cbc:SignatureMethod>urn:oasis:names:specification:ubl:dsig:enveloped:xades</cbc:SignatureMethod>
    </cac:Signature>
    <cac:AccountingSupplierParty>
        <cac:Party>
            <cac:PartyIdentification>
                <cbc:ID schemeID=\"CRN\">" . htmlspecialchars($supplier['id']) . "</cbc:ID>
            </cac:PartyIdentification>
            <cac:PostalAddress>
                <cbc:StreetName>$supplierStreet</cbc:StreetName>
                <cbc:BuildingNumber>$supplierBuilding</cbc:BuildingNumber>
                <cbc:CitySubdivisionName>$supplierSubdivision</cbc:CitySubdivisionName>
                <cbc:CityName>$supplierCity</cbc:CityName>
                <cbc:PostalZone>$supplierPostal</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>SA</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
                <cbc:CompanyID>$supplierVat</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>$supplierName</cbc:RegistrationName>
            </cac:PartyLegalEntity>
        </cac:Party>
    </cac:AccountingSupplierParty>";

        // Add customer party (simplified invoices can have empty customer for B2C)
        if (!empty($customerName) || !empty($customerVat)) {
            $xml .= "
    <cac:AccountingCustomerParty>
        <cac:Party>
            <cac:PostalAddress>
                <cbc:StreetName>$customerStreet</cbc:StreetName>
                <cbc:BuildingNumber>$customerBuilding</cbc:BuildingNumber>
                <cbc:CitySubdivisionName>$customerSubdivision</cbc:CitySubdivisionName>
                <cbc:CityName>$customerCity</cbc:CityName>
                <cbc:PostalZone>$customerPostal</cbc:PostalZone>
                <cac:Country>
                    <cbc:IdentificationCode>SA</cbc:IdentificationCode>
                </cac:Country>
            </cac:PostalAddress>";

            if (!empty($customerVat)) {
                $xml .= "
            <cac:PartyTaxScheme>
                <cbc:CompanyID>$customerVat</cbc:CompanyID>
                <cac:TaxScheme>
                    <cbc:ID>VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:PartyTaxScheme>";
            }

            if (!empty($customerName)) {
                $xml .= "
            <cac:PartyLegalEntity>
                <cbc:RegistrationName>$customerName</cbc:RegistrationName>
            </cac:PartyLegalEntity>";
            }

            $xml .= "
        </cac:Party>
    </cac:AccountingCustomerParty>";
        } else {
            // Empty customer party for B2C simplified invoices
            $xml .= "
    <cac:AccountingCustomerParty/>";
        }

        $xml .= "
    <cac:Delivery>
        <cbc:ActualDeliveryDate>$supplyDate</cbc:ActualDeliveryDate>
        <cbc:LatestDeliveryDate>$supplyEndDate</cbc:LatestDeliveryDate>
    </cac:Delivery>
    <cac:PaymentMeans>
        <cbc:PaymentMeansCode>$paymentMeansCode</cbc:PaymentMeansCode>$creditDebitReasonXml
    </cac:PaymentMeans>
    <cac:AllowanceCharge>
        <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
        <cbc:AllowanceChargeReason>discount</cbc:AllowanceChargeReason>
        <cbc:Amount currencyID=\"SAR\">0.00</cbc:Amount>
        <cac:TaxCategory>
            <cbc:ID schemeID=\"UN/ECE 5305\" schemeAgencyID=\"6\">S</cbc:ID>
            <cbc:Percent>15</cbc:Percent>
            <cac:TaxScheme>
                <cbc:ID schemeID=\"UN/ECE 5153\" schemeAgencyID=\"6\">VAT</cbc:ID>
            </cac:TaxScheme>
        </cac:TaxCategory>
    </cac:AllowanceCharge>
    <cac:TaxTotal>
        <cbc:TaxAmount currencyID=\"SAR\">$taxAmountFormatted</cbc:TaxAmount>
    </cac:TaxTotal>
    <cac:TaxTotal>
        <cbc:TaxAmount currencyID=\"SAR\">$taxAmountFormatted</cbc:TaxAmount>
        <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID=\"SAR\">$lineExtensionFormatted</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID=\"SAR\">$taxAmountFormatted</cbc:TaxAmount>
            <cac:TaxCategory>
                <cbc:ID schemeID=\"UN/ECE 5305\" schemeAgencyID=\"6\">S</cbc:ID>
                <cbc:Percent>15.00</cbc:Percent>
                <cac:TaxScheme>
                    <cbc:ID schemeID=\"UN/ECE 5153\" schemeAgencyID=\"6\">VAT</cbc:ID>
                </cac:TaxScheme>
            </cac:TaxCategory>
        </cac:TaxSubtotal>
    </cac:TaxTotal>
    <cac:LegalMonetaryTotal>
        <cbc:LineExtensionAmount currencyID=\"SAR\">$lineExtensionFormatted</cbc:LineExtensionAmount>
        <cbc:TaxExclusiveAmount currencyID=\"SAR\">$taxExclusiveFormatted</cbc:TaxExclusiveAmount>
        <cbc:TaxInclusiveAmount currencyID=\"SAR\">$taxInclusiveFormatted</cbc:TaxInclusiveAmount>
        <cbc:AllowanceTotalAmount currencyID=\"SAR\">0.00</cbc:AllowanceTotalAmount>
        <cbc:PrepaidAmount currencyID=\"SAR\">0.00</cbc:PrepaidAmount>
        <cbc:PayableAmount currencyID=\"SAR\">$payableAmountFormatted</cbc:PayableAmount>
    </cac:LegalMonetaryTotal>$invoiceLinesXml
</Invoice>";

        return $xml;
    }

}
