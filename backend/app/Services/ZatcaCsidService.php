<?php

namespace App\Services;

use App\Constants\ZatcaConstants;
use Exception;
use Illuminate\Support\Facades\Http;

/**
 * ZATCA CSID Service
 *
 * Handles ZATCA API calls for CSID generation
 */
class ZatcaCsidService
{
    /**
     * Generate CSID from ZATCA API
     *
     * @param string $csr Certificate Signing Request
     * @param string $otp One Time Password
     * @param string $environment 'sandbox', 'simulation', or 'production'
     * @return array
     * @throws Exception
     */
    public function generateCsid(string $csr, string $otp, string $environment): array
    {
        $endpoint = ZatcaConstants::getApiUrl($environment, 'csid');

        // Prepare the request payload
        $payload = [
            'csr' => $csr
        ];

        try {
            $response = Http::timeout(30)
                ->withHeaders([
                    'Content-Type' => 'application/json',
                    'OTP' => $otp,
                    'Accept-Version' => 'V2',
                    'Accept' => 'application/json'
                ])
                ->post($endpoint, $payload);

            if (!$response->successful()) {
                throw new Exception('ZATCA API Error: ' . $response->body());
            }

            $responseData = $response->json();

            // Validate response structure
            if (!isset($responseData['binarySecurityToken']) || !isset($responseData['secret'])) {
                throw new Exception('Invalid ZATCA response structure');
            }

            return [
                'binarySecurityToken' => $responseData['binarySecurityToken'],
                'secret' => $responseData['secret'],
                'requestID' => $responseData['requestID'] ?? null,
                'status_code' => $response->status()
            ];

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Generate X509 certificate from ZATCA API
     *
     * @param string $csid
     * @param string $secret
     * @param string $requestId
     * @param string $environment
     * @return array
     * @throws Exception
     */
    public function generateX509(string $csid, string $secret, string $requestId, string $environment): array
    {
        $endpoint = ZatcaConstants::getApiUrl($environment, 'x509');

        $payload = [
            'compliance_request_id' => $requestId
        ];

        try {
            $response = Http::timeout(30)
                ->withHeaders([
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'Accept-Version' => 'V2'
                ])
                ->withBasicAuth($csid, $secret)
                ->post($endpoint, $payload);

            if (!$response->successful()) {
                throw new Exception('ZATCA X509 API Error: ' . $response->body());
            }

            $responseData = $response->json();

            return [
                'binarySecurityToken' => $responseData['binarySecurityToken'],
                'secret' => $responseData['secret'],
                'requestID' => $responseData['requestID'] ?? null,
                'status_code' => $response->status()
            ];

        } catch (Exception $e) {
            throw $e;
        }
    }
}
