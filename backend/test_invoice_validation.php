<?php

require_once __DIR__ . '/vendor/autoload.php';

use Modules\ZatcaValidations\Services\ValidationRulesService;
use Illuminate\Support\Facades\Validator;

/**
 * Test script to verify invoice validation works with correct structure
 * Run with: php test_invoice_validation.php
 */

echo "=== Invoice Validation Test ===\n\n";

// Test payload with correct nested structure
$correctPayload = [
    'id' => 'INV-2024-001',
    'uuid' => '550e8400-e29b-41d4-a716-************',
    'issueDate' => '2024-06-28',
    'issueTime' => '10:30:00',
    'profileID' => 'reporting:1.0',
    'documentCurrencyCode' => 'SAR',
    'taxCurrencyCode' => 'SAR',
    'invoiceTypeCode' => '388',
    'accountingSupplierParty' => [
        'companyID' => '***************',
        'partyName' => 'Test Company Ltd',
        'postalAddress' => [
            'streetName' => 'King Fahd Road',
            'cityName' => 'Riyadh',
            'postalZone' => '12345',
            'country' => 'SA'
        ],
        'partyTaxScheme' => []
    ],
    'accountingCustomerParty' => [
        'companyID' => '***************',
        'partyName' => 'Customer Company',
        'postalAddress' => [
            'streetName' => 'Customer Street',
            'cityName' => 'Riyadh',
            'postalZone' => '13714',
            'country' => 'SA'
        ]
    ],
    'invoiceLines' => [
        [
            'id' => '1',
            'invoicedQuantity' => 1.0,
            'lineExtensionAmount' => 100.00,
            'item' => [
                'name' => 'Test Product'  // Correct nested structure
            ],
            'price' => [
                'priceAmount' => 100.00   // Correct nested structure
            ]
        ]
    ],
    'taxTotal' => [
        'taxAmount' => 15.00,
        'taxSubtotals' => [
            [
                'taxableAmount' => 100.00,
                'taxAmount' => 15.00,
                'taxCategory' => [
                    'id' => 'S',
                    'percent' => 15.00
                ]
            ]
        ]
    ],
    'legalMonetaryTotal' => [
        'lineExtensionAmount' => 100.00,
        'taxExclusiveAmount' => 100.00,
        'taxInclusiveAmount' => 115.00,
        'payableAmount' => 115.00
    ],
    'additionalDocumentReference' => [
        'id' => 'ICV',
        'uuid' => 1
    ]
];

// Test payload with incorrect flat structure
$incorrectPayload = [
    'id' => 'INV-2024-002',
    'uuid' => '550e8400-e29b-41d4-a716-************',
    'issueDate' => '2024-06-28',
    'issueTime' => '10:30:00',
    'profileID' => 'reporting:1.0',
    'documentCurrencyCode' => 'SAR',
    'taxCurrencyCode' => 'SAR',
    'invoiceTypeCode' => '388',
    'accountingSupplierParty' => [
        'companyID' => '***************',
        'partyName' => 'Test Company Ltd',
        'postalAddress' => [
            'streetName' => 'King Fahd Road',
            'cityName' => 'Riyadh',
            'postalZone' => '12345',
            'country' => 'SA'
        ],
        'partyTaxScheme' => []
    ],
    'invoiceLines' => [
        [
            'id' => '1',
            'invoicedQuantity' => 1.0,
            'lineExtensionAmount' => 100.00,
            'itemName' => 'Test Product',  // Incorrect flat structure
            'priceAmount' => 100.00        // Incorrect flat structure
        ]
    ],
    'taxTotal' => [
        'taxAmount' => 15.00,
        'taxSubtotals' => [
            [
                'taxableAmount' => 100.00,
                'taxAmount' => 15.00,
                'taxCategory' => [
                    'id' => 'S',
                    'percent' => 15.00
                ]
            ]
        ]
    ],
    'legalMonetaryTotal' => [
        'lineExtensionAmount' => 100.00,
        'taxExclusiveAmount' => 100.00,
        'taxInclusiveAmount' => 115.00,
        'payableAmount' => 115.00
    ],
    'additionalDocumentReference' => [
        'id' => 'ICV',
        'uuid' => 1
    ]
];

echo "Testing correct payload structure...\n";
echo "Expected: PASS (no validation errors)\n";

try {
    $validationService = new ValidationRulesService();
    $rules = $validationService->getInvoiceValidationRules();
    
    $validator = Validator::make($correctPayload, $rules);
    
    if ($validator->fails()) {
        echo "Result: ❌ FAILED\n";
        echo "Errors:\n";
        foreach ($validator->errors()->all() as $error) {
            echo "  - $error\n";
        }
    } else {
        echo "Result: ✅ PASSED\n";
    }
} catch (Exception $e) {
    echo "Result: ❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat('-', 50) . "\n\n";

echo "Testing incorrect payload structure...\n";
echo "Expected: FAIL (missing invoiceLines.*.item.name and invoiceLines.*.price.priceAmount)\n";

try {
    $validator = Validator::make($incorrectPayload, $rules);
    
    if ($validator->fails()) {
        echo "Result: ✅ CORRECTLY FAILED\n";
        echo "Validation errors (as expected):\n";
        foreach ($validator->errors()->all() as $error) {
            echo "  - $error\n";
        }
    } else {
        echo "Result: ❌ UNEXPECTEDLY PASSED\n";
    }
} catch (Exception $e) {
    echo "Result: ❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== Summary ===\n";
echo "The ZATCA validation module now correctly validates:\n";
echo "1. invoiceLines.*.item.name (nested structure) ✅\n";
echo "2. invoiceLines.*.price.priceAmount (nested structure) ✅\n";
echo "3. Rejects flat structure like itemName and priceAmount ✅\n";
echo "\nInternal test payload generators have been updated to use correct structure.\n";
