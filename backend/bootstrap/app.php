<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // Register JWT authentication middleware
        $middleware->alias([
            'auth.jwt' => \App\Http\Middleware\JwtAuthMiddleware::class,
            'auth.company' => \App\Http\Middleware\CompanyApiKeyAuth::class,
            'auth.location' => \App\Http\Middleware\LocationAuthMiddleware::class,
            'installation.required' => \App\Http\Middleware\InstallationMiddleware::class,
            'zatca.prevalidation' => \Modules\ZatcaValidations\Http\Middleware\ZatcaPreValidationMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
