<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Company;
use App\Models\BusinessLocation;

class ZatcaValidationsModuleTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $company;
    protected $businessLocation;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::factory()->create();
        
        // Create test company
        $this->company = Company::factory()->create([
            'user_id' => $this->user->id,
        ]);
        
        // Create test business location
        $this->businessLocation = BusinessLocation::factory()->create([
            'company_id' => $this->company->id,
        ]);
    }

    /**
     * Test getting validation rules for invoice type
     */
    public function test_can_get_invoice_validation_rules()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/zatca-validations/rules/invoice');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'status',
                'data' => [
                    'type',
                    'rules',
                    'messages'
                ]
            ]);
    }

    /**
     * Test getting validation rules for compliance type
     */
    public function test_can_get_compliance_validation_rules()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/zatca-validations/rules/compliance');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'status',
                'data' => [
                    'type',
                    'rules',
                    'messages'
                ]
            ]);
    }

    /**
     * Test invoice validation with valid data
     */
    public function test_can_validate_invoice_with_valid_data()
    {
        $validInvoiceData = [
            'id' => 'INV-001',
            'uuid' => '550e8400-e29b-41d4-a716-************',
            'issueDate' => '2024-06-16',
            'issueTime' => '10:30:00',
            'profileID' => 'reporting:1.0',
            'documentCurrencyCode' => 'SAR',
            'taxCurrencyCode' => 'SAR',
            'invoiceTypeCode' => '388',
            'accountingSupplierParty' => [
                'companyID' => '***************',
                'partyName' => 'Test Company',
                'postalAddress' => [
                    'streetName' => 'Test Street',
                    'cityName' => 'Riyadh',
                    'postalZone' => '12345',
                    'country' => 'SA'
                ],
                'partyTaxScheme' => []
            ],
            'accountingCustomerParty' => [
                'partyName' => 'Customer Company'
            ],
            'taxTotal' => [
                'taxAmount' => 15.00,
                'taxSubtotal' => []
            ],
            'legalMonetaryTotal' => [
                'lineExtensionAmount' => 100.00,
                'taxExclusiveAmount' => 100.00,
                'taxInclusiveAmount' => 115.00,
                'payableAmount' => 115.00
            ],
            'invoiceLines' => [
                [
                    'id' => '1',
                    'invoicedQuantity' => 1,
                    'lineExtensionAmount' => 100.00,
                    'item' => [
                        'name' => 'Test Item'
                    ],
                    'price' => [
                        'priceAmount' => 100.00
                    ]
                ]
            ],
            'additionalDocumentReference' => []
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/zatca-validations/validate/invoice', $validInvoiceData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'status',
                'data'
            ]);
    }

    /**
     * Test invoice validation with invalid data
     */
    public function test_invoice_validation_fails_with_invalid_data()
    {
        $invalidInvoiceData = [
            'id' => '', // Empty required field
            'uuid' => 'invalid-uuid', // Invalid UUID format
            'issueDate' => 'invalid-date', // Invalid date format
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/zatca-validations/validate/invoice', $invalidInvoiceData);

        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'status',
                'message',
                'errors'
            ]);
    }

    /**
     * Test batch validation
     */
    public function test_can_perform_batch_validation()
    {
        $batchData = [
            'validations' => [
                [
                    'type' => 'invoice',
                    'data' => [
                        'id' => 'INV-001',
                        'uuid' => '550e8400-e29b-41d4-a716-************',
                        'issueDate' => '2024-06-16',
                        'issueTime' => '10:30:00',
                        'profileID' => 'reporting:1.0',
                        'documentCurrencyCode' => 'SAR',
                        'taxCurrencyCode' => 'SAR',
                        'invoiceTypeCode' => '388',
                        'accountingSupplierParty' => [
                            'companyID' => '***************',
                            'partyName' => 'Test Company',
                            'postalAddress' => [
                                'streetName' => 'Test Street',
                                'cityName' => 'Riyadh',
                                'postalZone' => '12345',
                                'country' => 'SA'
                            ],
                            'partyTaxScheme' => []
                        ],
                        'accountingCustomerParty' => [
                            'partyName' => 'Customer Company'
                        ],
                        'taxTotal' => [
                            'taxAmount' => 15.00,
                            'taxSubtotal' => []
                        ],
                        'legalMonetaryTotal' => [
                            'lineExtensionAmount' => 100.00,
                            'taxExclusiveAmount' => 100.00,
                            'taxInclusiveAmount' => 115.00,
                            'payableAmount' => 115.00
                        ],
                        'invoiceLines' => [
                            [
                                'id' => '1',
                                'invoicedQuantity' => 1,
                                'lineExtensionAmount' => 100.00,
                                'item' => [
                                    'name' => 'Test Item'
                                ],
                                'price' => [
                                    'priceAmount' => 100.00
                                ]
                            ]
                        ],
                        'additionalDocumentReference' => []
                    ]
                ]
            ]
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/zatca-validations/validate/batch', $batchData);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'status',
                'data'
            ]);
    }

    /**
     * Test unauthorized access
     */
    public function test_unauthorized_access_is_denied()
    {
        $response = $this->getJson('/api/zatca-validations/rules/invoice');
        
        $response->assertStatus(401);
    }

    /**
     * Test public rules endpoint (no authentication required)
     */
    public function test_public_rules_endpoint_works_without_auth()
    {
        $response = $this->getJson('/api/zatca-validations/public/rules/invoice');
        
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'status',
                'data' => [
                    'type',
                    'rules',
                    'messages'
                ]
            ]);
    }
}
