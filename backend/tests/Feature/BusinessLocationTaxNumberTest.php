<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Company;
use App\Models\BusinessLocation;
use Illuminate\Foundation\Testing\RefreshDatabase;

class BusinessLocationTaxNumberTest extends TestCase
{
    use RefreshDatabase;

    protected $company;
    protected $headers;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test company
        $this->company = Company::create([
            'name' => 'Test Company',
            'api_key' => 'test-api-key-12345678901234567890',
        ]);
        
        $this->headers = [
            'Authorization' => 'Bearer ' . $this->company->api_key,
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];
    }

    /** @test */
    public function it_can_create_business_location_with_valid_tax_number()
    {
        $response = $this->postJson('/api/v1/business-locations', [
            'seller_name' => 'Test Seller',
            'tax_no' => '123456789012345',
            'common_name' => 'Test Location',
        ], $this->headers);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'status',
            'message',
            'data' => [
                'id',
                'seller_name',
                'tax_no',
                'common_name',
            ]
        ]);
        
        $this->assertDatabaseHas('business_locations', [
            'tax_no' => '123456789012345',
            'seller_name' => 'Test Seller',
        ]);
    }

    /** @test */
    public function it_can_create_business_location_without_tax_number()
    {
        $response = $this->postJson('/api/v1/business-locations', [
            'seller_name' => 'Test Seller',
            'common_name' => 'Test Location',
        ], $this->headers);

        $response->assertStatus(201);
        
        $this->assertDatabaseHas('business_locations', [
            'seller_name' => 'Test Seller',
            'tax_no' => null,
        ]);
    }

    /** @test */
    public function it_rejects_business_location_with_invalid_tax_number_length()
    {
        $response = $this->postJson('/api/v1/business-locations', [
            'seller_name' => 'Test Seller',
            'tax_no' => '12345678901234', // 14 digits
            'common_name' => 'Test Location',
        ], $this->headers);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['tax_no']);
    }

    /** @test */
    public function it_rejects_business_location_with_non_numeric_tax_number()
    {
        $response = $this->postJson('/api/v1/business-locations', [
            'seller_name' => 'Test Seller',
            'tax_no' => '12345678901234a', // contains letter
            'common_name' => 'Test Location',
        ], $this->headers);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['tax_no']);
    }

    /** @test */
    public function it_rejects_duplicate_tax_number()
    {
        // Create first location
        BusinessLocation::create([
            'company_id' => $this->company->id,
            'tax_no' => '123456789012345',
            'seller_name' => 'First Seller',
        ]);

        // Try to create second location with same tax number
        $response = $this->postJson('/api/v1/business-locations', [
            'seller_name' => 'Second Seller',
            'tax_no' => '123456789012345',
            'common_name' => 'Test Location',
        ], $this->headers);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['tax_no']);
    }

    /** @test */
    public function it_can_update_business_location_with_valid_tax_number()
    {
        $location = BusinessLocation::create([
            'company_id' => $this->company->id,
            'seller_name' => 'Original Seller',
        ]);

        $response = $this->putJson("/api/v1/business-locations/{$location->id}", [
            'tax_no' => '123456789012345',
        ], $this->headers);

        $response->assertStatus(200);
        
        $this->assertDatabaseHas('business_locations', [
            'id' => $location->id,
            'tax_no' => '123456789012345',
        ]);
    }

    /** @test */
    public function it_can_update_business_location_keeping_same_tax_number()
    {
        $location = BusinessLocation::create([
            'company_id' => $this->company->id,
            'seller_name' => 'Original Seller',
            'tax_no' => '123456789012345',
        ]);

        $response = $this->putJson("/api/v1/business-locations/{$location->id}", [
            'seller_name' => 'Updated Seller',
            'tax_no' => '123456789012345', // Same tax number
        ], $this->headers);

        $response->assertStatus(200);
        
        $this->assertDatabaseHas('business_locations', [
            'id' => $location->id,
            'seller_name' => 'Updated Seller',
            'tax_no' => '123456789012345',
        ]);
    }

    /** @test */
    public function it_rejects_update_with_duplicate_tax_number()
    {
        // Create two locations
        $location1 = BusinessLocation::create([
            'company_id' => $this->company->id,
            'tax_no' => '123456789012345',
            'seller_name' => 'First Seller',
        ]);

        $location2 = BusinessLocation::create([
            'company_id' => $this->company->id,
            'tax_no' => '987654321098765',
            'seller_name' => 'Second Seller',
        ]);

        // Try to update location2 with location1's tax number
        $response = $this->putJson("/api/v1/business-locations/{$location2->id}", [
            'tax_no' => '123456789012345',
        ], $this->headers);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['tax_no']);
    }
}
