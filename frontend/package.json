{"name": "zatca-admin-panel", "version": "1.0.0", "description": "ZATCA Admin Panel React Application", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "vite preview --port 3000 --host 0.0.0.0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "chart.js": "^4.4.0", "react-chartjs-2": "^5.2.0", "framer-motion": "^10.16.4", "@fortawesome/fontawesome-free": "^6.4.2"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^4.5.0"}, "keywords": ["react", "admin-panel", "zatca", "tailwind"], "author": "ZATCA Team", "license": "MIT"}