# Use Node.js 18 Alpine as base image
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Create node_modules directory with proper permissions
RUN mkdir -p /app/node_modules && chown -R node:node /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy source code
COPY . .

# Ensure proper ownership
RUN chown -R node:node /app

# Switch to node user
USER node

# Expose port 3000
EXPOSE 3000

# Start development server
CMD ["npm", "run", "dev"]
