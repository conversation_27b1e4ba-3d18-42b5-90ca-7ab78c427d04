import React, { createContext, useContext, useState, useEffect } from 'react'

const ThemeContext = createContext()

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

export const ThemeProvider = ({ children }) => {
  const [theme, setTheme] = useState('light') // light, dark
  const [primaryColor, setPrimaryColor] = useState('#1877F2') // Facebook Blue

  useEffect(() => {
    // Load theme from localStorage
    const savedTheme = localStorage.getItem('zatca_theme')
    const savedColor = localStorage.getItem('zatca_primary_color')
    
    if (savedTheme) {
      setTheme(savedTheme)
    } else {
      // Check system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      setTheme(prefersDark ? 'dark' : 'light')
    }
    
    if (savedColor) {
      setPrimaryColor(savedColor)
    }
  }, [])

  useEffect(() => {
    // Apply theme to document
    const root = document.documentElement
    
    if (theme === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
    
    // Update CSS custom properties for primary color
    root.style.setProperty('--primary-color', primaryColor)
    
    // Save to localStorage
    localStorage.setItem('zatca_theme', theme)
    localStorage.setItem('zatca_primary_color', primaryColor)
  }, [theme, primaryColor])

  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light')
  }

  const updatePrimaryColor = (color) => {
    setPrimaryColor(color)
  }

  const value = {
    theme,
    primaryColor,
    toggleTheme,
    updatePrimaryColor,
    isDark: theme === 'dark',
    isLight: theme === 'light'
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}
