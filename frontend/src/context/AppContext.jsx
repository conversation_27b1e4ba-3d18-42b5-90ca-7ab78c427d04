import React, { createContext, useContext, useState, useEffect } from 'react'

const AppContext = createContext()

export const useApp = () => {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error('useApp must be used within an AppProvider')
  }
  return context
}

export const AppProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [currentCompany, setCurrentCompany] = useState(null)
  const [companies, setCompanies] = useState([])
  const [appSettings, setAppSettings] = useState({
    appName: 'ZATCA Admin Panel',
    defaultLanguage: 'en',
    appLogo: null,
  })

  // Load initial data
  useEffect(() => {
    const savedUser = localStorage.getItem('zatca_user')
    const savedCompany = localStorage.getItem('zatca_current_company')
    const savedCompanies = localStorage.getItem('zatca_companies')
    const savedSettings = localStorage.getItem('zatca_app_settings')

    if (savedUser) {
      setUser(JSON.parse(savedUser))
    }

    if (savedCompany) {
      setCurrentCompany(JSON.parse(savedCompany))
    }

    if (savedCompanies) {
      setCompanies(JSON.parse(savedCompanies))
    } else {
      // Initialize with dummy data
      const dummyCompanies = [
        {
          id: 1,
          name: 'Tech Solutions Ltd',
          branches: [
            {
              id: 1,
              sellerName: 'Tech Solutions Ltd',
              vatNumber: '300012345600003',
              organisationName: 'Tech Solutions',
              serialNumber: 'TS001',
              organisationUnit: 'IT Department',
              registeredAddress: 'Riyadh, Saudi Arabia',
              businessCategory: 'Technology',
              documentType: 'B2B',
              commonName: 'tech-solutions'
            }
          ]
        },
        {
          id: 2,
          name: 'Global Trading Co',
          branches: [
            {
              id: 2,
              sellerName: 'Global Trading Co',
              vatNumber: '300012345600004',
              organisationName: 'Global Trading',
              serialNumber: 'GT001',
              organisationUnit: 'Sales Department',
              registeredAddress: 'Jeddah, Saudi Arabia',
              businessCategory: 'Trading',
              documentType: 'Both',
              commonName: 'global-trading'
            }
          ]
        }
      ]
      setCompanies(dummyCompanies)
      setCurrentCompany(dummyCompanies[0])
    }

    if (savedSettings) {
      setAppSettings(JSON.parse(savedSettings))
    }
  }, [])

  // Save data to localStorage when state changes
  useEffect(() => {
    if (user) {
      localStorage.setItem('zatca_user', JSON.stringify(user))
    }
  }, [user])

  useEffect(() => {
    if (currentCompany) {
      localStorage.setItem('zatca_current_company', JSON.stringify(currentCompany))
    }
  }, [currentCompany])

  useEffect(() => {
    if (companies.length > 0) {
      localStorage.setItem('zatca_companies', JSON.stringify(companies))
    }
  }, [companies])

  useEffect(() => {
    localStorage.setItem('zatca_app_settings', JSON.stringify(appSettings))
  }, [appSettings])

  const login = (userData) => {
    setUser(userData)
  }

  const logout = () => {
    setUser(null)
    setCurrentCompany(null)
    localStorage.removeItem('zatca_user')
    localStorage.removeItem('zatca_current_company')
    localStorage.removeItem('zatca_logged_in')
  }

  const switchCompany = (company) => {
    setCurrentCompany(company)
  }

  const addCompany = (company) => {
    const newCompany = {
      ...company,
      id: Date.now(),
      branches: []
    }
    setCompanies(prev => [...prev, newCompany])
    return newCompany
  }

  const updateCompany = (companyId, updates) => {
    setCompanies(prev => 
      prev.map(company => 
        company.id === companyId 
          ? { ...company, ...updates }
          : company
      )
    )
  }

  const deleteCompany = (companyId) => {
    setCompanies(prev => prev.filter(company => company.id !== companyId))
    if (currentCompany?.id === companyId) {
      setCurrentCompany(companies.find(c => c.id !== companyId) || null)
    }
  }

  const addBranch = (companyId, branch) => {
    const newBranch = {
      ...branch,
      id: Date.now()
    }
    
    setCompanies(prev => 
      prev.map(company => 
        company.id === companyId 
          ? { ...company, branches: [...company.branches, newBranch] }
          : company
      )
    )
    
    return newBranch
  }

  const updateAppSettings = (settings) => {
    setAppSettings(prev => ({ ...prev, ...settings }))
  }

  const value = {
    user,
    currentCompany,
    companies,
    appSettings,
    login,
    logout,
    switchCompany,
    addCompany,
    updateCompany,
    deleteCompany,
    addBranch,
    updateAppSettings
  }

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  )
}
