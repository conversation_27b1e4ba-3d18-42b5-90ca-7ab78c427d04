import React, { createContext, useContext, useState, useEffect } from 'react'
import toast from 'react-hot-toast'

// Translation data
const translations = {
  en: {
    // Navigation
    dashboard: 'Dashboard',
    companies: 'Companies',
    documents: 'Documents',
    settings: 'Settings',
    profile: 'Profile',
    logout: 'Logout',

    // Dashboard
    welcomeBack: 'Welcome back',
    totalUsers: 'Total Users',
    totalCompanies: 'Total Companies',
    totalDocuments: 'Total Documents',
    totalRevenue: 'Total Revenue',

    // Common
    save: 'Save',
    cancel: 'Cancel',
    edit: 'Edit',
    delete: 'Delete',
    create: 'Create',
    search: 'Search',
    filter: 'Filter',
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',

    // Forms
    fullName: 'Full Name',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    currentPassword: 'Current Password',
    newPassword: 'New Password',

    // Company/Branch
    companyName: 'Company Name',
    branchName: 'Branch Name',
    environment: 'Environment',
    crn: 'CRN',
    otp: 'OTP',

    // Environment options
    sandbox: 'Sandbox',
    simulation: 'Simulation',
    production: 'Production',

    // Status
    error: 'Error',
    notSubmitted: 'Not Submitted',
    reported: 'Reported',
    cleared: 'Cleared',

    // Messages
    languageSwitched: 'Language switched to English',
    profileUpdated: 'Profile updated successfully',
    passwordChanged: 'Password changed successfully',
    companyCreated: 'Company created successfully',
    branchCreated: 'Branch created successfully',
    dataRefreshed: 'Data refreshed for selected range',
    statusUpdated: 'Status updated successfully'
  },
  ar: {
    // Navigation
    dashboard: '\u0644\u0648\u062D\u0629 \u0627\u0644\u062A\u062D\u0643\u0645',
    companies: '\u0627\u0644\u0634\u0631\u0643\u0627\u062A',
    documents: '\u0627\u0644\u0645\u0633\u062A\u0646\u062F\u0627\u062A',
    settings: '\u0627\u0644\u0625\u0639\u062F\u0627\u062F\u0627\u062A',
    profile: '\u0627\u0644\u0645\u0644\u0641 \u0627\u0644\u0634\u062E\u0635\u064A',
    logout: '\u062A\u0633\u062C\u064A\u0644 \u0627\u0644\u062E\u0631\u0648\u062C',

    // Dashboard
    welcomeBack: '\u0645\u0631\u062D\u0628\u0627\u064B \u0628\u0639\u0648\u062F\u062A\u0643',
    totalUsers: '\u0625\u062C\u0645\u0627\u0644\u064A \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645\u064A\u0646',
    totalCompanies: '\u0625\u062C\u0645\u0627\u0644\u064A \u0627\u0644\u0634\u0631\u0643\u0627\u062A',
    totalDocuments: '\u0625\u062C\u0645\u0627\u0644\u064A \u0627\u0644\u0645\u0633\u062A\u0646\u062F\u0627\u062A',
    totalRevenue: '\u0625\u062C\u0645\u0627\u0644\u064A \u0627\u0644\u0625\u064A\u0631\u0627\u062F\u0627\u062A',

    // Common
    save: '\u062D\u0641\u0638',
    cancel: '\u0625\u0644\u063A\u0627\u0621',
    edit: '\u062A\u0639\u062F\u064A\u0644',
    delete: '\u062D\u0630\u0641',
    create: '\u0625\u0646\u0634\u0627\u0621',
    search: '\u0628\u062D\u062B',
    filter: '\u062A\u0635\u0641\u064A\u0629',
    loading: '\u062C\u0627\u0631\u064A \u0627\u0644\u062A\u062D\u0645\u064A\u0644...',
    success: '\u0646\u062C\u062D',
    error: '\u062E\u0637\u0623',

    // Forms
    fullName: '\u0627\u0644\u0627\u0633\u0645 \u0627\u0644\u0643\u0627\u0645\u0644',
    email: '\u0627\u0644\u0628\u0631\u064A\u062F \u0627\u0644\u0625\u0644\u0643\u062A\u0631\u0648\u0646\u064A',
    password: '\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631',
    confirmPassword: '\u062A\u0623\u0643\u064A\u062F \u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631',
    currentPassword: '\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631 \u0627\u0644\u062D\u0627\u0644\u064A\u0629',
    newPassword: '\u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631 \u0627\u0644\u062C\u062F\u064A\u062F\u0629',

    // Company/Branch
    companyName: '\u0627\u0633\u0645 \u0627\u0644\u0634\u0631\u0643\u0629',
    branchName: '\u0627\u0633\u0645 \u0627\u0644\u0641\u0631\u0639',
    environment: '\u0627\u0644\u0628\u064A\u0626\u0629',
    crn: '\u0631\u0642\u0645 \u0627\u0644\u0633\u062C\u0644 \u0627\u0644\u062A\u062C\u0627\u0631\u064A',
    otp: '\u0631\u0645\u0632 \u0627\u0644\u062A\u062D\u0642\u0642',

    // Environment options
    sandbox: '\u0628\u064A\u0626\u0629 \u0627\u0644\u062A\u062C\u0631\u064A\u0628',
    simulation: '\u0628\u064A\u0626\u0629 \u0627\u0644\u0645\u062D\u0627\u0643\u0627\u0629',
    production: '\u0628\u064A\u0626\u0629 \u0627\u0644\u0625\u0646\u062A\u0627\u062C',

    // Status
    error: '\u062E\u0637\u0623',
    notSubmitted: '\u063A\u064A\u0631 \u0645\u0631\u0633\u0644',
    reported: '\u0645\u064F\u0628\u0644\u063A \u0639\u0646\u0647',
    cleared: '\u0645\u064F\u062E\u0644\u0635',

    // Messages
    languageSwitched: '\u062A\u0645 \u062A\u063A\u064A\u064A\u0631 \u0627\u0644\u0644\u063A\u0629 \u0625\u0644\u0649 \u0627\u0644\u0639\u0631\u0628\u064A\u0629',
    profileUpdated: '\u062A\u0645 \u062A\u062D\u062F\u064A\u062B \u0627\u0644\u0645\u0644\u0641 \u0627\u0644\u0634\u062E\u0635\u064A \u0628\u0646\u062C\u0627\u062D',
    passwordChanged: '\u062A\u0645 \u062A\u063A\u064A\u064A\u0631 \u0643\u0644\u0645\u0629 \u0627\u0644\u0645\u0631\u0648\u0631 \u0628\u0646\u062C\u0627\u062D',
    companyCreated: '\u062A\u0645 \u0625\u0646\u0634\u0627\u0621 \u0627\u0644\u0634\u0631\u0643\u0629 \u0628\u0646\u062C\u0627\u062D',
    branchCreated: '\u062A\u0645 \u0625\u0646\u0634\u0627\u0621 \u0627\u0644\u0641\u0631\u0639 \u0628\u0646\u062C\u0627\u062D',
    dataRefreshed: '\u062A\u0645 \u062A\u062D\u062F\u064A\u062B \u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A \u0644\u0644\u0646\u0637\u0627\u0642 \u0627\u0644\u0645\u062D\u062F\u062F',
    statusUpdated: '\u062A\u0645 \u062A\u062D\u062F\u064A\u062B \u0627\u0644\u062D\u0627\u0644\u0629 \u0628\u0646\u062C\u0627\u062D'
  }
}

const LanguageContext = createContext()

export const useLanguage = () => {
  const context = useContext(LanguageContext)
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider')
  }
  return context
}

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('en')
  const [isRTL, setIsRTL] = useState(false)

  useEffect(() => {
    const savedLanguage = localStorage.getItem('zatca_language') || 'en'
    setLanguage(savedLanguage)
    setIsRTL(savedLanguage === 'ar')

    // Update document direction
    document.documentElement.dir = savedLanguage === 'ar' ? 'rtl' : 'ltr'
    document.documentElement.lang = savedLanguage
  }, [])

  const switchLanguage = (newLanguage) => {
    setLanguage(newLanguage)
    setIsRTL(newLanguage === 'ar')
    localStorage.setItem('zatca_language', newLanguage)

    // Update document direction
    document.documentElement.dir = newLanguage === 'ar' ? 'rtl' : 'ltr'
    document.documentElement.lang = newLanguage

    // Show toast notification
    const message = newLanguage === 'ar' ? translations.ar.languageSwitched : translations.en.languageSwitched
    toast.success(message, {
      icon: newLanguage === 'ar' ? '🇸🇦' : '🇺🇸',
      duration: 3000,
    })
  }

  const t = (key) => {
    return translations[language][key] || key
  }

  const value = {
    language,
    isRTL,
    switchLanguage,
    t
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  )
}
