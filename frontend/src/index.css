@import '@emran-alhaddad/saudi-riyal-font/index.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 3D Flip Animation Styles */
.preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* React Hot Toast Styles - Complete toast styling */
div[data-hot-toast] {
  background: linear-gradient(135deg, #1877F2 0%, #42A5F5 100%) !important;
  color: white !important;
  border: none !important;
  box-shadow: 0 10px 25px rgba(24, 119, 242, 0.4) !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  padding: 12px 16px !important;
}

div[data-hot-toast][data-type="success"] {
  background: linear-gradient(135deg, #10B981 0%, #059669 100%) !important;
  box-shadow: 0 10px 25px rgba(16, 185, 129, 0.4) !important;
}

div[data-hot-toast][data-type="error"] {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important;
  box-shadow: 0 10px 25px rgba(239, 68, 68, 0.4) !important;
}

div[data-hot-toast][data-type="loading"] {
  background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%) !important;
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4) !important;
}

/* Toast container styling */
div[role="status"],
div[role="alert"] {
  background: linear-gradient(135deg, #1877F2 0%, #42A5F5 100%) !important;
  color: white !important;
  border: none !important;
  box-shadow: 0 10px 25px rgba(24, 119, 242, 0.4) !important;
  border-radius: 12px !important;
  font-weight: 500 !important;
  padding: 12px 16px !important;
}

/* Saudi Riyal Currency Support */
.saudi-riyal {
  font-family: 'saudi_riyal', sans-serif;
  font-weight: normal;
}

.icon-saudi_riyal::before {
  content: '\E900';
  font-family: 'saudi_riyal';
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-thumb {
  background: rgba(75, 85, 99, 0.5);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(75, 85, 99, 0.7);
}

/* Glassmorphism effects */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Smooth transitions for all elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 font-medium py-2 px-4 rounded-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 transition-all duration-200;
  }

  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700;
  }

  .glass-card {
    @apply backdrop-blur-lg bg-white/25 dark:bg-black/25 border border-white/20 dark:border-white/10 rounded-xl shadow-xl;
  }

  .gradient-text {
    @apply bg-gradient-to-r from-primary-500 to-blue-600 bg-clip-text text-transparent;
  }

  .sidebar-item {
    @apply flex items-center px-4 py-3 text-gray-700 dark:text-gray-200 hover:bg-primary-50 dark:hover:bg-primary-900/20 hover:text-primary-600 dark:hover:text-primary-400 rounded-lg transition-all duration-200 cursor-pointer;
  }

  .sidebar-item.active {
    @apply bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 font-medium;
  }

  .table-row {
    @apply border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-150;
  }

  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-success {
    @apply bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400;
  }

  .badge-error {
    @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
  }

  .badge-info {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
