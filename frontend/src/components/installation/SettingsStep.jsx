import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'

const SettingsStep = ({ formData, updateFormData, onPrevious, onFinish, canProceed }) => {
  const [errors, setErrors] = useState({})
  const [passwordStrength, setPasswordStrength] = useState(0)

  // Validate email format
  const validateEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  // Calculate password strength
  const calculatePasswordStrength = (password) => {
    let strength = 0
    if (password.length >= 8) strength += 25
    if (/[a-z]/.test(password)) strength += 25
    if (/[A-Z]/.test(password)) strength += 25
    if (/[0-9]/.test(password)) strength += 25
    if (/[^A-Za-z0-9]/.test(password)) strength += 25
    return Math.min(strength, 100)
  }

  // Validate URL format
  const validateUrl = (url) => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  useEffect(() => {
    setPasswordStrength(calculatePasswordStrength(formData.password))
  }, [formData.password])

  const handleInputChange = (field, value) => {
    updateFormData({ [field]: value })
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }

    // Real-time validation
    if (field === 'email' && value && !validateEmail(value)) {
      setErrors(prev => ({ ...prev, email: 'Please enter a valid email address' }))
    }

    if (field === 'appUrl' && value && !validateUrl(value)) {
      setErrors(prev => ({ ...prev, appUrl: 'Please enter a valid URL (e.g., http://localhost:8080)' }))
    }

    if (field === 'confirmPassword' && value !== formData.password) {
      setErrors(prev => ({ ...prev, confirmPassword: 'Passwords do not match' }))
    } else if (field === 'confirmPassword' && value === formData.password) {
      setErrors(prev => ({ ...prev, confirmPassword: '' }))
    }
  }

  const handleFinishClick = () => {
    if (!canProceed) {
      toast.error('Please fill in all required fields correctly')
      return
    }

    // Final validation
    const newErrors = {}
    
    if (!formData.appUrl) newErrors.appUrl = 'Application URL is required'
    else if (!validateUrl(formData.appUrl)) newErrors.appUrl = 'Please enter a valid URL'
    
    if (!formData.fullName) newErrors.fullName = 'Full name is required'
    if (!formData.email) newErrors.email = 'Email is required'
    else if (!validateEmail(formData.email)) newErrors.email = 'Please enter a valid email address'
    
    if (!formData.password) newErrors.password = 'Password is required'
    else if (formData.password.length < 8) newErrors.password = 'Password must be at least 8 characters'
    
    if (!formData.confirmPassword) newErrors.confirmPassword = 'Please confirm your password'
    else if (formData.password !== formData.confirmPassword) newErrors.confirmPassword = 'Passwords do not match'

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      toast.error('Please fix the errors below')
      return
    }

    onFinish()
  }

  const getPasswordStrengthColor = () => {
    if (passwordStrength < 25) return 'bg-red-500'
    if (passwordStrength < 50) return 'bg-yellow-500'
    if (passwordStrength < 75) return 'bg-blue-500'
    return 'bg-green-500'
  }

  const getPasswordStrengthText = () => {
    if (passwordStrength < 25) return 'Weak'
    if (passwordStrength < 50) return 'Fair'
    if (passwordStrength < 75) return 'Good'
    return 'Strong'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, type: "spring" }}
          className="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <i className="fas fa-cog text-2xl text-purple-600 dark:text-purple-400"></i>
        </motion.div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Application Settings
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Configure your application and create an admin account
        </p>
      </div>

      {/* Form */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="space-y-6"
      >
        {/* Application URL */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Application URL *
          </label>
          <div className="relative">
            <input
              type="url"
              value={formData.appUrl}
              onChange={(e) => handleInputChange('appUrl', e.target.value)}
              placeholder="http://localhost:8080"
              className={`input-field ${errors.appUrl ? 'border-red-500 focus:ring-red-500' : ''}`}
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <i className="fas fa-globe text-gray-400"></i>
            </div>
          </div>
          {errors.appUrl && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-sm text-red-600 dark:text-red-400"
            >
              {errors.appUrl}
            </motion.p>
          )}
          <p className="text-xs text-gray-500 dark:text-gray-400">
            The URL where your application will be accessible
          </p>
        </div>

        {/* Full Name */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Full Name *
          </label>
          <div className="relative">
            <input
              type="text"
              value={formData.fullName}
              onChange={(e) => handleInputChange('fullName', e.target.value.replace(/\b\w/g, l => l.toUpperCase()))}
              placeholder="John Doe"
              className={`input-field ${errors.fullName ? 'border-red-500 focus:ring-red-500' : ''}`}
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <i className="fas fa-user text-gray-400"></i>
            </div>
          </div>
          {errors.fullName && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-sm text-red-600 dark:text-red-400"
            >
              {errors.fullName}
            </motion.p>
          )}
        </div>

        {/* Email */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Email Address *
          </label>
          <div className="relative">
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              placeholder="<EMAIL>"
              className={`input-field ${errors.email ? 'border-red-500 focus:ring-red-500' : ''}`}
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {formData.email && validateEmail(formData.email) ? (
                <i className="fas fa-check text-green-500"></i>
              ) : formData.email && !validateEmail(formData.email) ? (
                <i className="fas fa-times text-red-500"></i>
              ) : (
                <i className="fas fa-envelope text-gray-400"></i>
              )}
            </div>
          </div>
          {errors.email && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-sm text-red-600 dark:text-red-400"
            >
              {errors.email}
            </motion.p>
          )}
        </div>

        {/* Password */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Password *
          </label>
          <div className="relative">
            <input
              type="password"
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              placeholder="Enter a strong password"
              className={`input-field ${errors.password ? 'border-red-500 focus:ring-red-500' : ''}`}
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <i className="fas fa-lock text-gray-400"></i>
            </div>
          </div>
          
          {/* Password strength meter */}
          {formData.password && (
            <div className="space-y-1">
              <div className="flex justify-between text-xs">
                <span className="text-gray-600 dark:text-gray-400">Password strength:</span>
                <span className={`font-medium ${
                  passwordStrength < 25 ? 'text-red-600' :
                  passwordStrength < 50 ? 'text-yellow-600' :
                  passwordStrength < 75 ? 'text-blue-600' : 'text-green-600'
                }`}>
                  {getPasswordStrengthText()}
                </span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <motion.div
                  className={`h-2 rounded-full ${getPasswordStrengthColor()}`}
                  initial={{ width: 0 }}
                  animate={{ width: `${passwordStrength}%` }}
                  transition={{ duration: 0.3 }}
                />
              </div>
            </div>
          )}
          
          {errors.password && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-sm text-red-600 dark:text-red-400"
            >
              {errors.password}
            </motion.p>
          )}
        </div>

        {/* Confirm Password */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Confirm Password *
          </label>
          <div className="relative">
            <input
              type="password"
              value={formData.confirmPassword}
              onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
              placeholder="Confirm your password"
              className={`input-field ${errors.confirmPassword ? 'border-red-500 focus:ring-red-500' : ''}`}
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              {formData.confirmPassword && formData.password === formData.confirmPassword ? (
                <i className="fas fa-check text-green-500"></i>
              ) : formData.confirmPassword && formData.password !== formData.confirmPassword ? (
                <i className="fas fa-times text-red-500"></i>
              ) : (
                <i className="fas fa-lock text-gray-400"></i>
              )}
            </div>
          </div>
          {errors.confirmPassword && (
            <motion.p
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-sm text-red-600 dark:text-red-400"
            >
              {errors.confirmPassword}
            </motion.p>
          )}
        </div>
      </motion.div>

      {/* Action buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="flex justify-between pt-4"
      >
        <motion.button
          onClick={onPrevious}
          className="px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-200"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span className="flex items-center space-x-2">
            <i className="fas fa-arrow-left"></i>
            <span>Previous</span>
          </span>
        </motion.button>

        <motion.button
          onClick={handleFinishClick}
          disabled={!canProceed}
          className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
            canProceed
              ? 'bg-green-500 hover:bg-green-600 text-white transform hover:scale-105 shadow-lg hover:shadow-xl'
              : 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
          }`}
          whileHover={canProceed ? { scale: 1.05 } : {}}
          whileTap={canProceed ? { scale: 0.95 } : {}}
        >
          <span className="flex items-center space-x-2">
            <span>Finish Setup</span>
            <i className="fas fa-check"></i>
          </span>
        </motion.button>
      </motion.div>
    </div>
  )
}

export default SettingsStep
