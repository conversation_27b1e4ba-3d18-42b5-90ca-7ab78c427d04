import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

const PrerequisitesStep = ({ onNext, onPrevious }) => {
  const [expandedFaq, setExpandedFaq] = useState(null)

  const prerequisites = [
    {
      name: 'Docker',
      version: '20.10+',
      status: 'verified',
      icon: 'fab fa-docker',
      description: 'Container platform for running the application'
    },
    {
      name: 'Docker Compose',
      version: '2.0+',
      status: 'verified',
      icon: 'fas fa-layer-group',
      description: 'Tool for defining multi-container applications'
    },
    {
      name: 'PHP',
      version: '8.1+',
      status: 'verified',
      icon: 'fab fa-php',
      description: 'Server-side scripting language'
    },
    {
      name: 'MariaDB',
      version: '11.7+',
      status: 'verified',
      icon: 'fas fa-database',
      description: 'Relational database management system'
    },
    {
      name: 'Apache',
      version: '2.4+',
      status: 'verified',
      icon: 'fas fa-server',
      description: 'Web server for hosting the application'
    }
  ]

  const faqItems = [
    {
      question: 'How do I install Docker on Windows?',
      answer: 'Download Docker Desktop from docker.com and follow the installation wizard. Make sure to enable WSL 2 integration for better performance.'
    },
    {
      question: 'What if I already have these services running?',
      answer: 'The application uses Docker containers with specific ports. Make sure ports 8080, 3306, and 8082 are available, or modify the docker-compose.yml file.'
    },
    {
      question: 'Do I need to install PHP and MariaDB separately?',
      answer: 'No, all dependencies are containerized. You only need Docker and Docker Compose installed on your host system.'
    },
    {
      question: 'How much disk space is required?',
      answer: 'The application requires approximately 2GB of disk space for Docker images and data storage.'
    }
  ]

  const toggleFaq = (index) => {
    setExpandedFaq(expandedFaq === index ? null : index)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, type: "spring" }}
          className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <i className="fas fa-check-circle text-2xl text-green-600 dark:text-green-400"></i>
        </motion.div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          System Prerequisites
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Verify that your system meets the requirements
        </p>
      </div>

      {/* Prerequisites table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden"
      >
        <div className="px-4 py-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <h4 className="font-medium text-gray-900 dark:text-white">Required Components</h4>
        </div>
        
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {prerequisites.map((item, index) => (
            <motion.div
              key={item.name}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 * index }}
              className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-150"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
                    <i className={`${item.icon} text-blue-600 dark:text-blue-400`}></i>
                  </div>
                  <div>
                    <h5 className="font-medium text-gray-900 dark:text-white">{item.name}</h5>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{item.description}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-3">
                  <span className="text-sm font-mono text-gray-600 dark:text-gray-400">
                    {item.version}
                  </span>
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2 * index }}
                    className="flex items-center space-x-1"
                  >
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium text-green-600 dark:text-green-400">
                      Verified
                    </span>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* FAQ Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700"
      >
        <div className="px-4 py-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
          <h4 className="font-medium text-gray-900 dark:text-white flex items-center space-x-2">
            <i className="fas fa-question-circle text-blue-500"></i>
            <span>Frequently Asked Questions</span>
          </h4>
        </div>
        
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {faqItems.map((item, index) => (
            <div key={index}>
              <motion.button
                onClick={() => toggleFaq(index)}
                className="w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-150"
                whileHover={{ x: 5 }}
              >
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-900 dark:text-white">
                    {item.question}
                  </span>
                  <motion.i
                    className={`fas fa-chevron-down text-gray-400 transition-transform duration-200 ${
                      expandedFaq === index ? 'rotate-180' : ''
                    }`}
                    animate={{ rotate: expandedFaq === index ? 180 : 0 }}
                  ></motion.i>
                </div>
              </motion.button>
              
              <AnimatePresence>
                {expandedFaq === index && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="overflow-hidden"
                  >
                    <div className="px-4 pb-3 text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800/30">
                      {item.answer}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Action buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="flex justify-between pt-4"
      >
        <motion.button
          onClick={onPrevious}
          className="px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 rounded-lg font-medium hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-200"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span className="flex items-center space-x-2">
            <i className="fas fa-arrow-left"></i>
            <span>Previous</span>
          </span>
        </motion.button>

        <motion.button
          onClick={onNext}
          className="px-6 py-3 bg-primary-500 hover:bg-primary-600 text-white rounded-lg font-medium transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span className="flex items-center space-x-2">
            <span>Next</span>
            <i className="fas fa-arrow-right"></i>
          </span>
        </motion.button>
      </motion.div>
    </div>
  )
}

export default PrerequisitesStep
