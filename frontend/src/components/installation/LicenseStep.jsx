import React, { useState } from 'react'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'

const LicenseStep = ({ formData, updateFormData, onNext, canProceed }) => {
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(false)

  const licenseText = `
ZATCA E-INVOICING MIDDLEWARE LICENSE AGREEMENT

Version 1.0 - Effective Date: January 1, 2024

IMPORTANT: READ CAREFULLY BEFORE USING THIS SOFTWARE

This License Agreement ("Agreement") is a legal agreement between you (either an individual or a single entity) and ZATCA Middleware Solutions ("Company") for the ZATCA E-Invoicing Middleware software product ("Software").

1. GRANT OF LICENSE
Subject to the terms and conditions of this Agreement, Company hereby grants you a non-exclusive, non-transferable license to use the Software for your internal business purposes in connection with Saudi Arabian e-invoicing compliance requirements.

2. PERMITTED USES
You may:
- Install and use the Software on your systems for e-invoicing compliance
- Create backups of the Software for archival purposes
- Use the Software to generate, validate, and submit e-invoices to ZATCA

3. RESTRICTIONS
You may not:
- Distribute, sell, lease, or sublicense the Software
- Reverse engineer, decompile, or disassemble the Software
- Remove or alter any proprietary notices or labels
- Use the Software for any illegal or unauthorized purpose

4. COMPLIANCE
You acknowledge that the Software is designed to facilitate compliance with Saudi Arabian e-invoicing regulations. You are solely responsible for ensuring that your use of the Software complies with all applicable laws and regulations.

5. SUPPORT AND UPDATES
Company may provide updates, patches, and support services at its discretion. Such services, if provided, are subject to additional terms.

6. WARRANTY DISCLAIMER
THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND. COMPANY DISCLAIMS ALL WARRANTIES, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.

7. LIMITATION OF LIABILITY
IN NO EVENT SHALL COMPANY BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES ARISING OUT OF THE USE OF THE SOFTWARE.

8. TERMINATION
This Agreement is effective until terminated. You may terminate it at any time by destroying all copies of the Software. Company may terminate this Agreement if you fail to comply with any term hereof.

9. GOVERNING LAW
This Agreement shall be governed by the laws of the Kingdom of Saudi Arabia.

By clicking "I Accept" below, you acknowledge that you have read this Agreement, understand it, and agree to be bound by its terms and conditions.

For questions regarding this license, please contact: <EMAIL>
  `

  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target
    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 10
    setIsScrolledToBottom(isAtBottom)
  }

  const handleAcceptChange = (e) => {
    const accepted = e.target.checked
    updateFormData({ licenseAccepted: accepted })
    
    if (accepted) {
      toast.success('License agreement accepted')
    }
  }

  const handleNextClick = () => {
    if (!canProceed) {
      toast.error('Please accept the license agreement to continue', {
        icon: '⚠️',
      })
      return
    }
    onNext()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, type: "spring" }}
          className="w-16 h-16 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <i className="fas fa-file-contract text-2xl text-primary-600 dark:text-primary-400"></i>
        </motion.div>
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          License Agreement
        </h3>
        <p className="text-gray-600 dark:text-gray-400">
          Please read and accept the license terms to continue
        </p>
      </div>

      {/* License text container */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="relative"
      >
        <div
          className="h-64 overflow-y-auto p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700 text-sm leading-relaxed"
          onScroll={handleScroll}
        >
          <pre className="whitespace-pre-wrap text-gray-700 dark:text-gray-300 font-mono text-xs">
            {licenseText}
          </pre>
        </div>
        
        {/* Scroll indicator */}
        {!isScrolledToBottom && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute bottom-2 right-2 bg-primary-500 text-white px-2 py-1 rounded text-xs font-medium"
          >
            Scroll to read all
          </motion.div>
        )}
      </motion.div>

      {/* Acceptance checkbox */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="flex items-start space-x-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
      >
        <div className="relative">
          <input
            type="checkbox"
            id="license-accept"
            checked={formData.licenseAccepted}
            onChange={handleAcceptChange}
            className="sr-only"
          />
          <motion.label
            htmlFor="license-accept"
            className={`flex items-center justify-center w-6 h-6 rounded border-2 cursor-pointer transition-all duration-200 ${
              formData.licenseAccepted
                ? 'bg-primary-500 border-primary-500'
                : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 hover:border-primary-400'
            }`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {formData.licenseAccepted && (
              <motion.i
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="fas fa-check text-white text-xs"
              />
            )}
          </motion.label>
        </div>
        <div className="flex-1">
          <label
            htmlFor="license-accept"
            className="text-sm font-medium text-gray-900 dark:text-white cursor-pointer"
          >
            I have read and accept the license agreement
          </label>
          <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
            By checking this box, you agree to be bound by the terms and conditions outlined above.
          </p>
        </div>
      </motion.div>

      {/* Action buttons */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6 }}
        className="flex justify-end pt-4"
      >
        <motion.button
          onClick={handleNextClick}
          disabled={!canProceed}
          className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
            canProceed
              ? 'bg-primary-500 hover:bg-primary-600 text-white transform hover:scale-105 shadow-lg hover:shadow-xl'
              : 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
          }`}
          whileHover={canProceed ? { scale: 1.05 } : {}}
          whileTap={canProceed ? { scale: 0.95 } : { x: [-10, 10, -10, 10, 0] }}
        >
          <span className="flex items-center space-x-2">
            <span>Next</span>
            <i className="fas fa-arrow-right"></i>
          </span>
        </motion.button>
      </motion.div>
    </div>
  )
}

export default LicenseStep
