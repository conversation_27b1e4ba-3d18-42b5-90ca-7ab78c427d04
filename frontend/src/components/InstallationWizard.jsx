import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'

// Step components
import LicenseStep from './installation/LicenseStep'
import PrerequisitesStep from './installation/PrerequisitesStep'
import SettingsStep from './installation/SettingsStep'

const InstallationWizard = ({ onComplete }) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [formData, setFormData] = useState({
    licenseAccepted: false,
    appUrl: '',
    fullName: '',
    email: '',
    password: '',
    confirmPassword: ''
  })

  const steps = [
    {
      id: 'license',
      title: 'License Agreement',
      component: LicenseStep,
      icon: 'fas fa-file-contract'
    },
    {
      id: 'prerequisites',
      title: 'Prerequisites',
      component: PrerequisitesStep,
      icon: 'fas fa-check-circle'
    },
    {
      id: 'settings',
      title: 'Application Settings',
      component: SettingsStep,
      icon: 'fas fa-cog'
    }
  ]

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(prev => prev + 1)
      toast.success('Step completed successfully!')
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
    }
  }

  const handleFinish = () => {
    // Validate final form
    if (!formData.appUrl || !formData.fullName || !formData.email || !formData.password) {
      toast.error('Please fill in all required fields')
      return
    }

    if (formData.password !== formData.confirmPassword) {
      toast.error('Passwords do not match')
      return
    }

    toast.success('Installation completed successfully!')
    onComplete()
  }

  const updateFormData = (updates) => {
    setFormData(prev => ({ ...prev, ...updates }))
  }

  const canProceed = () => {
    switch (currentStep) {
      case 0:
        return formData.licenseAccepted
      case 1:
        return true // Prerequisites are just informational
      case 2:
        return formData.appUrl && formData.fullName && formData.email && formData.password && formData.confirmPassword
      default:
        return false
    }
  }

  const CurrentStepComponent = steps[currentStep].component

  return (
    <div className="min-h-screen flex items-center justify-center p-4 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-200/30 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-200/30 rounded-full blur-3xl"></div>
      </div>

      <div className="relative w-full max-w-4xl">
        {/* Progress indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <motion.div
                  className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                    index <= currentStep
                      ? 'bg-primary-500 border-primary-500 text-white'
                      : 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <i className={step.icon}></i>
                </motion.div>
                
                {index < steps.length - 1 && (
                  <div className={`w-16 h-1 mx-2 rounded-full transition-all duration-300 ${
                    index < currentStep ? 'bg-primary-500' : 'bg-gray-200 dark:bg-gray-700'
                  }`}></div>
                )}
              </div>
            ))}
          </div>
          
          <div className="text-center mt-4">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              {steps[currentStep].title}
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Step {currentStep + 1} of {steps.length}
            </p>
          </div>
        </div>

        {/* Main content card */}
        <motion.div
          className="glass-card p-8 mx-auto max-w-2xl"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <CurrentStepComponent
                formData={formData}
                updateFormData={updateFormData}
                onNext={handleNext}
                onPrevious={handlePrevious}
                onFinish={handleFinish}
                canProceed={canProceed()}
                isFirstStep={currentStep === 0}
                isLastStep={currentStep === steps.length - 1}
              />
            </motion.div>
          </AnimatePresence>
        </motion.div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            ZATCA E-Invoicing Middleware Setup
          </p>
        </div>
      </div>
    </div>
  )
}

export default InstallationWizard
