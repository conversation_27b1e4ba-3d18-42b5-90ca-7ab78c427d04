import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useLanguage } from '../../context/LanguageContext'

const FancyDropdown = ({
  options = [],
  value,
  onChange,
  placeholder = 'Select an option',
  searchable = true, // Default to true for better UX
  icon,
  className = '',
  disabled = false,
  colorCoded = false,
  showIcons = true,
  maxHeight = 'max-h-60'
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const { isRTL } = useLanguage()
  const dropdownRef = useRef(null)

  const filteredOptions = searchable
    ? options.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options

  const selectedOption = options.find(option => option.value === value)

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false)
        setSearchTerm('')
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const getStatusColor = (status) => {
    if (!colorCoded) return ''

    switch (status?.toLowerCase()) {
      case 'error':
        return 'text-red-500 bg-red-50 dark:bg-red-900/20'
      case 'not submitted':
      case 'notsubmitted':
        return 'text-gray-500 bg-gray-50 dark:bg-gray-900/20'
      case 'reported':
        return 'text-yellow-500 bg-yellow-50 dark:bg-yellow-900/20'
      case 'cleared':
        return 'text-green-500 bg-green-50 dark:bg-green-900/20'
      case 'sandbox':
        return 'text-blue-500 bg-blue-50 dark:bg-blue-900/20'
      case 'simulation':
        return 'text-purple-500 bg-purple-50 dark:bg-purple-900/20'
      case 'production':
        return 'text-green-600 bg-green-50 dark:bg-green-900/20'
      default:
        return 'text-gray-600 bg-gray-50 dark:bg-gray-900/20'
    }
  }

  const getStatusIcon = (status) => {
    if (!colorCoded) return null

    switch (status?.toLowerCase()) {
      case 'error':
        return 'fas fa-exclamation-circle'
      case 'not submitted':
      case 'notsubmitted':
        return 'fas fa-clock'
      case 'reported':
        return 'fas fa-flag'
      case 'cleared':
        return 'fas fa-check-circle'
      case 'sandbox':
        return 'fas fa-flask'
      case 'simulation':
        return 'fas fa-play-circle'
      case 'production':
        return 'fas fa-server'
      default:
        return 'fas fa-circle'
    }
  }

  return (
    <div ref={dropdownRef} className={`relative ${className}`}>
      {/* Trigger Button */}
      <motion.button
        type="button"
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className={`w-full flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl transition-all duration-200 ${
          disabled
            ? 'opacity-50 cursor-not-allowed'
            : 'hover:border-primary-400 dark:hover:border-primary-500 focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500'
        } ${isOpen ? 'ring-2 ring-primary-500/20 border-primary-500' : ''} ${
          isRTL ? 'text-right' : 'text-left'
        }`}
        whileHover={!disabled ? { scale: 1.01 } : {}}
        whileTap={!disabled ? { scale: 0.99 } : {}}
      >
        <div className={`flex items-center space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>
          {icon && <i className={`${icon} text-gray-400`} />}
          {selectedOption?.icon && <i className={`${selectedOption.icon} ${getStatusColor(selectedOption.value)}`} />}
          <span className={`${selectedOption ? 'text-gray-900 dark:text-white' : 'text-gray-500'} ${
            colorCoded && selectedOption ? getStatusColor(selectedOption.value) : ''
          }`}>
            {selectedOption ? selectedOption.label : placeholder}
          </span>
        </div>

        <motion.i
          className="fas fa-chevron-down text-gray-400"
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        />
      </motion.button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className={`absolute ${isRTL ? 'right-0' : 'left-0'} mt-2 w-full bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-xl shadow-2xl z-50 max-h-60 overflow-hidden`}
          >
            {/* Search Input - Only show if searchable and has many options */}
            {searchable && options.length > 5 && (
              <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                <div className="relative">
                  <i className={`fas fa-search absolute ${isRTL ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`} />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    placeholder="Search..."
                    className={`w-full ${isRTL ? 'pr-10 pl-3' : 'pl-10 pr-3'} py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200`}
                  />
                </div>
              </div>
            )}

            {/* Options */}
            <div className={`${maxHeight} overflow-y-auto custom-scrollbar`}>
              {filteredOptions.length === 0 ? (
                <div className="px-4 py-3 text-gray-500 text-center">
                  No options found
                </div>
              ) : (
                filteredOptions.map((option, index) => (
                  <motion.button
                    key={option.value}
                    type="button"
                    onClick={() => {
                      onChange(option.value)
                      setIsOpen(false)
                      setSearchTerm('')
                    }}
                    className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gradient-to-r hover:from-blue-50 hover:to-teal-50 dark:hover:from-blue-900/20 dark:hover:to-teal-900/20 transition-all duration-300 rounded-lg mx-2 my-1 ${
                      value === option.value
                        ? 'bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 text-primary-600 dark:text-primary-400 shadow-lg'
                        : 'text-gray-700 dark:text-gray-300'
                    } ${isRTL ? 'space-x-reverse text-right' : ''}`}
                    initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.03, type: "spring", stiffness: 100 }}
                    whileHover={{
                      x: isRTL ? -3 : 3,
                      scale: 1.02,
                      boxShadow: "0 4px 15px rgba(59, 130, 246, 0.15)"
                    }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {option.icon && (
                      <i className={`${option.icon} ${colorCoded ? getStatusColor(option.value) : 'text-gray-400'}`} />
                    )}
                    {colorCoded && getStatusIcon(option.value) && (
                      <i className={`${getStatusIcon(option.value)} ${getStatusColor(option.value)}`} />
                    )}
                    <span className={colorCoded ? getStatusColor(option.value) : ''}>{option.label}</span>
                    {value === option.value && (
                      <motion.i
                        className="fas fa-check text-primary-500 ml-auto"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", duration: 0.3 }}
                      />
                    )}
                  </motion.button>
                ))
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default FancyDropdown
