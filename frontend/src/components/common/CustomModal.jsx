import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useLanguage } from '../../context/LanguageContext'

const CustomModal = ({ 
  isOpen, 
  onClose, 
  title, 
  message, 
  type = 'confirm', // 'confirm', 'alert', 'success', 'error'
  onConfirm,
  confirmText,
  cancelText,
  showCancel = true
}) => {
  const { t, isRTL } = useLanguage()

  const getIcon = () => {
    switch (type) {
      case 'success':
        return 'fas fa-check-circle text-green-500'
      case 'error':
        return 'fas fa-exclamation-triangle text-red-500'
      case 'confirm':
        return 'fas fa-question-circle text-blue-500'
      default:
        return 'fas fa-info-circle text-blue-500'
    }
  }

  const getButtonColors = () => {
    switch (type) {
      case 'success':
        return 'bg-green-500 hover:bg-green-600'
      case 'error':
        return 'bg-red-500 hover:bg-red-600'
      default:
        return 'bg-primary-500 hover:bg-primary-600'
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
            onClick={onClose}
          />

          {/* Modal */}
          <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ type: "spring", duration: 0.5 }}
              className={`bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 dark:border-gray-700/50 max-w-md w-full mx-4 ${
                isRTL ? 'text-right' : 'text-left'
              }`}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="p-6 pb-4">
                <div className={`flex items-center space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>
                  <motion.i
                    className={`${getIcon()} text-2xl`}
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring" }}
                  />
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                    {title}
                  </h3>
                </div>
              </div>

              {/* Content */}
              <div className="px-6 pb-6">
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                  {message}
                </p>
              </div>

              {/* Actions */}
              <div className={`flex space-x-3 p-6 pt-0 ${isRTL ? 'space-x-reverse' : ''}`}>
                {showCancel && (
                  <motion.button
                    onClick={onClose}
                    className="flex-1 px-4 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl font-medium transition-all duration-200 hover:bg-gray-200 dark:hover:bg-gray-600 hover:scale-105"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {cancelText || t('cancel')}
                  </motion.button>
                )}
                
                <motion.button
                  onClick={() => {
                    if (onConfirm) onConfirm()
                    onClose()
                  }}
                  className={`flex-1 px-4 py-3 ${getButtonColors()} text-white rounded-xl font-medium transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl`}
                  whileHover={{ scale: 1.02, boxShadow: "0 10px 25px rgba(0,0,0,0.2)" }}
                  whileTap={{ scale: 0.98 }}
                >
                  <motion.span
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.1 }}
                  >
                    {confirmText || (type === 'alert' ? 'OK' : t('confirm'))}
                  </motion.span>
                </motion.button>
              </div>
            </motion.div>
          </div>
        </>
      )}
    </AnimatePresence>
  )
}

export default CustomModal
