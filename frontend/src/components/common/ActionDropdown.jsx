import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { createPortal } from 'react-dom'

const ActionDropdown = ({ document: documentData, onSync, onView, onDownloadPDF, onDownloadXML, isSyncing }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 })
  const dropdownRef = useRef(null)
  const buttonRef = useRef(null)

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target) &&
          buttonRef.current && !buttonRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }

    window.document.addEventListener('mousedown', handleClickOutside)
    return () => window.document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const calculatePosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect()
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

      setDropdownPosition({
        top: rect.bottom + scrollTop + 4,
        left: rect.right + scrollLeft - 192 // 192px is dropdown width
      })
    }
  }

  const handleToggle = () => {
    if (!isOpen) {
      calculatePosition()
    }
    setIsOpen(!isOpen)
  }

  const actions = [
    {
      id: 'view',
      label: 'View Details',
      icon: 'fas fa-eye',
      onClick: () => onView(documentData.id),
      color: 'text-blue-600 hover:bg-blue-50'
    },
    ...(documentData.syncStatus === 'Not Synced' ? [{
      id: 'sync',
      label: 'Sync Now',
      icon: 'fas fa-sync',
      onClick: () => onSync(documentData.id),
      color: 'text-green-600 hover:bg-green-50',
      disabled: isSyncing
    }] : []),
    {
      id: 'pdf',
      label: 'Download PDF',
      icon: 'fas fa-file-pdf',
      onClick: () => onDownloadPDF(documentData.id),
      color: 'text-red-600 hover:bg-red-50'
    },
    {
      id: 'xml',
      label: 'Download XML',
      icon: 'fas fa-file-code',
      onClick: () => onDownloadXML(documentData.id),
      color: 'text-purple-600 hover:bg-purple-50'
    }
  ]

  const handleActionClick = (action) => {
    if (!action.disabled) {
      action.onClick()
      setIsOpen(false)
    }
  }

  return (
    <div className="relative inline-block">
      {/* Trigger Button */}
      <motion.button
        ref={buttonRef}
        onClick={handleToggle}
        className="flex items-center justify-center w-8 h-8 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-all duration-200"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <i className="fas fa-ellipsis-v text-sm" />
      </motion.button>

      {/* Dropdown Menu - Using Portal */}
      {isOpen && createPortal(
        <AnimatePresence>
          <motion.div
            ref={dropdownRef}
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.15, ease: "easeOut" }}
            className="w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-2xl overflow-hidden"
            style={{
              position: 'fixed',
              top: dropdownPosition.top,
              left: dropdownPosition.left,
              zIndex: 9999,
              minWidth: '192px',
              maxHeight: '200px',
              overflowY: 'auto'
            }}
          >
            <ul className="py-2">
              {actions.map((action, index) => (
                <motion.li
                  key={action.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <button
                    onClick={() => handleActionClick(action)}
                    disabled={action.disabled}
                    className={`w-full flex items-center space-x-3 px-4 py-3 text-left transition-all duration-200 ${
                      action.disabled
                        ? 'opacity-50 cursor-not-allowed'
                        : `${action.color} hover:scale-[1.02]`
                    }`}
                  >
                    <div className="flex items-center justify-center w-5 h-5">
                      {action.id === 'sync' && isSyncing ? (
                        <div className="w-4 h-4 border-2 border-green-600 border-t-transparent rounded-full animate-spin" />
                      ) : (
                        <i className={`${action.icon} text-sm`} />
                      )}
                    </div>
                    <span className="text-sm font-medium">{action.label}</span>
                  </button>
                </motion.li>
              ))}
            </ul>
          </motion.div>
        </AnimatePresence>,
        document.body
      )}
    </div>
  )
}

export default ActionDropdown
