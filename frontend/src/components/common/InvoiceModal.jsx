import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import SaudiRiyalIcon from './SaudiRiyalIcon'

const InvoiceModal = ({ isOpen, onClose, document }) => {
  if (!isOpen || !document) return null

  // Dummy invoice data
  const invoiceData = {
    invoiceNumber: `INV-${document.id}`,
    issueDate: new Date().toLocaleDateString(),
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toLocaleDateString(),
    seller: {
      name: 'ZATCA Compliant Company',
      vatNumber: '300012345600003',
      address: 'King Fahd Road, Riyadh 12345, Saudi Arabia',
      phone: '+966 11 123 4567',
      email: '<EMAIL>'
    },
    buyer: {
      name: 'Customer Company Ltd',
      vatNumber: '300098765400001',
      address: 'Prince Sultan Road, Jeddah 21455, Saudi Arabia',
      phone: '+966 12 987 6543',
      email: '<EMAIL>'
    },
    items: [
      { description: 'Professional Services', quantity: 1, unitPrice: 5000, vatRate: 15 },
      { description: 'Consultation Hours', quantity: 10, unitPrice: 500, vatRate: 15 },
      { description: 'Software License', quantity: 1, unitPrice: 2000, vatRate: 15 }
    ],
    qrCode: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
  }

  const subtotal = invoiceData.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)
  const vatAmount = invoiceData.items.reduce((sum, item) => sum + (item.quantity * item.unitPrice * item.vatRate / 100), 0)
  const total = subtotal + vatAmount

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          transition={{ type: "spring", duration: 0.5 }}
          className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              Invoice Details
            </h2>
            <motion.button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <i className="fas fa-times text-xl" />
            </motion.button>
          </div>

          {/* Invoice Content */}
          <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
            <div className="bg-white dark:bg-gray-900 p-8 rounded-xl border border-gray-200 dark:border-gray-700">
              {/* Invoice Header */}
              <div className="flex justify-between items-start mb-8">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">INVOICE</h1>
                  <p className="text-gray-600 dark:text-gray-400">#{invoiceData.invoiceNumber}</p>
                </div>
                <div className="text-right">
                  <div className="w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-4">
                    <img src={invoiceData.qrCode} alt="QR Code" className="w-20 h-20" />
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">QR Code</p>
                </div>
              </div>

              {/* Company Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">From:</h3>
                  <div className="text-gray-600 dark:text-gray-400">
                    <p className="font-medium text-gray-900 dark:text-white">{invoiceData.seller.name}</p>
                    <p>VAT: {invoiceData.seller.vatNumber}</p>
                    <p>{invoiceData.seller.address}</p>
                    <p>{invoiceData.seller.phone}</p>
                    <p>{invoiceData.seller.email}</p>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">To:</h3>
                  <div className="text-gray-600 dark:text-gray-400">
                    <p className="font-medium text-gray-900 dark:text-white">{invoiceData.buyer.name}</p>
                    <p>VAT: {invoiceData.buyer.vatNumber}</p>
                    <p>{invoiceData.buyer.address}</p>
                    <p>{invoiceData.buyer.phone}</p>
                    <p>{invoiceData.buyer.email}</p>
                  </div>
                </div>
              </div>

              {/* Invoice Details */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Issue Date</p>
                  <p className="font-medium text-gray-900 dark:text-white">{invoiceData.issueDate}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Due Date</p>
                  <p className="font-medium text-gray-900 dark:text-white">{invoiceData.dueDate}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Status</p>
                  <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
                    document.status === 'cleared' ? 'bg-green-100 text-green-800' :
                    document.status === 'error' ? 'bg-red-100 text-red-800' :
                    document.status === 'reported' ? 'bg-blue-100 text-blue-800' :
                    'bg-yellow-100 text-yellow-800'
                  }`}>
                    {document.status.charAt(0).toUpperCase() + document.status.slice(1).replace('-', ' ')}
                  </span>
                </div>
              </div>

              {/* Items Table */}
              <div className="mb-8">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Items</h3>
                <div className="overflow-x-auto">
                  <table className="w-full border border-gray-200 dark:border-gray-700 rounded-lg">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Description</th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Qty</th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Unit Price</th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">VAT %</th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase">Total</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                      {invoiceData.items.map((item, index) => (
                        <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                          <td className="px-4 py-3 text-gray-900 dark:text-white">{item.description}</td>
                          <td className="px-4 py-3 text-center text-gray-900 dark:text-white">{item.quantity}</td>
                          <td className="px-4 py-3 text-right text-gray-900 dark:text-white">
                            <div className="flex items-center justify-end space-x-1">
                              <SaudiRiyalIcon size="sm" />
                              <span>{item.unitPrice.toLocaleString()}</span>
                            </div>
                          </td>
                          <td className="px-4 py-3 text-center text-gray-900 dark:text-white">{item.vatRate}%</td>
                          <td className="px-4 py-3 text-right text-gray-900 dark:text-white">
                            <div className="flex items-center justify-end space-x-1">
                              <SaudiRiyalIcon size="sm" />
                              <span>{(item.quantity * item.unitPrice).toLocaleString()}</span>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Totals */}
              <div className="flex justify-end">
                <div className="w-64">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">Subtotal:</span>
                      <div className="flex items-center space-x-1">
                        <SaudiRiyalIcon size="sm" />
                        <span className="text-gray-900 dark:text-white">{subtotal.toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-400">VAT (15%):</span>
                      <div className="flex items-center space-x-1">
                        <SaudiRiyalIcon size="sm" />
                        <span className="text-gray-900 dark:text-white">{vatAmount.toLocaleString()}</span>
                      </div>
                    </div>
                    <div className="border-t border-gray-200 dark:border-gray-700 pt-2">
                      <div className="flex justify-between">
                        <span className="text-lg font-semibold text-gray-900 dark:text-white">Total:</span>
                        <div className="flex items-center space-x-1">
                          <SaudiRiyalIcon size="lg" />
                          <span className="text-lg font-bold text-gray-900 dark:text-white">{total.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
                <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
                  This is a ZATCA compliant e-invoice generated automatically by the system.
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <motion.button
              onClick={() => window.print()}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-all duration-200"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <i className="fas fa-print mr-2" />
              Print
            </motion.button>
            <motion.button
              onClick={onClose}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-all duration-200"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              Close
            </motion.button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  )
}

export default InvoiceModal
