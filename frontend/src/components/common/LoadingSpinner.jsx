import React from 'react'
import { motion } from 'framer-motion'

const LoadingSpinner = ({
  size = 'md',
  color = 'primary',
  text = '',
  className = '',
  type = 'gradient' // 'gradient', 'dots', 'pulse'
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }

  const colorClasses = {
    primary: 'border-primary-500',
    white: 'border-white',
    gray: 'border-gray-500',
    green: 'border-green-500',
    red: 'border-red-500',
    blue: 'border-blue-500'
  }

  if (type === 'gradient') {
    return (
      <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
        <motion.div
          className={`${sizeClasses[size]} rounded-full bg-gradient-to-r from-primary-400 via-blue-500 to-purple-600 p-1`}
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <div className="w-full h-full bg-white dark:bg-gray-900 rounded-full flex items-center justify-center">
            <motion.div
              className="w-2 h-2 bg-gradient-to-r from-primary-500 to-blue-600 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 0.8, repeat: Infinity }}
            />
          </div>
        </motion.div>

        {text && (
          <motion.p
            className="text-sm text-gray-900 dark:text-white font-medium"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            {text}
          </motion.p>
        )}
      </div>
    )
  }

  if (type === 'dots') {
    return (
      <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
        <div className="flex space-x-1">
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              className={`w-2 h-2 bg-primary-500 rounded-full`}
              animate={{ y: [0, -8, 0] }}
              transition={{
                duration: 0.6,
                repeat: Infinity,
                delay: index * 0.1
              }}
            />
          ))}
        </div>

        {text && (
          <motion.p
            className="text-sm text-gray-900 dark:text-white font-medium"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            {text}
          </motion.p>
        )}
      </div>
    )
  }

  if (type === 'pulse') {
    return (
      <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
        <motion.div
          className={`${sizeClasses[size]} bg-primary-500 rounded-full`}
          animate={{ scale: [1, 1.2, 1], opacity: [1, 0.7, 1] }}
          transition={{ duration: 1, repeat: Infinity }}
        />

        {text && (
          <motion.p
            className="text-sm text-gray-900 dark:text-white font-medium"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            {text}
          </motion.p>
        )}
      </div>
    )
  }

  // Default spinner
  return (
    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
      <motion.div
        className={`${sizeClasses[size]} border-2 ${colorClasses[color]} border-t-transparent rounded-full`}
        animate={{ rotate: 360 }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      />

      {text && (
        <motion.p
          className="text-sm text-gray-900 dark:text-white font-medium"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {text}
        </motion.p>
      )}
    </div>
  )
}

export default LoadingSpinner
