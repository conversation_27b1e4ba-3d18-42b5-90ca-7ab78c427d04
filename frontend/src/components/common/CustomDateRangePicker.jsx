import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useLanguage } from '../../context/LanguageContext'
import toast from 'react-hot-toast'

const CustomDateRangePicker = ({ value, onChange, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [selectedRange, setSelectedRange] = useState(value || 'today')
  const [customStartDate, setCustomStartDate] = useState('')
  const [customEndDate, setCustomEndDate] = useState('')
  const [showCustomPicker, setShowCustomPicker] = useState(false)
  const { t, isRTL } = useLanguage()
  const dropdownRef = useRef(null)

  const dateRangeOptions = [
    { value: 'today', label: 'Today', icon: 'fas fa-calendar-day' },
    { value: 'yesterday', label: 'Yesterday', icon: 'fas fa-calendar-minus' },
    { value: 'this-week', label: 'This Week', icon: 'fas fa-calendar-week' },
    { value: 'last-week', label: 'Last Week', icon: 'fas fa-calendar-alt' },
    { value: 'this-month', label: 'This Month', icon: 'fas fa-calendar' },
    { value: 'last-month', label: 'Last Month', icon: 'fas fa-calendar-times' },
    { value: 'this-year', label: 'This Year', icon: 'fas fa-calendar-check' },
    { value: 'custom', label: 'Custom Range', icon: 'fas fa-calendar-plus' }
  ]

  const selectedOption = dateRangeOptions.find(option => option.value === selectedRange)

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false)
        setShowCustomPicker(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleRangeSelect = (range) => {
    if (range === 'custom') {
      setShowCustomPicker(true)
      return
    }
    
    setSelectedRange(range)
    setIsOpen(false)
    setShowCustomPicker(false)
    onChange(range)
    
    toast.success(t('dataRefreshed'), {
      icon: '📅',
      duration: 2000,
    })
  }

  const handleCustomRangeApply = () => {
    if (!customStartDate || !customEndDate) {
      toast.error('Please select both start and end dates', { icon: '⚠️' })
      return
    }

    if (new Date(customStartDate) > new Date(customEndDate)) {
      toast.error('Start date must be before end date', { icon: '❌' })
      return
    }

    const customRange = `${customStartDate} to ${customEndDate}`
    setSelectedRange('custom')
    setIsOpen(false)
    setShowCustomPicker(false)
    onChange({ type: 'custom', startDate: customStartDate, endDate: customEndDate })
    
    toast.success(t('dataRefreshed'), {
      icon: '📅',
      duration: 2000,
    })
  }

  const formatDisplayValue = () => {
    if (selectedRange === 'custom' && customStartDate && customEndDate) {
      return `${customStartDate} - ${customEndDate}`
    }
    return selectedOption?.label || 'Select Range'
  }

  return (
    <div ref={dropdownRef} className={`relative ${className}`}>
      {/* Trigger Button */}
      <motion.button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={`w-full flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl transition-all duration-200 hover:border-primary-400 dark:hover:border-primary-500 focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 ${
          isOpen ? 'ring-2 ring-primary-500/20 border-primary-500' : ''
        } ${isRTL ? 'text-right' : 'text-left'}`}
        whileHover={{ scale: 1.01 }}
        whileTap={{ scale: 0.99 }}
      >
        <div className={`flex items-center space-x-3 ${isRTL ? 'space-x-reverse' : ''}`}>
          <i className={`${selectedOption?.icon || 'fas fa-calendar'} text-gray-400`} />
          <span className="text-gray-900 dark:text-white">
            {formatDisplayValue()}
          </span>
        </div>
        
        <motion.i
          className="fas fa-chevron-down text-gray-400"
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
        />
      </motion.button>

      {/* Dropdown Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className={`absolute ${isRTL ? 'right-0' : 'left-0'} mt-2 w-full bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-xl shadow-2xl z-50 overflow-hidden`}
          >
            {/* Date Range Options */}
            <div className="max-h-64 overflow-y-auto">
              {dateRangeOptions.map((option, index) => (
                <motion.button
                  key={option.value}
                  type="button"
                  onClick={() => handleRangeSelect(option.value)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-all duration-200 ${
                    selectedRange === option.value ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' : 'text-gray-700 dark:text-gray-300'
                  } ${isRTL ? 'space-x-reverse text-right' : ''}`}
                  initial={{ opacity: 0, x: isRTL ? 20 : -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  whileHover={{ x: isRTL ? -5 : 5, backgroundColor: 'rgba(59, 130, 246, 0.1)' }}
                >
                  <i className={`${option.icon} text-gray-400`} />
                  <span>{option.label}</span>
                  {selectedRange === option.value && (
                    <motion.i
                      className="fas fa-check text-primary-500 ml-auto"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: "spring", duration: 0.3 }}
                    />
                  )}
                </motion.button>
              ))}
            </div>

            {/* Custom Date Range Picker */}
            <AnimatePresence>
              {showCustomPicker && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="border-t border-gray-200 dark:border-gray-700 p-4 bg-gray-50 dark:bg-gray-700/50"
                >
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                          Start Date
                        </label>
                        <input
                          type="date"
                          value={customStartDate}
                          onChange={(e) => setCustomStartDate(e.target.value)}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200"
                        />
                      </div>
                      <div>
                        <label className="block text-xs font-medium text-gray-600 dark:text-gray-400 mb-1">
                          End Date
                        </label>
                        <input
                          type="date"
                          value={customEndDate}
                          onChange={(e) => setCustomEndDate(e.target.value)}
                          className="w-full px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg text-sm focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200"
                        />
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <motion.button
                        type="button"
                        onClick={() => setShowCustomPicker(false)}
                        className="flex-1 px-3 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-gray-300 dark:hover:bg-gray-500"
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        Cancel
                      </motion.button>
                      <motion.button
                        type="button"
                        onClick={handleCustomRangeApply}
                        className="flex-1 px-3 py-2 bg-primary-500 text-white rounded-lg text-sm font-medium transition-all duration-200 hover:bg-primary-600 shadow-lg hover:shadow-xl"
                        whileHover={{ scale: 1.02, boxShadow: "0 10px 25px rgba(0,0,0,0.2)" }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <motion.span
                          className="flex items-center justify-center space-x-1"
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                        >
                          <i className="fas fa-check text-xs" />
                          <span>Apply</span>
                        </motion.span>
                      </motion.button>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default CustomDateRangePicker
