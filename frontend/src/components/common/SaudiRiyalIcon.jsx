import React from 'react'
import { motion } from 'framer-motion'

const SaudiRiyalIcon = ({ className = '', animate = true, size = 'md' }) => {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl'
  }

  const IconComponent = animate ? motion.span : 'span'
  const animationProps = animate ? {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: 0.3, ease: "easeOut" }
  } : {}

  return (
    <IconComponent
      className={`icon-saudi_riyal ${sizeClasses[size]} ${className}`}
      {...animationProps}
    />
  )
}

export default SaudiRiyalIcon
