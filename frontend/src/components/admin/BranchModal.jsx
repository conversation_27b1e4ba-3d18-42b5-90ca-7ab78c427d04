import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useLanguage } from '../../context/LanguageContext'
import LoadingSpinner from '../common/LoadingSpinner'


const BranchModal = ({
  isOpen,
  onClose,
  onSubmit,
  branchForm,
  setBranchForm,
  isLoading,
  isEdit = false,
  selectedCompany
}) => {
  const { t, isRTL } = useLanguage()
  const [currentStep, setCurrentStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)

  const environmentOptions = [
    { value: 'sandbox', label: t('sandbox'), icon: 'fas fa-flask' },
    { value: 'simulation', label: t('simulation'), icon: 'fas fa-play-circle' },
    { value: 'production', label: t('production'), icon: 'fas fa-server' }
  ]

  const steps = [
    {
      id: 1,
      title: 'Basic Information',
      icon: 'fas fa-info-circle',
      description: 'Enter basic branch details'
    },
    {
      id: 2,
      title: 'Configuration',
      icon: 'fas fa-cogs',
      description: 'Configure environment and settings'
    },
    {
      id: 3,
      title: 'Review & Submit',
      icon: 'fas fa-check-circle',
      description: 'Review and submit branch information'
    }
  ]

  const canProceedToNext = () => {
    switch (currentStep) {
      case 1:
        return branchForm.sellerName && branchForm.vatNumber
      case 2:
        return branchForm.environment && branchForm.documentType
      case 3:
        return true
      default:
        return false
    }
  }

  const handleNext = () => {
    if (canProceedToNext() && currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleFinish = async () => {
    setIsSubmitting(true)
    try {
      await onSubmit()
      // Don't reset step here - let the parent component handle closing
      // The modal will be closed by the parent component
    } catch (error) {
      console.error('Error submitting branch:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const resetWizard = () => {
    setCurrentStep(1)
    setIsSubmitting(false)
  }

  const handleInputChange = (field, value) => {
    setBranchForm(prev => ({ ...prev, [field]: value }))
  }

  // Reset wizard when modal opens - MUST be before early return
  useEffect(() => {
    if (isOpen) {
      setCurrentStep(1)
      setIsSubmitting(false)
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          transition={{ type: "spring", duration: 0.5 }}
          className={`bg-gradient-to-br from-blue-50/95 via-white/95 to-blue-50/95 dark:from-blue-900/20 dark:via-gray-800/95 dark:to-blue-900/20 backdrop-blur-xl rounded-2xl shadow-2xl border border-blue-200/50 dark:border-blue-700/50 w-full max-w-4xl max-h-[90vh] overflow-hidden glow-effect ${
            isRTL ? 'text-right' : 'text-left'
          }`}
          onClick={(e) => e.stopPropagation()}
          style={{
            boxShadow: '0 0 50px rgba(24, 119, 242, 0.2), 0 25px 50px rgba(0, 0, 0, 0.15)'
          }}
        >
          {/* Header */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {isEdit ? 'Edit Branch' : 'Create New Branch'}
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  {selectedCompany && `for ${selectedCompany.name}`}
                </p>
              </div>
              <motion.button
                onClick={onClose}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors duration-200"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <i className="fas fa-times text-gray-500 dark:text-gray-400" />
              </motion.button>
            </div>

            {/* Progress Steps */}
            <div className="mt-6">
              <div className="flex items-center justify-center space-x-4">
                {steps.map((step, index) => (
                  <div key={step.id} className="flex items-center">
                    <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-300 ${
                      currentStep === step.id
                        ? 'bg-blue-500 border-blue-500 text-white'
                        : currentStep > step.id
                          ? 'bg-green-500 border-green-500 text-white'
                          : 'bg-gray-100 border-gray-300 text-gray-500'
                    }`}>
                      {currentStep > step.id ? (
                        <i className="fas fa-check text-sm" />
                      ) : (
                        <span className="text-sm font-medium">{step.id}</span>
                      )}
                    </div>
                    <div className="ml-3">
                      <p className={`text-sm font-medium ${
                        currentStep === step.id ? 'text-blue-600' : 'text-gray-500'
                      }`}>
                        {step.title}
                      </p>
                      <p className="text-xs text-gray-400">{step.description}</p>
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`w-12 h-0.5 mx-4 ${
                        currentStep > step.id ? 'bg-green-500' : 'bg-gray-300'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto max-h-[60vh]">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: isRTL ? -20 : 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: isRTL ? 20 : -20 }}
                transition={{ duration: 0.3 }}
                className="space-y-6"
              >
                {/* Step 1: Basic Information */}
                {currentStep === 1 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Seller Name *
                      </label>
                      <motion.input
                        type="text"
                        value={branchForm.sellerName}
                        onChange={(e) => handleInputChange('sellerName', e.target.value)}
                        className={`w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200 ${
                          isRTL ? 'text-right' : 'text-left'
                        }`}
                        whileFocus={{ scale: 1.01 }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        VAT Number *
                      </label>
                      <motion.input
                        type="text"
                        value={branchForm.vatNumber}
                        onChange={(e) => handleInputChange('vatNumber', e.target.value)}
                        className={`w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200 ${
                          isRTL ? 'text-right' : 'text-left'
                        }`}
                        whileFocus={{ scale: 1.01 }}
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Organisation Name *
                      </label>
                      <motion.input
                        type="text"
                        value={branchForm.organisationName}
                        onChange={(e) => handleInputChange('organisationName', e.target.value)}
                        className={`w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200 ${
                          isRTL ? 'text-right' : 'text-left'
                        }`}
                        whileFocus={{ scale: 1.01 }}
                      />
                    </div>
                  </div>
                )}

                {/* Step 2: Configuration */}
                {currentStep === 2 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Serial Number
                      </label>
                      <motion.input
                        type="text"
                        value={branchForm.serialNumber}
                        onChange={(e) => handleInputChange('serialNumber', e.target.value)}
                        className={`w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200 ${
                          isRTL ? 'text-right' : 'text-left'
                        }`}
                        whileFocus={{ scale: 1.01 }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Organisation Unit
                      </label>
                      <motion.input
                        type="text"
                        value={branchForm.organisationUnit}
                        onChange={(e) => handleInputChange('organisationUnit', e.target.value)}
                        className={`w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200 ${
                          isRTL ? 'text-right' : 'text-left'
                        }`}
                        whileFocus={{ scale: 1.01 }}
                      />
                    </div>

                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Registered Address
                      </label>
                      <motion.textarea
                        value={branchForm.registeredAddress}
                        onChange={(e) => handleInputChange('registeredAddress', e.target.value)}
                        rows={3}
                        className={`w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200 resize-none ${
                          isRTL ? 'text-right' : 'text-left'
                        }`}
                        whileFocus={{ scale: 1.01 }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Business Category
                      </label>
                      <motion.input
                        type="text"
                        value={branchForm.businessCategory}
                        onChange={(e) => handleInputChange('businessCategory', e.target.value)}
                        className={`w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200 ${
                          isRTL ? 'text-right' : 'text-left'
                        }`}
                        whileFocus={{ scale: 1.01 }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Common Name
                      </label>
                      <motion.input
                        type="text"
                        value={branchForm.commonName}
                        onChange={(e) => handleInputChange('commonName', e.target.value)}
                        className={`w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200 ${
                          isRTL ? 'text-right' : 'text-left'
                        }`}
                        whileFocus={{ scale: 1.01 }}
                      />
                    </div>
                  </div>
                )}

                {/* Step 3: Review & Submit */}
                {currentStep === 3 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('environment')}
                      </label>
                      <select
                        value={branchForm.environment}
                        onChange={(e) => handleInputChange('environment', e.target.value)}
                        className="input-field"
                      >
                        <option value="">Select environment</option>
                        <option value="sandbox">Sandbox</option>
                        <option value="simulation">Simulation</option>
                        <option value="production">Production</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('crn')}
                      </label>
                      <motion.input
                        type="text"
                        value={branchForm.crn}
                        onChange={(e) => handleInputChange('crn', e.target.value.replace(/\D/g, ''))}
                        placeholder="Enter CRN (numbers only)"
                        className={`w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200 ${
                          isRTL ? 'text-right' : 'text-left'
                        }`}
                        whileFocus={{ scale: 1.01 }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('email')}
                      </label>
                      <motion.input
                        type="email"
                        value={branchForm.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        placeholder="Enter email address"
                        className={`w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200 ${
                          isRTL ? 'text-right' : 'text-left'
                        }`}
                        whileFocus={{ scale: 1.01 }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        {t('otp')}
                      </label>
                      <motion.input
                        type="text"
                        value={branchForm.otp}
                        onChange={(e) => handleInputChange('otp', e.target.value.replace(/\D/g, '').slice(0, 6))}
                        placeholder="Enter 6-digit OTP"
                        maxLength={6}
                        className={`w-full px-4 py-3 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500/20 focus:border-primary-500 transition-all duration-200 ${
                          isRTL ? 'text-right' : 'text-left'
                        }`}
                        whileFocus={{ scale: 1.01 }}
                      />
                      {branchForm.otp && branchForm.otp.length === 6 && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          className="mt-2 flex items-center text-green-600 dark:text-green-400 text-sm"
                        >
                          <i className="fas fa-check-circle mr-2" />
                          Valid OTP format
                        </motion.div>
                      )}
                    </div>
                  </div>
                )}
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Wizard Navigation Footer */}
          <div className={`p-6 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className="flex space-x-3">
              {/* Cancel Button */}
              <motion.button
                onClick={() => {
                  onClose()
                  resetWizard()
                }}
                className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-all duration-200 hover:bg-gray-200 dark:hover:bg-gray-600"
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Cancel
              </motion.button>

              {/* Previous Button */}
              {currentStep > 1 && (
                <motion.button
                  onClick={handlePrevious}
                  className="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg font-medium transition-all duration-200 hover:bg-gray-200 dark:hover:bg-gray-600"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                >
                  <i className="fas fa-arrow-left mr-2" />
                  Previous
                </motion.button>
              )}
            </div>

            <div className="flex space-x-3">
              {/* Next Button */}
              {currentStep < steps.length && (
                <motion.button
                  onClick={handleNext}
                  disabled={!canProceedToNext()}
                  className="px-6 py-3 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={canProceedToNext() ? { scale: 1.02 } : {}}
                  whileTap={canProceedToNext() ? { scale: 0.98 } : {}}
                >
                  Next
                  <i className="fas fa-arrow-right ml-2" />
                </motion.button>
              )}

              {/* Finish Button */}
              {currentStep === steps.length && (
                <motion.button
                  onClick={handleFinish}
                  disabled={isSubmitting || !canProceedToNext()}
                  className="px-6 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg font-medium hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                  whileHover={!isSubmitting && canProceedToNext() ? { scale: 1.02 } : {}}
                  whileTap={!isSubmitting && canProceedToNext() ? { scale: 0.98 } : {}}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.2 }}
                >
                  {isSubmitting ? (
                    <LoadingSpinner size="sm" color="white" text="Creating..." />
                  ) : (
                    <>
                      <i className="fas fa-check mr-2" />
                      Finish
                    </>
                  )}
                </motion.button>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  )
}

export default BranchModal
