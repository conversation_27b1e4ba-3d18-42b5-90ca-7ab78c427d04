import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'
import { useApp } from '../../context/AppContext'
import { useLanguage } from '../../context/LanguageContext'
import LoadingSpinner from '../common/LoadingSpinner'
import CustomModal from '../common/CustomModal'
import FancyDropdown from '../common/FancyDropdown'
import BranchModal from './BranchModal'

const Companies = () => {
  const [expandedCompanies, setExpandedCompanies] = useState({})
  const [showCreateCompany, setShowCreateCompany] = useState(false)
  const [showCreateBranch, setShowCreateBranch] = useState(false)
  const [showEditCompany, setShowEditCompany] = useState(false)
  const [showEditBranch, setShowEditBranch] = useState(false)
  const [selectedCompany, setSelectedCompany] = useState(null)
  const [selectedBranch, setSelectedBranch] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(10)
  const [isLoading, setIsLoading] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [deleteTarget, setDeleteTarget] = useState(null)
  const [activeTab, setActiveTab] = useState('basic')

  const { companies, addCompany, addBranch, deleteCompany } = useApp()
  const { t, isRTL } = useLanguage()

  const [companyForm, setCompanyForm] = useState({
    name: ''
  })

  const [branchForm, setBranchForm] = useState({
    sellerName: '',
    vatNumber: '',
    organisationName: '',
    serialNumber: '',
    organisationUnit: '',
    registeredAddress: '',
    businessCategory: '',
    documentType: 'B2B',
    commonName: '',
    environment: 'sandbox',
    crn: '',
    email: '',
    otp: ''
  })

  // Environment options for dropdown
  const environmentOptions = [
    { value: 'sandbox', label: t('sandbox'), icon: 'fas fa-flask' },
    { value: 'simulation', label: t('simulation'), icon: 'fas fa-play-circle' },
    { value: 'production', label: t('production'), icon: 'fas fa-server' }
  ]

  const toggleCompanyExpansion = (companyId) => {
    setExpandedCompanies(prev => ({
      ...prev,
      [companyId]: !prev[companyId]
    }))
  }

  const handleCreateCompany = async () => {
    if (!companyForm.name.trim()) {
      toast.error('Company name is required')
      return
    }

    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      const newCompany = addCompany(companyForm)
      setCompanyForm({ name: '' })
      setShowCreateCompany(false)
      setIsLoading(false)
      toast.success(t('companyCreated'), {
        icon: '🏢',
        duration: 3000,
      })
    }, 1500)
  }

  const handleEditCompany = async () => {
    if (!companyForm.name.trim()) {
      toast.error('Company name is required')
      return
    }

    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      // Update company logic would go here
      setCompanyForm({ name: '' })
      setShowEditCompany(false)
      setSelectedCompany(null)
      setIsLoading(false)
      toast.success('Company updated successfully', {
        icon: '✨',
        duration: 3000,
      })
    }, 1500)
  }

  const handleCreateBranch = async () => {
    // Enhanced validation
    if (!branchForm.sellerName || !branchForm.vatNumber || !branchForm.organisationName) {
      toast.error('Please fill in all required fields')
      return
    }

    if (branchForm.email && !/\S+@\S+\.\S+/.test(branchForm.email)) {
      toast.error('Please enter a valid email address')
      return
    }

    if (branchForm.crn && !/^\d+$/.test(branchForm.crn)) {
      toast.error('CRN must contain only numbers')
      return
    }

    if (branchForm.otp && (branchForm.otp.length !== 6 || !/^\d{6}$/.test(branchForm.otp))) {
      toast.error('OTP must be exactly 6 digits')
      return
    }

    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      const newBranch = addBranch(selectedCompany.id, branchForm)
      setBranchForm({
        sellerName: '',
        vatNumber: '',
        organisationName: '',
        serialNumber: '',
        organisationUnit: '',
        registeredAddress: '',
        businessCategory: '',
        documentType: 'B2B',
        commonName: '',
        environment: 'sandbox',
        crn: '',
        email: '',
        otp: ''
      })
      setShowCreateBranch(false)
      setSelectedCompany(null)
      setIsLoading(false)
      toast.success(t('branchCreated'), {
        icon: '🌿',
        duration: 3000,
      })
    }, 2000)
  }

  const handleEditBranch = async () => {
    // Same validation as create
    if (!branchForm.sellerName || !branchForm.vatNumber || !branchForm.organisationName) {
      toast.error('Please fill in all required fields')
      return
    }

    if (branchForm.email && !/\S+@\S+\.\S+/.test(branchForm.email)) {
      toast.error('Please enter a valid email address')
      return
    }

    if (branchForm.crn && !/^\d+$/.test(branchForm.crn)) {
      toast.error('CRN must contain only numbers')
      return
    }

    if (branchForm.otp && (branchForm.otp.length !== 6 || !/^\d{6}$/.test(branchForm.otp))) {
      toast.error('OTP must be exactly 6 digits')
      return
    }

    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      // Update branch logic would go here
      setBranchForm({
        sellerName: '',
        vatNumber: '',
        organisationName: '',
        serialNumber: '',
        organisationUnit: '',
        registeredAddress: '',
        businessCategory: '',
        documentType: 'B2B',
        commonName: '',
        environment: 'sandbox',
        crn: '',
        email: '',
        otp: ''
      })
      setShowEditBranch(false)
      setSelectedBranch(null)
      setIsLoading(false)
      toast.success('Branch updated successfully', {
        icon: '✨',
        duration: 3000,
      })
    }, 2000)
  }

  const handleDeleteCompany = (company) => {
    setDeleteTarget({ type: 'company', data: company })
    setShowDeleteModal(true)
  }

  const handleDeleteBranch = (company, branch) => {
    setDeleteTarget({ type: 'branch', data: { company, branch } })
    setShowDeleteModal(true)
  }

  const confirmDelete = () => {
    if (deleteTarget.type === 'company') {
      deleteCompany(deleteTarget.data.id)
      toast.success(`Company "${deleteTarget.data.name}" deleted successfully`, {
        icon: '🗑️',
        duration: 3000,
      })
    } else if (deleteTarget.type === 'branch') {
      // Delete branch logic would go here
      toast.success(`Branch "${deleteTarget.data.branch.sellerName}" deleted successfully`, {
        icon: '🗑️',
        duration: 3000,
      })
    }
    setShowDeleteModal(false)
    setDeleteTarget(null)
  }

  const openEditCompany = (company) => {
    setSelectedCompany(company)
    setCompanyForm({ name: company.name })
    setShowEditCompany(true)
  }

  const openEditBranch = (company, branch) => {
    setSelectedCompany(company)
    setSelectedBranch(branch)
    setBranchForm({
      sellerName: branch.sellerName || '',
      vatNumber: branch.vatNumber || '',
      organisationName: branch.organisationName || '',
      serialNumber: branch.serialNumber || '',
      organisationUnit: branch.organisationUnit || '',
      registeredAddress: branch.registeredAddress || '',
      businessCategory: branch.businessCategory || '',
      documentType: branch.documentType || 'B2B',
      commonName: branch.commonName || '',
      environment: branch.environment || 'sandbox',
      crn: branch.crn || '',
      email: branch.email || '',
      otp: branch.otp || ''
    })
    setShowEditBranch(true)
  }

  const filteredCompanies = companies.filter(company =>
    company.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const totalPages = Math.ceil(filteredCompanies.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedCompanies = filteredCompanies.slice(startIndex, startIndex + itemsPerPage)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Companies</h1>
          <p className="text-gray-600 dark:text-gray-400">Manage your companies and branches</p>
        </div>
        <motion.button
          onClick={() => setShowCreateCompany(true)}
          className="btn-primary"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <i className="fas fa-plus mr-2"></i>
          Create Company
        </motion.button>
      </div>

      {/* Search and filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search companies..."
              className="input-field pl-10"
            />
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>
      </div>

      {/* Companies tree */}
      <div className="card">
        <div className="p-6">
          <div className="space-y-4">
            {paginatedCompanies.length > 0 ? (
              paginatedCompanies.map((company) => (
                <motion.div
                  key={company.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden"
                >
                  {/* Company header */}
                  <div className="p-4 bg-gray-50 dark:bg-gray-800/50 flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <motion.button
                        onClick={() => toggleCompanyExpansion(company.id)}
                        className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded"
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <motion.i
                          className={`fas fa-chevron-right text-gray-500 transition-transform duration-200 ${
                            expandedCompanies[company.id] ? 'rotate-90' : ''
                          }`}
                          animate={{ rotate: expandedCompanies[company.id] ? 90 : 0 }}
                        />
                      </motion.button>
                      <i className="fas fa-building text-primary-500"></i>
                      <div>
                        <h3 className="font-semibold text-gray-900 dark:text-white">
                          {company.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {company.branches?.length || 0} branches
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <motion.button
                        onClick={() => {
                          setSelectedCompany(company)
                          setShowCreateBranch(true)
                        }}
                        className="px-3 py-1 text-sm bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50 transition-colors duration-200"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <i className="fas fa-plus mr-1"></i>
                        Add Branch
                      </motion.button>
                      <motion.button
                        onClick={() => openEditCompany(company)}
                        className="px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded-lg hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors duration-200"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <i className="fas fa-edit mr-1"></i>
                        Edit
                      </motion.button>
                      <motion.button
                        onClick={() => handleDeleteCompany(company)}
                        className="px-3 py-1 text-sm bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded-lg hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors duration-200"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <i className="fas fa-trash"></i>
                      </motion.button>
                    </div>
                  </div>

                  {/* Branches */}
                  <AnimatePresence>
                    {expandedCompanies[company.id] && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="p-4 space-y-3">
                          {company.branches && company.branches.length > 0 ? (
                            company.branches.map((branch) => (
                              <motion.div
                                key={branch.id}
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                className="ml-6 p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg"
                              >
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-3">
                                    <i className="fas fa-code-branch text-gray-400"></i>
                                    <div>
                                      <h4 className="font-medium text-gray-900 dark:text-white">
                                        {branch.sellerName}
                                      </h4>
                                      <p className="text-sm text-gray-600 dark:text-gray-400">
                                        VAT: {branch.vatNumber} • {branch.documentType}
                                        {branch.environment && ` • ${branch.environment}`}
                                      </p>
                                    </div>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <span className={`badge ${
                                      branch.documentType === 'B2B' ? 'badge-info' :
                                      branch.documentType === 'B2C' ? 'badge-warning' : 'badge-success'
                                    }`}>
                                      {branch.documentType}
                                    </span>
                                    <motion.button
                                      onClick={() => openEditBranch(company, branch)}
                                      className="px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 rounded hover:bg-blue-200 dark:hover:bg-blue-900/50 transition-colors duration-200"
                                      whileHover={{ scale: 1.05 }}
                                      whileTap={{ scale: 0.95 }}
                                    >
                                      <i className="fas fa-edit"></i>
                                    </motion.button>
                                    <motion.button
                                      onClick={() => handleDeleteBranch(company, branch)}
                                      className="px-2 py-1 text-xs bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 rounded hover:bg-red-200 dark:hover:bg-red-900/50 transition-colors duration-200"
                                      whileHover={{ scale: 1.05 }}
                                      whileTap={{ scale: 0.95 }}
                                    >
                                      <i className="fas fa-trash"></i>
                                    </motion.button>
                                  </div>
                                </div>
                              </motion.div>
                            ))
                          ) : (
                            <div className="ml-6 p-4 text-center text-gray-500 dark:text-gray-400">
                              <i className="fas fa-info-circle mb-2"></i>
                              <p>No branches yet. Click "Add Branch" to create one.</p>
                            </div>
                          )}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))
            ) : (
              <div className="text-center py-12">
                <i className="fas fa-building text-4xl text-gray-300 dark:text-gray-600 mb-4"></i>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No companies found
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {searchTerm ? 'Try adjusting your search terms' : 'Get started by creating your first company'}
                </p>
                {!searchTerm && (
                  <motion.button
                    onClick={() => setShowCreateCompany(true)}
                    className="btn-primary"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <i className="fas fa-plus mr-2"></i>
                    Create Company
                  </motion.button>
                )}
              </div>
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredCompanies.length)} of {filteredCompanies.length} companies
              </p>
              <div className="flex items-center space-x-2">
                <motion.button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-800"
                  whileHover={{ scale: currentPage > 1 ? 1.05 : 1 }}
                  whileTap={{ scale: currentPage > 1 ? 0.95 : 1 }}
                >
                  Previous
                </motion.button>
                <span className="px-3 py-1 text-sm bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400 rounded-lg">
                  {currentPage} of {totalPages}
                </span>
                <motion.button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-800"
                  whileHover={{ scale: currentPage < totalPages ? 1.05 : 1 }}
                  whileTap={{ scale: currentPage < totalPages ? 0.95 : 1 }}
                >
                  Next
                </motion.button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Create Company Modal */}
      <AnimatePresence>
        {showCreateCompany && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowCreateCompany(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="bg-gradient-to-br from-blue-50/95 via-white/95 to-blue-50/95 dark:from-blue-900/20 dark:via-gray-800/95 dark:to-blue-900/20 backdrop-blur-xl rounded-2xl shadow-2xl border border-blue-200/50 dark:border-blue-700/50 p-6 w-full max-w-md"
              onClick={(e) => e.stopPropagation()}
              style={{
                boxShadow: '0 0 50px rgba(24, 119, 242, 0.2), 0 25px 50px rgba(0, 0, 0, 0.15)'
              }}
            >
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Create New Company
              </h3>

              <motion.div
                className="space-y-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Company Name *
                  </label>
                  <motion.input
                    type="text"
                    value={companyForm.name}
                    onChange={(e) => setCompanyForm({ name: e.target.value })}
                    placeholder="Enter company name"
                    className="input-field"
                    autoFocus
                    whileFocus={{ scale: 1.02, borderColor: '#1877F2' }}
                    transition={{ duration: 0.2 }}
                  />
                </motion.div>
              </motion.div>

              <motion.div
                className="flex justify-end space-x-3 mt-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <motion.button
                  onClick={() => setShowCreateCompany(false)}
                  className="btn-secondary"
                  whileHover={{ scale: 1.05, boxShadow: "0 4px 15px rgba(0,0,0,0.1)" }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  Cancel
                </motion.button>
                <motion.button
                  onClick={handleCreateCompany}
                  disabled={isLoading}
                  className="btn-primary"
                  whileHover={{ scale: 1.05, boxShadow: "0 4px 20px rgba(24, 119, 242, 0.3)" }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  {isLoading ? (
                    <LoadingSpinner size="sm" color="white" text="Creating..." />
                  ) : (
                    'Create Company'
                  )}
                </motion.button>
              </motion.div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Edit Company Modal */}
      <AnimatePresence>
        {showEditCompany && selectedCompany && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowEditCompany(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="bg-gradient-to-br from-blue-50/95 via-white/95 to-blue-50/95 dark:from-blue-900/20 dark:via-gray-800/95 dark:to-blue-900/20 backdrop-blur-xl rounded-2xl shadow-2xl border border-blue-200/50 dark:border-blue-700/50 p-6 w-full max-w-md"
              onClick={(e) => e.stopPropagation()}
              style={{
                boxShadow: '0 0 50px rgba(24, 119, 242, 0.2), 0 25px 50px rgba(0, 0, 0, 0.15)'
              }}
            >
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                Edit Company
              </h3>

              <motion.div
                className="space-y-4"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Company Name *
                  </label>
                  <motion.input
                    type="text"
                    value={companyForm.name}
                    onChange={(e) => setCompanyForm({ name: e.target.value })}
                    placeholder="Enter company name"
                    className="input-field"
                    autoFocus
                    whileFocus={{ scale: 1.02, borderColor: '#1877F2' }}
                    transition={{ duration: 0.2 }}
                  />
                </motion.div>
              </motion.div>

              <motion.div
                className="flex justify-end space-x-3 mt-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <motion.button
                  onClick={() => setShowEditCompany(false)}
                  className="btn-secondary"
                  whileHover={{ scale: 1.05, boxShadow: "0 4px 15px rgba(0,0,0,0.1)" }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  Cancel
                </motion.button>
                <motion.button
                  onClick={handleEditCompany}
                  disabled={isLoading}
                  className="btn-primary"
                  whileHover={{ scale: 1.05, boxShadow: "0 4px 20px rgba(24, 119, 242, 0.3)" }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6 }}
                >
                  {isLoading ? (
                    <LoadingSpinner size="sm" color="white" text="Updating..." />
                  ) : (
                    'Update Company'
                  )}
                </motion.button>
              </motion.div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Create Branch Modal */}
      <BranchModal
        isOpen={showCreateBranch}
        onClose={() => setShowCreateBranch(false)}
        onSubmit={handleCreateBranch}
        branchForm={branchForm}
        setBranchForm={setBranchForm}
        isLoading={isLoading}
        selectedCompany={selectedCompany}
      />

      {/* Edit Branch Modal */}
      <BranchModal
        isOpen={showEditBranch}
        onClose={() => setShowEditBranch(false)}
        onSubmit={handleEditBranch}
        branchForm={branchForm}
        setBranchForm={setBranchForm}
        isLoading={isLoading}
        isEdit={true}
        selectedCompany={selectedCompany}
      />

      {/* Delete Confirmation Modal */}
      <CustomModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        title="Confirm Deletion"
        message={
          deleteTarget?.type === 'company'
            ? `Are you sure you want to delete "${deleteTarget.data.name}"? This action cannot be undone.`
            : `Are you sure you want to delete branch "${deleteTarget?.data.branch.sellerName}"? This action cannot be undone.`
        }
        type="error"
        onConfirm={confirmDelete}
        confirmText="Delete"
        cancelText={t('cancel')}
      />
    </div>
  )
}

export default Companies
