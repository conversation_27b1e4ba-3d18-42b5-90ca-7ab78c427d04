import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, ArcElement, Title, Tooltip, Legend } from 'chart.js'
import { Line, Bar, Pie } from 'react-chartjs-2'
import toast from 'react-hot-toast'
import { useApp } from '../../context/AppContext'
import { useLanguage } from '../../context/LanguageContext'
import SaudiRiyalIcon from '../common/SaudiRiyalIcon'
import CustomDateRangePicker from '../common/CustomDateRangePicker'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
)

const Dashboard = ({ onNavigate }) => {
  const [dateRange, setDateRange] = useState('today')
  const [isLiveRefresh, setIsLiveRefresh] = useState(false)
  const [flippedTiles, setFlippedTiles] = useState({})
  const { user, currentCompany } = useApp()
  const { t, isRTL } = useLanguage()

  // Enhanced mock data with Saudi Riyal
  const stats = {
    totalUsers: 1245,
    totalCompanies: 87,
    totalDocuments: 12340,
    totalRevenue: 1230500
  }

  const additionalStats = {
    totalUsers: { change: '+3%', period: 'vs last month', trend: 'up' },
    totalCompanies: { change: '+5%', period: 'vs last month', trend: 'up' },
    totalDocuments: { change: '-2%', period: 'vs last week', trend: 'down' },
    totalRevenue: { change: '+4%', period: 'vs last month', trend: 'up' }
  }

  // Saudi Riyal component
  const SaudiRiyalDisplay = ({ amount, className = '' }) => (
    <span className={`flex items-center space-x-1 ${className}`}>
      <SaudiRiyalIcon size="md" />
      <span>{amount.toLocaleString()}</span>
    </span>
  )

  // Chart data
  const lineChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [
      {
        label: 'Document Submissions',
        data: [1200, 1900, 3000, 5000, 2000, 3000],
        borderColor: '#1877F2',
        backgroundColor: 'rgba(24, 119, 242, 0.1)',
        tension: 0.4,
        fill: true
      }
    ]
  }

  const barChartData = {
    labels: ['Tech Solutions', 'Global Trading', 'Saudi Corp', 'Al-Riyadh Co', 'Modern Systems'],
    datasets: [
      {
        label: 'Activity Score',
        data: [85, 92, 78, 88, 95],
        backgroundColor: [
          'rgba(24, 119, 242, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(139, 92, 246, 0.8)'
        ],
        borderRadius: 8
      }
    ]
  }

  const pieChartData = {
    labels: ['Cleared', 'Reported', 'Error', 'Not Submitted'],
    datasets: [
      {
        data: [45, 30, 15, 10],
        backgroundColor: [
          '#10B981',
          '#F59E0B',
          '#EF4444',
          '#6B7280'
        ],
        borderWidth: 0
      }
    ]
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom'
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      },
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        }
      }
    }
  }

  const handleTileFlip = (tileId) => {
    setFlippedTiles(prev => ({
      ...prev,
      [tileId]: !prev[tileId]
    }))
  }

  const handleDateRangeChange = (range) => {
    if (typeof range === 'object' && range.type === 'custom') {
      setDateRange(`${range.startDate} to ${range.endDate}`)
      toast.success(`Date range updated to custom range`, {
        icon: '📅',
        duration: 2000,
      })
    } else {
      setDateRange(range)
      toast.success(`Date range updated to ${range}`, {
        icon: '📅',
        duration: 2000,
      })
    }
  }

  const toggleLiveRefresh = () => {
    setIsLiveRefresh(!isLiveRefresh)
    toast.success(`Live refresh ${!isLiveRefresh ? 'enabled' : 'disabled'}`, {
      icon: !isLiveRefresh ? '🔄' : '⏸️',
    })
  }

  const downloadChart = (chartName) => {
    toast.success(`${chartName} chart downloaded!`, {
      icon: '📊',
    })
  }

  useEffect(() => {
    if (isLiveRefresh) {
      const interval = setInterval(() => {
        // Simulate live data updates
        console.log('Refreshing data...')
      }, 5000)
      return () => clearInterval(interval)
    }
  }, [isLiveRefresh])

  const tiles = [
    {
      id: 'users',
      title: t('totalUsers'),
      value: stats.totalUsers.toLocaleString(),
      icon: 'fas fa-users',
      color: 'bg-blue-500',
      gradient: 'from-blue-500 to-blue-600',
      change: additionalStats.totalUsers.change,
      trend: additionalStats.totalUsers.trend,
      period: additionalStats.totalUsers.period,
      sparklineData: [12, 19, 15, 17, 14, 18, 20, 22, 18, 25, 23, 28],
      navigateTo: 'users'
    },
    {
      id: 'companies',
      title: t('totalCompanies'),
      value: stats.totalCompanies.toLocaleString(),
      icon: 'fas fa-building',
      color: 'bg-green-500',
      gradient: 'from-green-500 to-green-600',
      change: additionalStats.totalCompanies.change,
      trend: additionalStats.totalCompanies.trend,
      period: additionalStats.totalCompanies.period,
      sparklineData: [8, 12, 10, 14, 12, 16, 15, 18, 17, 20, 19, 22],
      navigateTo: 'companies'
    },
    {
      id: 'documents',
      title: t('totalDocuments'),
      value: stats.totalDocuments.toLocaleString(),
      icon: 'fas fa-file-alt',
      color: 'bg-yellow-500',
      gradient: 'from-yellow-500 to-yellow-600',
      change: additionalStats.totalDocuments.change,
      trend: additionalStats.totalDocuments.trend,
      period: additionalStats.totalDocuments.period,
      sparklineData: [120, 135, 140, 125, 130, 128, 122, 118, 115, 120, 125, 123],
      navigateTo: 'documents'
    },
    {
      id: 'revenue',
      title: t('totalRevenue'),
      value: stats.totalRevenue,
      icon: 'fas fa-coins',
      color: 'bg-purple-500',
      gradient: 'from-purple-500 to-purple-600',
      change: additionalStats.totalRevenue.change,
      trend: additionalStats.totalRevenue.trend,
      period: additionalStats.totalRevenue.period,
      sparklineData: [100, 120, 115, 130, 125, 140, 135, 150, 145, 160, 155, 165],
      navigateTo: 'revenue',
      isRevenue: true
    }
  ]

  const handleTileClick = (tile) => {
    switch (tile.navigateTo) {
      case 'documents':
        if (onNavigate) {
          onNavigate('documents')
        }
        break
      case 'companies':
        if (onNavigate) {
          onNavigate('companies')
        }
        break
      case 'users':
        // Future: Navigate to users page
        break
      case 'revenue':
        // Future: Navigate to revenue page
        break
      default:
        // Future: Navigate to respective page
        break
    }
  }

  return (
    <div className="space-y-6">
      {/* Welcome banner */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary-500 to-blue-600 rounded-xl p-6 text-white relative overflow-hidden"
      >
        <div className={`relative z-10 ${isRTL ? 'text-right' : 'text-left'}`}>
          <h1 className="text-2xl font-bold mb-2">
            {t('welcomeBack')}, {user?.name}! 👋
          </h1>
          <p className="text-blue-100">
            {currentCompany ? `Managing ${currentCompany.name}` : 'Your dashboard is ready to shine'}
          </p>
        </div>
        <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -mr-16 -mt-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full -ml-12 -mb-12"></div>
      </motion.div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <i className="fas fa-calendar text-gray-500"></i>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Date Range:</span>
            <div className="w-64">
              <CustomDateRangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
              />
            </div>
          </div>
          <motion.button
            onClick={() => handleDateRangeChange('today')}
            className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Reset
          </motion.button>
        </div>

        <motion.button
          onClick={toggleLiveRefresh}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
            isLiveRefresh
              ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
          }`}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <motion.i
            className={`fas fa-sync-alt ${isLiveRefresh ? 'animate-spin' : ''}`}
            animate={{ rotate: isLiveRefresh ? 360 : 0 }}
            transition={{ duration: 2, repeat: isLiveRefresh ? Infinity : 0, ease: "linear" }}
          />
          <span>Live Refresh</span>
        </motion.button>
      </div>

      {/* Stats tiles */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {tiles.map((tile, index) => (
          <motion.div
            key={tile.id}
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{
              delay: index * 0.1,
              type: "spring",
              stiffness: 100,
              damping: 15
            }}
            className="relative h-40 cursor-pointer group"
            onMouseEnter={() => handleTileFlip(tile.id)}
            onMouseLeave={() => handleTileFlip(tile.id)}
            onClick={() => handleTileClick(tile)}
            whileHover={{
              scale: 1.05,
              y: -5,
              boxShadow: "0 20px 40px rgba(0,0,0,0.15)"
            }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.div
              className="w-full h-full relative preserve-3d"
              animate={{ rotateY: flippedTiles[tile.id] ? 180 : 0 }}
              transition={{ duration: 0.8, type: "spring", stiffness: 100 }}
              style={{ transformStyle: "preserve-3d" }}
            >
              {/* Front side */}
              <div className={`absolute inset-0 w-full h-full bg-gradient-to-br ${tile.gradient} rounded-2xl p-6 text-white shadow-xl border border-white/20`}
                   style={{ backfaceVisibility: "hidden" }}>
                <div className="flex flex-col h-full">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm`}>
                      <motion.i
                        className={`${tile.icon} text-xl`}
                        animate={{ rotate: [0, 5, -5, 0] }}
                        transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
                      />
                    </div>
                    <motion.div
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        tile.trend === 'up'
                          ? 'bg-green-500/20 text-green-100'
                          : 'bg-red-500/20 text-red-100'
                      }`}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: index * 0.1 + 0.3 }}
                    >
                      <i className={`fas fa-arrow-${tile.trend === 'up' ? 'up' : 'down'} mr-1`} />
                      {tile.change}
                    </motion.div>
                  </div>

                  <div className="flex-1">
                    <p className="text-white/80 text-sm font-medium mb-1">{tile.title}</p>
                    <motion.div
                      className="text-3xl font-bold"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: index * 0.1 + 0.2, type: "spring" }}
                    >
                      {tile.isRevenue ? (
                        <SaudiRiyalDisplay amount={tile.value} />
                      ) : (
                        tile.value
                      )}
                    </motion.div>
                  </div>

                  {/* Mini sparkline */}
                  <div className="mt-2">
                    <div className="flex items-end space-x-1 h-6">
                      {tile.sparklineData.slice(-8).map((value, i) => (
                        <motion.div
                          key={i}
                          className="bg-white/30 rounded-sm flex-1"
                          initial={{ height: 0 }}
                          animate={{ height: `${(value / Math.max(...tile.sparklineData)) * 100}%` }}
                          transition={{ delay: index * 0.1 + i * 0.05 }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Back side */}
              <div className={`absolute inset-0 w-full h-full bg-gradient-to-br ${tile.gradient} rounded-2xl p-6 text-white shadow-xl border border-white/20`}
                   style={{ backfaceVisibility: "hidden", transform: "rotateY(180deg)" }}>
                <div className="flex flex-col justify-center h-full text-center">
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: flippedTiles[tile.id] ? 1 : 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <i className={`${tile.icon} text-4xl mb-3 opacity-80`} />
                    <p className="text-white/80 text-sm mb-2">{tile.period}</p>
                    <p className="text-3xl font-bold mb-1">{tile.change}</p>
                    <p className="text-white/80 text-xs">Growth Rate</p>

                    {/* Full sparkline chart */}
                    <div className="mt-4">
                      <div className="flex items-end justify-center space-x-1 h-8">
                        {tile.sparklineData.map((value, i) => (
                          <motion.div
                            key={i}
                            className="bg-white/40 rounded-sm w-2"
                            initial={{ height: 0 }}
                            animate={{
                              height: flippedTiles[tile.id]
                                ? `${(value / Math.max(...tile.sparklineData)) * 100}%`
                                : 0
                            }}
                            transition={{ delay: 0.5 + i * 0.03 }}
                          />
                        ))}
                      </div>
                    </div>

                    <motion.button
                      className="mt-3 px-4 py-2 bg-white/20 rounded-lg text-sm font-medium hover:bg-white/30 transition-colors duration-200"
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleTileClick(tile)
                      }}
                    >
                      View Details
                    </motion.button>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        ))}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Line Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="card p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Document Submissions
            </h3>
            <motion.button
              onClick={() => downloadChart('Line')}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <i className="fas fa-download"></i>
            </motion.button>
          </div>
          <div className="h-64">
            <Line data={lineChartData} options={chartOptions} />
          </div>
        </motion.div>

        {/* Bar Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="card p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Company Activity
            </h3>
            <motion.button
              onClick={() => downloadChart('Bar')}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <i className="fas fa-download"></i>
            </motion.button>
          </div>
          <div className="h-64">
            <Bar data={barChartData} options={chartOptions} />
          </div>
        </motion.div>

        {/* Pie Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="card p-6"
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Document Status
            </h3>
            <motion.button
              onClick={() => downloadChart('Pie')}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <i className="fas fa-download"></i>
            </motion.button>
          </div>
          <div className="h-64">
            <Pie data={pieChartData} options={{ ...chartOptions, scales: undefined }} />
          </div>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Recent Activity
          </h3>
          <div className="space-y-4">
            {[
              { action: 'Invoice submitted', company: 'Tech Solutions Ltd', time: '2 min ago', status: 'success' },
              { action: 'Company created', company: 'New Corp', time: '15 min ago', status: 'info' },
              { action: 'Validation error', company: 'Global Trading', time: '1 hour ago', status: 'error' },
              { action: 'Document cleared', company: 'Saudi Corp', time: '2 hours ago', status: 'success' }
            ].map((activity, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.9 + index * 0.1 }}
                className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg"
              >
                <div className={`w-2 h-2 rounded-full ${
                  activity.status === 'success' ? 'bg-green-500' :
                  activity.status === 'error' ? 'bg-red-500' : 'bg-blue-500'
                }`}></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {activity.action}
                  </p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">
                    {activity.company} • {activity.time}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default Dashboard
