import React, { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'
import { useTheme } from '../../context/ThemeContext'
import { useApp } from '../../context/AppContext'
import { useLanguage } from '../../context/LanguageContext'

const TopNavbar = ({ user, currentCompany, onToggleSidebar, sidebarCollapsed, onLogout, onNavigate, onLockScreen }) => {
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showCompanyMenu, setShowCompanyMenu] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [showSearch, setShowSearch] = useState(false)
  const [showLanguageMenu, setShowLanguageMenu] = useState(false)

  const { theme, toggleTheme } = useTheme()
  const { companies, switchCompany, appSettings } = useApp()
  const { language, switchLanguage, t, isRTL } = useLanguage()

  const userMenuRef = useRef(null)
  const companyMenuRef = useRef(null)
  const notificationRef = useRef(null)
  const languageMenuRef = useRef(null)

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
        setShowUserMenu(false)
      }
      if (companyMenuRef.current && !companyMenuRef.current.contains(event.target)) {
        setShowCompanyMenu(false)
      }
      if (notificationRef.current && !notificationRef.current.contains(event.target)) {
        setShowNotifications(false)
      }
      if (languageMenuRef.current && !languageMenuRef.current.contains(event.target)) {
        setShowLanguageMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleCompanySwitch = (company) => {
    switchCompany(company)
    setShowCompanyMenu(false)
    toast.success(`Now managing ${company.name}`, {
      icon: '🏢',
      duration: 3000,
    })
  }

  const handleLogout = () => {
    setShowUserMenu(false)
    onLogout()
    toast.success('Logged out successfully', {
      icon: '👋',
    })
  }

  const notifications = [
    { id: 1, title: 'New invoice submitted', message: 'Invoice #INV-001 has been processed', time: '2 min ago', type: 'success' },
    { id: 2, title: 'System update', message: 'ZATCA API updated to v2.1', time: '1 hour ago', type: 'info' },
    { id: 3, title: 'Validation error', message: 'Invoice #INV-002 failed validation', time: '3 hours ago', type: 'error' },
  ]

  const searchSuggestions = [
    { type: 'page', name: 'Dashboard', icon: 'fas fa-home' },
    { type: 'page', name: 'Companies', icon: 'fas fa-building' },
    { type: 'page', name: 'Documents', icon: 'fas fa-file-alt' },
    { type: 'company', name: 'Tech Solutions Ltd', icon: 'fas fa-building' },
    { type: 'company', name: 'Global Trading Co', icon: 'fas fa-building' },
  ]

  const filteredSuggestions = searchSuggestions.filter(item =>
    item.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4 sticky top-0 z-30 backdrop-blur-lg bg-white/95 dark:bg-gray-800/95"
    >
      <div className="flex items-center justify-between">
        {/* Left section */}
        <div className="flex items-center space-x-4">
          {/* Sidebar toggle */}
          <motion.button
            onClick={onToggleSidebar}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.i
              className={`fas ${sidebarCollapsed ? 'fa-bars' : 'fa-times'} text-gray-600 dark:text-gray-300`}
              animate={{ rotate: sidebarCollapsed ? 0 : 90 }}
              transition={{ duration: 0.2 }}
            />
          </motion.button>

          {/* App logo and name */}
          <motion.div
            className="flex items-center space-x-3 cursor-pointer"
            whileHover={{ scale: 1.02 }}
            onClick={() => window.location.reload()}
          >
            <div className="w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center">
              <motion.i
                className="fas fa-receipt text-white text-sm"
                animate={{ rotate: [0, 5, -5, 0] }}
                transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
              />
            </div>
            <span className="text-xl font-bold gradient-text hidden sm:block">
              {appSettings.appName}
            </span>
          </motion.div>
        </div>

        {/* Center section - Search */}
        <div className="flex-1 max-w-md mx-4 relative">
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => setShowSearch(true)}
              onBlur={() => setTimeout(() => setShowSearch(false), 200)}
              placeholder="Search companies, documents..."
              className="w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200"
            />
            <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          </div>

          {/* Search suggestions */}
          <AnimatePresence>
            {showSearch && searchQuery && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
              >
                {filteredSuggestions.length > 0 ? (
                  <div className="py-2">
                    {filteredSuggestions.map((item, index) => (
                      <motion.div
                        key={index}
                        className="px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer flex items-center space-x-3"
                        whileHover={{ x: 5 }}
                      >
                        <i className={`${item.icon} text-gray-400 w-4`}></i>
                        <span className="text-gray-700 dark:text-gray-300">{item.name}</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-auto">
                          {item.type}
                        </span>
                      </motion.div>
                    ))}
                  </div>
                ) : (
                  <div className="px-4 py-3 text-gray-500 dark:text-gray-400 text-sm">
                    No results found
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Right section */}
        <div className="flex items-center space-x-3">
          {/* Company selector */}
          <div className="relative" ref={companyMenuRef}>
            <motion.button
              onClick={() => setShowCompanyMenu(!showCompanyMenu)}
              className="flex items-center space-x-2 px-3 py-2 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <i className="fas fa-building text-primary-500"></i>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300 hidden md:block">
                {currentCompany?.name || 'Select Company'}
              </span>
              <i className={`fas fa-chevron-down text-gray-400 transition-transform duration-200 ${
                showCompanyMenu ? 'rotate-180' : ''
              }`}></i>
            </motion.button>

            <AnimatePresence>
              {showCompanyMenu && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full right-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
                >
                  <div className="py-2">
                    {companies.map((company) => (
                      <motion.button
                        key={company.id}
                        onClick={() => handleCompanySwitch(company)}
                        className={`w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150 ${
                          currentCompany?.id === company.id ? 'bg-primary-50 dark:bg-primary-900/20' : ''
                        }`}
                        whileHover={{ x: 5 }}
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${
                            currentCompany?.id === company.id ? 'bg-primary-500' : 'bg-gray-300 dark:bg-gray-600'
                          }`}></div>
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">
                              {company.name}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              {company.branches?.length || 0} branches
                            </div>
                          </div>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Language selector */}
          <div className="relative" ref={languageMenuRef}>
            <motion.button
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 flex items-center space-x-2"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setShowLanguageMenu(!showLanguageMenu)}
            >
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                {language === 'ar' ? '🇸🇦 AR' : '🇺🇸 EN'}
              </span>
              <motion.i
                className="fas fa-chevron-down text-xs text-gray-400"
                animate={{ rotate: showLanguageMenu ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              />
            </motion.button>

            {/* Language dropdown */}
            <AnimatePresence>
              {showLanguageMenu && (
                <motion.div
                  initial={{ opacity: 0, y: -10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -10, scale: 0.95 }}
                  transition={{ duration: 0.2 }}
                  className={`absolute ${isRTL ? 'left-0' : 'right-0'} mt-2 w-40 bg-white/95 dark:bg-gray-800/95 backdrop-blur-xl border border-gray-200 dark:border-gray-700 rounded-xl shadow-2xl z-50`}
                >
                  <div className="py-2">
                    <motion.button
                      onClick={() => {
                        switchLanguage('en')
                        setShowLanguageMenu(false)
                      }}
                      className={`w-full px-4 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150 ${
                        language === 'en' ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' : 'text-gray-700 dark:text-gray-300'
                      }`}
                      whileHover={{ x: isRTL ? -5 : 5 }}
                    >
                      <div className="flex items-center space-x-3">
                        <span>🇺🇸</span>
                        <span>English</span>
                        {language === 'en' && <i className="fas fa-check ml-auto text-primary-500" />}
                      </div>
                    </motion.button>

                    <motion.button
                      onClick={() => {
                        switchLanguage('ar')
                        setShowLanguageMenu(false)
                      }}
                      className={`w-full px-4 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150 ${
                        language === 'ar' ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' : 'text-gray-700 dark:text-gray-300'
                      }`}
                      whileHover={{ x: isRTL ? -5 : 5 }}
                    >
                      <div className="flex items-center space-x-3">
                        <span>🇸🇦</span>
                        <span>العربية</span>
                        {language === 'ar' && <i className="fas fa-check ml-auto text-primary-500" />}
                      </div>
                    </motion.button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Theme toggle */}
          <motion.button
            onClick={toggleTheme}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.i
              className={`fas ${theme === 'light' ? 'fa-moon' : 'fa-sun'} text-gray-600 dark:text-gray-300`}
              animate={{ rotate: theme === 'light' ? 0 : 180 }}
              transition={{ duration: 0.3 }}
            />
          </motion.button>

          {/* Notifications */}
          <div className="relative" ref={notificationRef}>
            <motion.button
              onClick={() => setShowNotifications(!showNotifications)}
              className="relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <i className="fas fa-bell text-gray-600 dark:text-gray-300"></i>
              <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                {notifications.length}
              </span>
            </motion.button>

            <AnimatePresence>
              {showNotifications && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
                >
                  <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 className="font-medium text-gray-900 dark:text-white">Notifications</h3>
                  </div>
                  <div className="max-h-64 overflow-y-auto">
                    {notifications.map((notification) => (
                      <motion.div
                        key={notification.id}
                        className="p-4 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer"
                        whileHover={{ x: 5 }}
                      >
                        <div className="flex items-start space-x-3">
                          <div className={`w-2 h-2 rounded-full mt-2 ${
                            notification.type === 'success' ? 'bg-green-500' :
                            notification.type === 'error' ? 'bg-red-500' : 'bg-blue-500'
                          }`}></div>
                          <div className="flex-1">
                            <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                              {notification.title}
                            </h4>
                            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                              {notification.message}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                              {notification.time}
                            </p>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* User menu */}
          <div className="relative" ref={userMenuRef}>
            <motion.button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-medium">
                  {user?.name?.charAt(0) || 'A'}
                </span>
              </div>
              <i className={`fas fa-chevron-down text-gray-400 transition-transform duration-200 ${
                showUserMenu ? 'rotate-180' : ''
              }`}></i>
            </motion.button>

            <AnimatePresence>
              {showUserMenu && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="absolute top-full right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50"
                >
                  <div className="py-2">
                    <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {user?.name}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {user?.email}
                      </p>
                    </div>

                    <motion.button
                      className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150"
                      whileHover={{ x: 5 }}
                      onClick={() => {
                        setShowUserMenu(false)
                        if (onNavigate) {
                          onNavigate('profile')
                        }
                      }}
                    >
                      <i className="fas fa-user w-4 mr-3"></i>
                      {t('profile')}
                    </motion.button>

                    <motion.button
                      className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150"
                      whileHover={{ x: 5 }}
                      onClick={() => {
                        setShowUserMenu(false)
                        if (onLockScreen) {
                          onLockScreen()
                        }
                      }}
                    >
                      <i className="fas fa-lock w-4 mr-3"></i>
                      Lock Screen
                    </motion.button>

                    <div className="border-t border-gray-200 dark:border-gray-700 my-1"></div>

                    <motion.button
                      onClick={handleLogout}
                      className="w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-150"
                      whileHover={{ x: 5 }}
                    >
                      <i className="fas fa-sign-out-alt w-4 mr-3"></i>
                      Logout
                    </motion.button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
    </motion.header>
  )
}

export default TopNavbar
