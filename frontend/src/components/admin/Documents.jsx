import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'
import { useLanguage } from '../../context/LanguageContext'
import FancyDropdown from '../common/FancyDropdown'
import LoadingSpinner from '../common/LoadingSpinner'
import SaudiRiyalIcon from '../common/SaudiRiyalIcon'
import CustomDateRangePicker from '../common/CustomDateRangePicker'
import ActionDropdown from '../common/ActionDropdown'
import InvoiceModal from '../common/InvoiceModal'

const Documents = () => {
  const [showFilters, setShowFilters] = useState(false)
  const [filters, setFilters] = useState({
    invoiceType: '',
    documentType: '',
    dateRange: '',
    status: '',
    environment: ''
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [selectedDocuments, setSelectedDocuments] = useState([])
  const [isUpdatingStatus, setIsUpdatingStatus] = useState({})
  const [isSyncing, setIsSyncing] = useState({})
  const [showInvoiceModal, setShowInvoiceModal] = useState(false)
  const [selectedDocument, setSelectedDocument] = useState(null)
  const [itemsPerPage, setItemsPerPage] = useState(25)

  const { t, isRTL } = useLanguage()

  // Status options for dropdown
  const statusOptions = [
    { value: 'error', label: t('error'), icon: 'fas fa-exclamation-circle' },
    { value: 'not-submitted', label: t('notSubmitted'), icon: 'fas fa-clock' },
    { value: 'reported', label: t('reported'), icon: 'fas fa-flag' },
    { value: 'cleared', label: t('cleared'), icon: 'fas fa-check-circle' }
  ]

  // Environment options for filter
  const environmentOptions = [
    { value: '', label: 'All Environments', icon: 'fas fa-globe' },
    { value: 'sandbox', label: t('sandbox'), icon: 'fas fa-flask' },
    { value: 'simulation', label: t('simulation'), icon: 'fas fa-play-circle' },
    { value: 'production', label: t('production'), icon: 'fas fa-server' }
  ]

  // Saudi Riyal symbol
  const saudiRiyalSymbol = '﷼'

  // Enhanced mock data
  const [documents, setDocuments] = useState([
    {
      id: 1,
      slNo: 'DOC-001',
      documentType: 'Invoice',
      invoiceType: 'B2B',
      dateTime: '2024-01-15 10:30:00',
      status: 'cleared',
      companyName: 'Tech Solutions Ltd',
      branchName: 'Main Branch',
      totalAmount: 15750.00,
      totalTax: 2362.50,
      syncStatus: 'Synced',
      environment: 'production'
    },
    {
      id: 2,
      slNo: 'DOC-002',
      documentType: 'Credit Note',
      invoiceType: 'B2C',
      dateTime: '2024-01-15 11:45:00',
      status: 'error',
      companyName: 'Global Trading Co',
      branchName: 'Riyadh Branch',
      totalAmount: 8500.00,
      totalTax: 1275.00,
      syncStatus: 'Not Synced',
      environment: 'sandbox'
    },
    {
      id: 3,
      slNo: 'DOC-003',
      documentType: 'Invoice',
      invoiceType: 'B2B',
      dateTime: '2024-01-15 14:20:00',
      status: 'reported',
      companyName: 'Saudi Corp',
      branchName: 'Jeddah Branch',
      totalAmount: 25000.00,
      totalTax: 3750.00,
      syncStatus: 'Synced',
      environment: 'simulation'
    },
    {
      id: 4,
      slNo: 'DOC-004',
      documentType: 'Debit Note',
      invoiceType: 'B2C',
      dateTime: '2024-01-15 16:10:00',
      status: 'not-submitted',
      companyName: 'Modern Systems',
      branchName: 'Main Office',
      totalAmount: 12300.00,
      totalTax: 1845.00,
      syncStatus: 'Not Synced',
      environment: 'production'
    }
  ])

  // Status update function
  const handleStatusUpdate = async (documentId, newStatus) => {
    setIsUpdatingStatus(prev => ({ ...prev, [documentId]: true }))

    // Simulate API call
    setTimeout(() => {
      setIsUpdatingStatus(prev => ({ ...prev, [documentId]: false }))
      toast.success(t('statusUpdated'), {
        icon: '✅',
        duration: 3000,
      })
    }, 1500)
  }

  // Sync function
  const handleSync = async (documentId) => {
    setIsSyncing(prev => ({ ...prev, [documentId]: true }))

    // Simulate sync API call
    setTimeout(() => {
      setIsSyncing(prev => ({ ...prev, [documentId]: false }))

      // Update the document sync status in the mock data
      setDocuments(prevDocs =>
        prevDocs.map(doc =>
          doc.id === documentId
            ? { ...doc, syncStatus: 'Synced' }
            : doc
        )
      )

      toast.success('Document synced successfully!', {
        icon: '🔄',
        duration: 3000,
      })
    }, 2000)
  }

  // Download functions
  const handleDownloadPDF = (documentId) => {
    toast.success('PDF download started!', {
      icon: '📄',
      duration: 2000,
    })
  }

  const handleDownloadXML = (documentId) => {
    toast.success('XML download started!', {
      icon: '📄',
      duration: 2000,
    })
  }

  // View document details
  const handleViewDocument = (documentId) => {
    setSelectedDocument(documents.find(doc => doc.id === documentId))
    setShowInvoiceModal(true)
  }

  const stats = {
    totalSubmitted: 1247,
    reported: 892,
    cleared: 645,
    error: 78
  }

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'cleared': return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400'
      case 'reported': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'error': return 'text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-400'
      case 'not-submitted': return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400'
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  const getEnvironmentColor = (environment) => {
    switch (environment?.toLowerCase()) {
      case 'production': return 'text-green-600 bg-green-100 dark:bg-green-900/20 dark:text-green-400'
      case 'simulation': return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20 dark:text-purple-400'
      case 'sandbox': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400'
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  const getDocumentIcon = (type) => {
    switch (type) {
      case 'Invoice': return 'fas fa-file-invoice'
      case 'Credit Note': return 'fas fa-file-minus'
      case 'Debit Note': return 'fas fa-file-plus'
      default: return 'fas fa-file-alt'
    }
  }

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleStatusFilter = (status) => {
    setFilters(prev => ({ ...prev, status: status }))
  }

  const clearFilters = () => {
    setFilters({
      invoiceType: '',
      documentType: '',
      dateRange: '',
      status: '',
      environment: ''
    })
    toast.success('Filters cleared')
  }

  const handleDocumentAction = (action, document) => {
    toast.success(`${action} action performed for ${document.slNo}`, {
      icon: action === 'Download Signed XML' ? '📄' :
            action === 'Download PDF A3 Invoice' ? '📋' : '👁️'
    })
  }

  const handleBulkAction = (action) => {
    if (selectedDocuments.length === 0) {
      toast.error('Please select documents first')
      return
    }
    toast.success(`${action} performed for ${selectedDocuments.length} documents`)
    setSelectedDocuments([])
  }

  const toggleDocumentSelection = (docId) => {
    setSelectedDocuments(prev =>
      prev.includes(docId)
        ? prev.filter(id => id !== docId)
        : [...prev, docId]
    )
  }

  const selectAllDocuments = () => {
    if (selectedDocuments.length === documents.length) {
      setSelectedDocuments([])
    } else {
      setSelectedDocuments(documents.map(doc => doc.id))
    }
  }

  const totalPages = Math.ceil(documents.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const paginatedDocuments = documents.slice(startIndex, startIndex + itemsPerPage)

  return (
    <div className="space-y-6">
      {/* Header with Gradient */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0 p-6 bg-gradient-to-r from-blue-50 to-teal-50 dark:from-blue-900/20 dark:to-teal-900/20 rounded-2xl border border-blue-100 dark:border-blue-800/50 mb-6"
      >
        <div>
          <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
            Documents
          </h1>
          <p className="text-gray-600 dark:text-gray-400">Manage e-invoices and documents</p>
        </div>
        <motion.button
          onClick={() => setShowFilters(!showFilters)}
          className="btn-primary"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <i className="fas fa-filter mr-2"></i>
          {showFilters ? 'Hide Filters' : 'Show Filters'}
        </motion.button>
      </motion.div>

      {/* Stats tiles */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[
          { title: 'Total Submitted', value: stats.totalSubmitted, color: 'bg-blue-500', icon: 'fas fa-paper-plane' },
          { title: 'Reported', value: stats.reported, color: 'bg-yellow-500', icon: 'fas fa-flag' },
          { title: 'Cleared', value: stats.cleared, color: 'bg-green-500', icon: 'fas fa-check-circle' },
          { title: 'Error', value: stats.error, color: 'bg-red-500', icon: 'fas fa-exclamation-triangle' }
        ].map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`card p-6 cursor-pointer hover:shadow-lg transition-shadow duration-200`}
            whileHover={{ scale: 1.02, y: -2 }}
            onClick={() => toast(`Filter by ${stat.title}`, { icon: '🔍' })}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-600 dark:text-gray-400 text-sm font-medium">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                  {stat.value.toLocaleString()}
                </p>
              </div>
              <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                <i className={`${stat.icon} text-white text-xl`}></i>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Filters */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ height: 0, opacity: 0, y: -20 }}
            animate={{ height: 'auto', opacity: 1, y: 0 }}
            exit={{ height: 0, opacity: 0, y: -20 }}
            transition={{ duration: 0.4, ease: "easeOut" }}
            className="bg-gradient-to-r from-blue-50 to-teal-50 dark:from-blue-900/20 dark:to-teal-900/20 backdrop-blur-xl border border-blue-100 dark:border-blue-800/50 rounded-2xl p-6 overflow-hidden shadow-lg"
          >
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Invoice Type
                </label>
                <select
                  value={filters.invoiceType}
                  onChange={(e) => handleFilterChange('invoiceType', e.target.value)}
                  className="input-field"
                >
                  <option value="">All Types</option>
                  <option value="B2B">B2B</option>
                  <option value="B2C">B2C</option>
                  <option value="Both">Both</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Document Type
                </label>
                <select
                  value={filters.documentType}
                  onChange={(e) => handleFilterChange('documentType', e.target.value)}
                  className="input-field"
                >
                  <option value="">All Documents</option>
                  <option value="Invoice">Invoice</option>
                  <option value="Credit Note">Credit Note</option>
                  <option value="Debit Note">Debit Note</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Date Range
                </label>
                <select
                  value={filters.dateRange}
                  onChange={(e) => handleFilterChange('dateRange', e.target.value)}
                  className="input-field"
                >
                  <option value="">All Dates</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="quarter">This Quarter</option>
                  <option value="year">This Year</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Environment
                </label>
                <select
                  value={filters.environment}
                  onChange={(e) => handleFilterChange('environment', e.target.value)}
                  className="input-field"
                >
                  <option value="">All Environments</option>
                  <option value="sandbox">Sandbox</option>
                  <option value="simulation">Simulation</option>
                  <option value="production">Production</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Status
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="input-field"
                >
                  <option value="">All Status</option>
                  <option value="error">Error</option>
                  <option value="not-submitted">Not Submitted</option>
                  <option value="reported">Reported</option>
                  <option value="cleared">Cleared</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end mt-4">
              <motion.button
                onClick={clearFilters}
                className="btn-secondary"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <i className="fas fa-times mr-2"></i>
                Clear Filters
              </motion.button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Bulk actions */}
      <AnimatePresence>
        {selectedDocuments.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-primary-50 dark:bg-primary-900/20 border border-primary-200 dark:border-primary-800 rounded-lg p-4"
          >
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-primary-700 dark:text-primary-300">
                {selectedDocuments.length} documents selected
              </span>
              <div className="flex items-center space-x-2">
                <motion.button
                  onClick={() => handleBulkAction('Bulk Download')}
                  className="px-3 py-1 text-sm bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors duration-200"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <i className="fas fa-download mr-1"></i>
                  Bulk Download
                </motion.button>
                <motion.button
                  onClick={() => setSelectedDocuments([])}
                  className="px-3 py-1 text-sm bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Clear Selection
                </motion.button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Documents table */}
      <div className="card" style={{ position: 'relative', overflow: 'visible' }}>
        <div className="overflow-x-auto" style={{ position: 'relative', overflow: 'visible' }}>
          <table className="w-full">
            <thead className="bg-gradient-to-r from-blue-50 to-teal-50 dark:from-blue-900/30 dark:to-teal-900/30 border-b border-blue-100 dark:border-blue-800/50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Actions
                </th>
                <th className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedDocuments.length === documents.length}
                    onChange={selectAllDocuments}
                    className="rounded"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Sl. No.
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Document Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Invoice Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Date & Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Company
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Total Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  Sync Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {paginatedDocuments.map((document, index) => (
                <motion.tr
                  key={document.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className="table-row hover:bg-gradient-to-r hover:from-blue-50 hover:to-teal-50 dark:hover:from-blue-900/10 dark:hover:to-teal-900/10 transition-all duration-300 cursor-pointer relative overflow-hidden group"
                  whileHover={{
                    scale: 1.01,
                    boxShadow: "0 4px 20px rgba(59, 130, 246, 0.1)"
                  }}
                  onClick={(e) => {
                    // Don't trigger row click if clicking on action dropdown or checkbox
                    if (!e.target.closest('.action-dropdown') && !e.target.closest('input[type="checkbox"]')) {
                      handleViewDocument(document.id)
                    }
                  }}
                  onHoverStart={() => {
                    // Ripple effect simulation
                  }}
                >
                  {/* Actions Column */}
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="action-dropdown">
                      <ActionDropdown
                        document={document}
                        onSync={handleSync}
                        onView={handleViewDocument}
                        onDownloadPDF={handleDownloadPDF}
                        onDownloadXML={handleDownloadXML}
                        isSyncing={isSyncing[document.id]}
                      />
                    </div>
                  </td>

                  <td className="px-6 py-4">
                    <input
                      type="checkbox"
                      checked={selectedDocuments.includes(document.id)}
                      onChange={() => toggleDocumentSelection(document.id)}
                      className="rounded"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {document.slNo}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    <div className="flex items-center space-x-2">
                      <i className={`${getDocumentIcon(document.documentType)} text-gray-400`}></i>
                      <span>{document.documentType}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {document.invoiceType}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {new Date(document.dateTime).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(document.status)}`}>
                      {document.status === 'cleared' && <i className="fas fa-check mr-1" />}
                      {document.status === 'error' && <i className="fas fa-exclamation-circle mr-1" />}
                      {document.status === 'reported' && <i className="fas fa-flag mr-1" />}
                      {document.status === 'not-submitted' && <i className="fas fa-clock mr-1" />}
                      {document.status.charAt(0).toUpperCase() + document.status.slice(1).replace('-', ' ')}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    <div>
                      <div className="font-medium">{document.companyName}</div>
                      <div className="text-gray-500 dark:text-gray-400">{document.branchName}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    <div>
                      <div className="font-medium flex items-center space-x-1">
                        <SaudiRiyalIcon size="sm" />
                        <span>{document.totalAmount.toLocaleString()}</span>
                      </div>
                      <div className="text-gray-500 dark:text-gray-400 flex items-center space-x-1">
                        <span>Tax:</span>
                        <SaudiRiyalIcon size="sm" />
                        <span>{document.totalTax.toLocaleString()}</span>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <motion.span
                      className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 ${
                        document.syncStatus === 'Synced'
                          ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 shadow-lg shadow-green-500/20'
                          : 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 shadow-lg shadow-gray-500/20'
                      }`}
                      whileHover={{ scale: 1.05, boxShadow: "0 0 20px rgba(34, 197, 94, 0.3)" }}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: 0.1 }}
                    >
                      {document.syncStatus === 'Synced' ? (
                        <><i className="fas fa-check mr-1"></i>Synced</>
                      ) : (
                        <><i className="fas fa-minus mr-1"></i>Not Synced</>
                      )}
                    </motion.span>
                  </td>

                </motion.tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Pagination */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, documents.length)} of {documents.length} documents
              </p>
              <div className="flex items-center space-x-2">
                <label className="text-sm text-gray-600 dark:text-gray-400">Items per page:</label>
                <select
                  value={itemsPerPage}
                  onChange={(e) => {
                    const newItemsPerPage = parseInt(e.target.value)
                    setItemsPerPage(newItemsPerPage)
                    setCurrentPage(1) // Reset to first page
                  }}
                  className="text-sm border border-gray-300 dark:border-gray-600 rounded-lg px-2 py-1 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <option value={10}>10</option>
                  <option value={25}>25</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                  <option value={250}>250</option>
                  <option value={500}>500</option>
                </select>
              </div>
            </div>
            {totalPages > 1 && (
              <div className="flex items-center space-x-2">
                <motion.button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-800"
                  whileHover={{ scale: currentPage > 1 ? 1.05 : 1 }}
                  whileTap={{ scale: currentPage > 1 ? 0.95 : 1 }}
                >
                  Previous
                </motion.button>
                <span className="px-3 py-1 text-sm bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400 rounded-lg">
                  {currentPage} of {totalPages}
                </span>
                <motion.button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-800"
                  whileHover={{ scale: currentPage < totalPages ? 1.05 : 1 }}
                  whileTap={{ scale: currentPage < totalPages ? 0.95 : 1 }}
                >
                  Next
                </motion.button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Invoice Details Modal */}
      <InvoiceModal
        isOpen={showInvoiceModal}
        onClose={() => setShowInvoiceModal(false)}
        document={selectedDocument}
      />
    </div>
  )
}

export default Documents
