import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import toast from 'react-hot-toast'
import { useTheme } from '../../context/ThemeContext'
import { useApp } from '../../context/AppContext'

const Settings = () => {
  const [activeTab, setActiveTab] = useState('general')
  const { theme, primaryColor, updatePrimaryColor } = useTheme()
  const { appSettings, updateAppSettings } = useApp()

  const [formData, setFormData] = useState({
    appName: appSettings.appName,
    defaultLanguage: appSettings.defaultLanguage,
    themeColor: primaryColor
  })
  const [showResetModal, setShowResetModal] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [updateProgress, setUpdateProgress] = useState(0)

  const currentVersion = '2.4.1'
  const availableVersion = '2.4.3'
  const hasUpdate = currentVersion !== availableVersion

  const tabs = [
    { id: 'general', name: 'General', icon: 'fas fa-cog' },
    { id: 'appearance', name: 'Appearance', icon: 'fas fa-palette' }
  ]

  const predefinedColors = [
    { name: 'Facebook Blue', value: '#1877F2' },
    { name: 'Twitter Blue', value: '#1DA1F2' },
    { name: 'Instagram Purple', value: '#E4405F' },
    { name: 'WhatsApp Green', value: '#25D366' },
    { name: 'YouTube Red', value: '#FF0000' },
    { name: 'LinkedIn Blue', value: '#0077B5' },
    { name: 'GitHub Dark', value: '#24292E' },
    { name: 'Slack Purple', value: '#4A154B' }
  ]

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleColorChange = (color) => {
    setFormData(prev => ({ ...prev, themeColor: color }))
    updatePrimaryColor(color)
    toast.success('Theme color updated!', {
      icon: '🎨',
    })
  }

  const handleSave = () => {
    updateAppSettings({
      appName: formData.appName,
      defaultLanguage: formData.defaultLanguage
    })

    toast.success('Settings saved successfully!', {
      icon: '🎉',
      duration: 3000,
    })
  }

  const handleReset = () => {
    setShowResetModal(true)
  }

  const confirmReset = () => {
    const defaults = {
      appName: 'ZATCA Admin Panel',
      defaultLanguage: 'en',
      themeColor: '#1877F2'
    }

    setFormData(defaults)
    updateAppSettings({
      appName: defaults.appName,
      defaultLanguage: defaults.defaultLanguage
    })
    updatePrimaryColor(defaults.themeColor)
    setShowResetModal(false)

    toast.success('Settings reset to defaults!', {
      icon: '🔄',
      duration: 3000,
    })
  }

  const handleUpdate = async () => {
    setIsUpdating(true)
    setUpdateProgress(0)

    // Simulate update progress
    const steps = [
      { progress: 20, message: 'Downloading update files...' },
      { progress: 40, message: 'Preparing installation...' },
      { progress: 60, message: 'Installing new features...' },
      { progress: 80, message: 'Updating database...' },
      { progress: 100, message: 'Finalizing update...' }
    ]

    for (const step of steps) {
      await new Promise(resolve => setTimeout(resolve, 1000))
      setUpdateProgress(step.progress)
      toast.loading(step.message, { id: 'update-progress' })
    }

    setTimeout(() => {
      setIsUpdating(false)
      setUpdateProgress(0)
      toast.success('🎉 Application updated successfully! Amazing new features are now available!', {
        id: 'update-progress',
        duration: 5000,
      })
    }, 500)
  }

  const handleLogoUpload = (type) => {
    // Simulate file upload
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    input.onchange = (e) => {
      const file = e.target.files[0]
      if (file) {
        toast.success(`${type} logo "${file.name}" uploaded successfully!`, {
          icon: '🖼️',
          duration: 3000,
        })
      }
    }
    input.click()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
        <p className="text-gray-600 dark:text-gray-400">Customize your application preferences</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <motion.button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                activeTab === tab.id
                  ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                  : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
              whileHover={{ y: -2 }}
              whileTap={{ y: 0 }}
            >
              <div className="flex items-center space-x-2">
                <i className={tab.icon}></i>
                <span>{tab.name}</span>
              </div>
            </motion.button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Settings Form */}
        <div className="lg:col-span-2">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3 }}
            className="card p-6"
          >
            {activeTab === 'general' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  General Settings
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Application Name
                    </label>
                    <input
                      type="text"
                      value={formData.appName}
                      onChange={(e) => handleInputChange('appName', e.target.value)}
                      className="input-field"
                      placeholder="Enter application name"
                    />
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      This will be displayed in the navigation bar and browser title
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Default Language
                    </label>
                    <select
                      value={formData.defaultLanguage}
                      onChange={(e) => handleInputChange('defaultLanguage', e.target.value)}
                      className="input-field"
                    >
                      <option value="en">🇺🇸 English</option>
                      <option value="ar">🇸🇦 العربية (Arabic)</option>
                    </select>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      Default language for new users and system messages
                    </p>
                  </div>
                </div>

                {/* Application Update Section */}
                <div className="p-6 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-xl border border-green-200 dark:border-green-800">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
                        <i className="fas fa-download text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          Application Update
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          Keep your application up to date with the latest features
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-white dark:bg-gray-800 rounded-xl shadow-sm">
                      <div>
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          Current Version: <span className="text-blue-600 dark:text-blue-400">{currentVersion}</span>
                        </p>
                        {hasUpdate && (
                          <p className="text-sm text-green-600 dark:text-green-400 mt-1">
                            New Version Available: <span className="font-semibold">{availableVersion}</span>
                          </p>
                        )}
                      </div>

                      {hasUpdate && !isUpdating && (
                        <motion.button
                          onClick={handleUpdate}
                          className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-all duration-200 shadow-lg hover:shadow-xl"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <i className="fas fa-download mr-2" />
                          Update Now
                        </motion.button>
                      )}
                    </div>

                    {isUpdating && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl border border-blue-200 dark:border-blue-800"
                      >
                        <div className="flex items-center space-x-3 mb-3">
                          <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                          <p className="text-sm font-medium text-blue-900 dark:text-blue-100">
                            Please wait while we update the application...
                          </p>
                        </div>
                        <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
                          <motion.div
                            className="bg-blue-600 h-2 rounded-full"
                            initial={{ width: 0 }}
                            animate={{ width: `${updateProgress}%` }}
                            transition={{ duration: 0.5 }}
                          />
                        </div>
                        <p className="text-xs text-blue-700 dark:text-blue-300 mt-2">
                          🚀 New amazing features are coming soon! Get ready for an enhanced experience!
                        </p>
                      </motion.div>
                    )}

                    {!hasUpdate && !isUpdating && (
                      <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-xl border border-green-200 dark:border-green-800">
                        <div className="flex items-center space-x-2">
                          <i className="fas fa-check-circle text-green-600 dark:text-green-400" />
                          <p className="text-sm text-green-800 dark:text-green-200">
                            You're running the latest version of the application!
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'appearance' && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Appearance Settings
                </h3>

                <div className="space-y-6">
                  {/* Logo Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                      Application Logo
                    </label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Light Mode Logo */}
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Light Mode</p>
                        <motion.div
                          className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center cursor-pointer hover:border-primary-500 transition-colors duration-200"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => handleLogoUpload('light')}
                        >
                          <i className="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Click to upload light mode logo
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                            PNG, JPG up to 2MB
                          </p>
                        </motion.div>
                      </div>

                      {/* Dark Mode Logo */}
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Dark Mode</p>
                        <motion.div
                          className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center cursor-pointer hover:border-primary-500 transition-colors duration-200"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          onClick={() => handleLogoUpload('dark')}
                        >
                          <i className="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Click to upload dark mode logo
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                            PNG, JPG up to 2MB
                          </p>
                        </motion.div>
                      </div>
                    </div>
                  </div>

                  {/* Theme Color */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                      Theme Color
                    </label>

                    {/* Color Picker */}
                    <div className="flex items-center space-x-4 mb-4">
                      <input
                        type="color"
                        value={formData.themeColor}
                        onChange={(e) => handleColorChange(e.target.value)}
                        className="w-12 h-12 rounded-lg border border-gray-300 dark:border-gray-600 cursor-pointer"
                      />
                      <div>
                        <input
                          type="text"
                          value={formData.themeColor}
                          onChange={(e) => handleColorChange(e.target.value)}
                          className="input-field w-32"
                          placeholder="#1877F2"
                        />
                        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                          Hex color code
                        </p>
                      </div>
                    </div>

                    {/* Predefined Colors */}
                    <div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        Or choose from presets:
                      </p>
                      <div className="grid grid-cols-4 gap-3">
                        {predefinedColors.map((color) => (
                          <motion.button
                            key={color.value}
                            onClick={() => handleColorChange(color.value)}
                            className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                              formData.themeColor === color.value
                                ? 'border-gray-900 dark:border-white'
                                : 'border-gray-200 dark:border-gray-700 hover:border-gray-400 dark:hover:border-gray-500'
                            }`}
                            style={{ backgroundColor: color.value }}
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                            title={color.name}
                          >
                            <div className="w-full h-6 rounded"></div>
                            <p className="text-xs text-white mt-1 font-medium text-shadow">
                              {color.name}
                            </p>
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
              <motion.button
                onClick={handleReset}
                className="px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <i className="fas fa-undo mr-2"></i>
                Reset to Defaults
              </motion.button>

              <motion.button
                onClick={handleSave}
                className="btn-primary"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <i className="fas fa-save mr-2"></i>
                Save Changes
              </motion.button>
            </div>
          </motion.div>
        </div>

        {/* Live Preview */}
        <div className="lg:col-span-1">
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
            className="card p-6 sticky top-6"
          >
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Live Preview
            </h3>

            {/* Mini navbar preview */}
            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4">
              <div className="flex items-center space-x-3">
                <div
                  className="w-8 h-8 rounded-lg flex items-center justify-center"
                  style={{ backgroundColor: formData.themeColor }}
                >
                  <i className="fas fa-receipt text-white text-sm"></i>
                </div>
                <span className="font-bold text-gray-900 dark:text-white">
                  {formData.appName}
                </span>
              </div>
            </div>

            {/* Mini button preview */}
            <div className="space-y-3">
              <motion.button
                className="w-full py-2 px-4 rounded-lg text-white font-medium transition-all duration-200"
                style={{ backgroundColor: formData.themeColor }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                Primary Button
              </motion.button>

              <div className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: formData.themeColor }}
                ></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Accent elements
                </span>
              </div>
            </div>

            <div className="mt-6 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-start space-x-2">
                <i className="fas fa-info-circle text-blue-500 mt-0.5"></i>
                <div>
                  <p className="text-sm font-medium text-blue-800 dark:text-blue-300">
                    Preview
                  </p>
                  <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                    Changes are applied in real-time. Save to make them permanent.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Reset Confirmation Modal */}
      <AnimatePresence>
        {showResetModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setShowResetModal(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              className="bg-gradient-to-br from-red-50/95 via-white/95 to-red-50/95 dark:from-red-900/20 dark:via-gray-800/95 dark:to-red-900/20 backdrop-blur-xl rounded-2xl shadow-2xl border border-red-200/50 dark:border-red-700/50 p-6 w-full max-w-md"
              onClick={(e) => e.stopPropagation()}
              style={{
                boxShadow: '0 0 50px rgba(239, 68, 68, 0.2), 0 25px 50px rgba(0, 0, 0, 0.15)'
              }}
            >
              <div className="text-center">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.1, type: "spring" }}
                  className="mx-auto flex items-center justify-center w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full mb-4"
                >
                  <i className="fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-xl" />
                </motion.div>

                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  Reset Settings
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Are you sure you want to reset all settings to defaults? This action cannot be undone.
                </p>

                <div className="flex space-x-3">
                  <motion.button
                    onClick={() => setShowResetModal(false)}
                    className="flex-1 px-4 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-xl font-medium transition-all duration-200 hover:bg-gray-200 dark:hover:bg-gray-600"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Cancel
                  </motion.button>
                  <motion.button
                    onClick={confirmReset}
                    className="flex-1 px-4 py-3 bg-red-500 text-white rounded-xl font-medium transition-all duration-200 hover:bg-red-600 shadow-lg hover:shadow-xl"
                    whileHover={{ scale: 1.02, boxShadow: "0 10px 25px rgba(239, 68, 68, 0.3)" }}
                    whileTap={{ scale: 0.98 }}
                  >
                    Reset Settings
                  </motion.button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default Settings
