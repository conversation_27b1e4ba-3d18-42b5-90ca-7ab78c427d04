import React, { useState } from 'react'
import { motion } from 'framer-motion'
import toast from 'react-hot-toast'
import { useLanguage } from '../../context/LanguageContext'

const Sidebar = ({ currentPage, onPageChange, collapsed, onToggle, onLogout }) => {
  const [hoveredItem, setHoveredItem] = useState(null)
  const { t, isRTL } = useLanguage()

  const menuItems = [
    {
      id: 'dashboard',
      name: t('dashboard'),
      icon: 'fas fa-home',
      description: 'Overview and analytics'
    },
    {
      id: 'companies',
      name: t('companies'),
      icon: 'fas fa-building',
      description: 'Manage companies and branches'
    },
    {
      id: 'documents',
      name: t('documents'),
      icon: 'fas fa-file-alt',
      description: 'E-invoices and documents'
    },
    {
      id: 'profile',
      name: t('profile'),
      icon: 'fas fa-user',
      description: 'Manage your profile'
    },
    {
      id: 'settings',
      name: t('settings'),
      icon: 'fas fa-cog',
      description: 'Application settings'
    }
  ]

  const handleItemClick = (itemId) => {
    onPageChange(itemId)
  }

  const handleLogoutClick = () => {
    onLogout()
    toast.success('Logged out successfully', {
      icon: '👋',
    })
  }

  return (
    <>
      {/* Sidebar */}
      <motion.aside
        initial={{ x: isRTL ? 300 : -300 }}
        animate={{ x: 0, width: collapsed ? 64 : 256 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        className={`fixed ${isRTL ? 'right-0' : 'left-0'} top-0 h-full bg-white dark:bg-gray-800 ${isRTL ? 'border-l' : 'border-r'} border-gray-200 dark:border-gray-700 z-50 shadow-lg`}
        dir={isRTL ? 'rtl' : 'ltr'}
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <motion.div
              className="flex items-center space-x-3"
              animate={{ justifyContent: collapsed ? 'center' : 'flex-start' }}
            >
              <div className="w-10 h-10 bg-primary-500 rounded-lg flex items-center justify-center">
                <motion.i
                  className="fas fa-receipt text-white"
                  animate={{ rotate: [0, 5, -5, 0] }}
                  transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                />
              </div>
              {!collapsed && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <h1 className="text-lg font-bold gradient-text">ZATCA</h1>
                  <p className="text-xs text-gray-500 dark:text-gray-400">Admin Panel</p>
                </motion.div>
              )}
            </motion.div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2">
            {menuItems.map((item) => (
              <div key={item.id} className="relative">
                <motion.button
                  onClick={() => handleItemClick(item.id)}
                  onMouseEnter={() => setHoveredItem(item.id)}
                  onMouseLeave={() => setHoveredItem(null)}
                  className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200 group ${
                    currentPage === item.id
                      ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400'
                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }`}
                  whileHover={{ scale: 1.02, x: 5 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <motion.i
                    className={`${item.icon} text-lg ${
                      currentPage === item.id ? 'text-primary-600 dark:text-primary-400' : ''
                    }`}
                    animate={{
                      rotate: item.icon.includes('fa-cog') && currentPage === item.id ? 360 : 0
                    }}
                    transition={{ duration: 0.5 }}
                  />

                  {!collapsed && (
                    <motion.span
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.2 }}
                      className="font-medium"
                    >
                      {item.name}
                    </motion.span>
                  )}

                  {/* Active indicator */}
                  {currentPage === item.id && (
                    <motion.div
                      layoutId="activeIndicator"
                      className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-primary-500 rounded-l-full"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.2 }}
                    />
                  )}
                </motion.button>

                {/* Tooltip for collapsed sidebar */}
                {collapsed && hoveredItem === item.id && (
                  <motion.div
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -10 }}
                    className="absolute left-full top-0 ml-2 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-sm rounded-lg shadow-lg z-50 whitespace-nowrap"
                  >
                    <div className="font-medium">{item.name}</div>
                    <div className="text-xs text-gray-300 dark:text-gray-400">
                      {item.description}
                    </div>
                    {/* Arrow */}
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1 w-2 h-2 bg-gray-900 dark:bg-gray-700 rotate-45"></div>
                  </motion.div>
                )}
              </div>
            ))}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700 space-y-2">
            {/* Collapse toggle */}
            <motion.button
              onClick={onToggle}
              className="w-full flex items-center justify-center space-x-3 px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-all duration-200"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <motion.i
                className={`fas ${collapsed ? 'fa-chevron-right' : 'fa-chevron-left'}`}
                animate={{ rotate: collapsed ? 0 : 180 }}
                transition={{ duration: 0.2 }}
              />
              {!collapsed && (
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.2 }}
                  className="text-sm font-medium"
                >
                  Collapse
                </motion.span>
              )}
            </motion.button>

            {/* Logout button */}
            <motion.button
              onClick={handleLogoutClick}
              className="w-full flex items-center justify-center space-x-3 px-3 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-all duration-200"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <i className="fas fa-sign-out-alt"></i>
              {!collapsed && (
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.2 }}
                  className="text-sm font-medium"
                >
                  Logout
                </motion.span>
              )}
            </motion.button>
          </div>
        </div>
      </motion.aside>

      {/* Resize handle */}
      {!collapsed && (
        <motion.div
          className="fixed left-64 top-0 w-1 h-full cursor-col-resize hover:bg-primary-500 transition-colors duration-200 z-40"
          whileHover={{ width: 4 }}
          onMouseDown={(e) => {
            // Simulate drag to resize functionality
            e.preventDefault()
            toast.success('Sidebar width adjusted!', {
              icon: '↔️',
              duration: 2000,
            })
          }}
        />
      )}
    </>
  )
}

export default Sidebar
