import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useApp } from '../context/AppContext'
import { useLanguage } from '../context/LanguageContext'

// Components
import TopNavbar from './admin/TopNavbar'
import Sidebar from './admin/Sidebar'
import Dashboard from './admin/Dashboard'
import Companies from './admin/Companies'
import Documents from './admin/Documents'
import Settings from './admin/Settings'
import Profile from './admin/Profile'

const AdminPanel = ({ onLogout, onLockScreen }) => {
  const [currentPage, setCurrentPage] = useState('dashboard')
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)
  const { user, currentCompany } = useApp()
  const { isRTL } = useLanguage()

  const pages = {
    dashboard: (props) => <Dashboard {...props} onNavigate={handlePageChange} />,
    companies: Companies,
    documents: Documents,
    profile: Profile,
    settings: Settings
  }

  const CurrentPageComponent = pages[currentPage]

  const handlePageChange = (page) => {
    setCurrentPage(page)
  }

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 flex ${isRTL ? 'flex-row-reverse' : 'flex-row'}`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Sidebar */}
      <Sidebar
        currentPage={currentPage}
        onPageChange={handlePageChange}
        collapsed={sidebarCollapsed}
        onToggle={toggleSidebar}
        onLogout={onLogout}
      />

      {/* Main content area */}
      <div className={`flex-1 flex flex-col transition-all duration-500 ${
        sidebarCollapsed
          ? (isRTL ? 'mr-16' : 'ml-16')
          : (isRTL ? 'mr-64' : 'ml-64')
      }`}>
        {/* Top Navigation */}
        <TopNavbar
          user={user}
          currentCompany={currentCompany}
          onToggleSidebar={toggleSidebar}
          sidebarCollapsed={sidebarCollapsed}
          onLogout={onLogout}
          onNavigate={handlePageChange}
          onLockScreen={onLockScreen}
        />

        {/* Page content */}
        <main className="flex-1 p-6 overflow-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentPage}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="h-full"
            >
              <CurrentPageComponent />
            </motion.div>
          </AnimatePresence>
        </main>
      </div>

      {/* Mobile overlay */}
      {!sidebarCollapsed && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={toggleSidebar}
        />
      )}
    </div>
  )
}

export default AdminPanel
