import React, { useState, useEffect } from 'react'
import { Toaster } from 'react-hot-toast'
import { motion, AnimatePresence } from 'framer-motion'

// Components
import InstallationWizard from './components/InstallationWizard'
import LoginPage from './components/LoginPage'
import AdminPanel from './components/AdminPanel'
import LoadingScreen from './components/LoadingScreen'
import ForgotPassword from './components/ForgotPassword'
import ResetPassword from './components/ResetPassword'

// Context
import { ThemeProvider } from './context/ThemeContext'
import { AppProvider } from './context/AppContext'
import { LanguageProvider } from './context/LanguageContext'

function App() {
  const [currentView, setCurrentView] = useState('installation') // installation, login, admin, loading, forgot-password, reset-password
  const [isLoading, setIsLoading] = useState(false)
  const [resetToken, setResetToken] = useState(null)

  // Check if app is installed
  useEffect(() => {
    const isInstalled = localStorage.getItem('zatca_installed')
    const isLoggedIn = localStorage.getItem('zatca_logged_in')

    if (isInstalled === 'true') {
      if (isLoggedIn === 'true') {
        setCurrentView('admin')
      } else {
        setCurrentView('login')
      }
    } else {
      setCurrentView('installation')
    }
  }, [])

  const handleInstallationComplete = () => {
    setIsLoading(true)
    setCurrentView('loading')

    // Simulate installation process with proper state management
    setTimeout(() => {
      localStorage.setItem('zatca_installed', 'true')
      setIsLoading(false)
      // Ensure smooth transition to login page
      setTimeout(() => {
        setCurrentView('login')
      }, 100)
    }, 3000)
  }

  const handleLoginSuccess = () => {
    localStorage.setItem('zatca_logged_in', 'true')
    setCurrentView('admin')
  }

  const handleLogout = () => {
    localStorage.removeItem('zatca_logged_in')
    setCurrentView('login')
  }

  const handleForgotPassword = () => {
    setCurrentView('forgot-password')
  }

  const handleBackToLogin = () => {
    setCurrentView('login')
  }

  const handleResetLinkSent = (token) => {
    setResetToken(token)
    setCurrentView('reset-password')
  }

  const handleResetComplete = () => {
    setResetToken(null)
    setCurrentView('login')
  }

  const renderCurrentView = () => {
    switch (currentView) {
      case 'installation':
        return <InstallationWizard onComplete={handleInstallationComplete} />
      case 'loading':
        return <LoadingScreen />
      case 'login':
        return <LoginPage onLoginSuccess={handleLoginSuccess} onForgotPassword={handleForgotPassword} />
      case 'forgot-password':
        return <ForgotPassword onBackToLogin={handleBackToLogin} onResetLinkSent={handleResetLinkSent} />
      case 'reset-password':
        return <ResetPassword token={resetToken} onResetComplete={handleResetComplete} />
      case 'admin':
        return <AdminPanel onLogout={handleLogout} />
      default:
        return <InstallationWizard onComplete={handleInstallationComplete} />
    }
  }

  return (
    <LanguageProvider>
      <ThemeProvider>
        <AppProvider>
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentView}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="min-h-screen"
            >
              {renderCurrentView()}
            </motion.div>
          </AnimatePresence>

          {/* Toast Notifications */}
          <Toaster
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '12px',
                color: '#374151',
                fontSize: '14px',
                fontWeight: '500',
                padding: '12px 16px',
                boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
              },
              success: {
                iconTheme: {
                  primary: '#10B981',
                  secondary: '#FFFFFF',
                },
              },
              error: {
                iconTheme: {
                  primary: '#EF4444',
                  secondary: '#FFFFFF',
                },
              },
            }}
          />
        </div>
        </AppProvider>
      </ThemeProvider>
    </LanguageProvider>
  )
}

export default App
