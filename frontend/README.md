# ZATCA Admin Panel Frontend

A modern, interactive React-based admin panel for the ZATCA E-Invoicing Middleware system.

## Features

### 🎨 Design & UI/UX
- **Luxurious Interface**: Premium glassmorphism design with Facebook blue (#1877F2) theme
- **Dark/Light Mode**: Seamless theme switching with system preference detection
- **Responsive Design**: Mobile-first approach with adaptive layouts
- **Micro-interactions**: Smooth animations and hover effects throughout
- **Accessibility**: WCAG 2.1 compliant with keyboard navigation and screen reader support

### 🚀 Installation Wizard
- **License Agreement**: Interactive license acceptance with scroll tracking
- **Prerequisites Check**: System requirements verification with FAQ section
- **Application Setup**: User account creation with real-time validation
- **Loading Experience**: Animated progress with particle effects

### 🔐 Authentication
- **Modern Login**: Glassmorphism login card with animated placeholders
- **Demo Mode**: Accept any credentials for demonstration purposes
- **Theme Toggle**: Light/dark mode switcher in login interface

### 📊 Dashboard
- **Interactive Tiles**: 3D flip animations showing additional statistics
- **Live Charts**: Line, bar, and pie charts with Chart.js integration
- **Real-time Data**: Live refresh toggle with animated indicators
- **Activity Feed**: Recent system activities with status indicators

### 🏢 Company Management
- **Tree Structure**: Expandable company/branch hierarchy
- **CRUD Operations**: Create, edit, delete companies and branches
- **Search & Filter**: Real-time search with pagination
- **Bulk Actions**: Multi-select operations with confirmation dialogs

### 📄 Document Management
- **Advanced Filtering**: Multi-criteria filtering with collapsible panels
- **Status Tracking**: Color-coded status badges (Cleared, Reported, Error)
- **Bulk Operations**: Select multiple documents for batch actions
- **Export Options**: Download signed XML, PDF A3 invoices

### ⚙️ Settings
- **Tabbed Interface**: General and Appearance settings organization
- **Live Preview**: Real-time preview of theme changes
- **Color Picker**: Custom theme colors with predefined presets
- **Logo Upload**: Drag-and-drop logo upload for light/dark modes

## Technology Stack

- **React 18.x**: Modern React with hooks and functional components
- **Vite**: Fast build tool and development server
- **Tailwind CSS**: Utility-first CSS framework for styling
- **Framer Motion**: Animation library for smooth transitions
- **Chart.js**: Interactive charts and data visualization
- **React Hot Toast**: Elegant toast notifications
- **FontAwesome**: Comprehensive icon library

## Getting Started

### Prerequisites
- Node.js 18+ 
- Docker & Docker Compose
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd zatca-next
   ```

2. **Start with Docker**
   ```bash
   cd backend
   docker-compose up -d
   ```

3. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8080
   - phpMyAdmin: http://localhost:8082

### Development

1. **Install dependencies**
   ```bash
   cd frontend
   npm install
   ```

2. **Start development server**
   ```bash
   npm run dev
   ```

3. **Build for production**
   ```bash
   npm run build
   ```

## Project Structure

```
frontend/
├── public/                 # Static assets
├── src/
│   ├── components/        # React components
│   │   ├── admin/        # Admin panel components
│   │   └── installation/ # Installation wizard steps
│   ├── context/          # React context providers
│   ├── main.jsx          # Application entry point
│   ├── App.jsx           # Main app component
│   └── index.css         # Global styles
├── package.json          # Dependencies and scripts
├── vite.config.js        # Vite configuration
├── tailwind.config.js    # Tailwind CSS configuration
└── Dockerfile           # Docker configuration
```

## Key Components

### Installation Wizard
- `InstallationWizard.jsx` - Main wizard container
- `LicenseStep.jsx` - License agreement with scroll tracking
- `PrerequisitesStep.jsx` - System requirements with FAQ
- `SettingsStep.jsx` - Application configuration form

### Admin Panel
- `AdminPanel.jsx` - Main admin layout
- `TopNavbar.jsx` - Navigation bar with search and notifications
- `Sidebar.jsx` - Collapsible navigation sidebar
- `Dashboard.jsx` - Analytics dashboard with charts
- `Companies.jsx` - Company and branch management
- `Documents.jsx` - Document listing and management
- `Settings.jsx` - Application settings with live preview

### Context Providers
- `ThemeContext.jsx` - Theme and color management
- `AppContext.jsx` - Application state and user data

## Features in Detail

### Theme System
- Dynamic color switching with CSS custom properties
- Persistent theme preferences in localStorage
- System preference detection and auto-switching
- Live preview in settings with real-time updates

### Animation System
- Page transitions with Framer Motion
- Micro-interactions on buttons and cards
- Loading states with skeleton screens
- Smooth hover effects and state changes

### Data Management
- Mock data for demonstration purposes
- Local storage persistence for user preferences
- Context-based state management
- Real-time form validation

### Responsive Design
- Mobile-first CSS approach
- Adaptive layouts for all screen sizes
- Touch-friendly interface elements
- Optimized for both desktop and mobile usage

## Docker Configuration

The frontend runs in a Docker container with:
- Node.js 18 Alpine base image
- Hot reload for development
- Volume mounting for live code updates
- Environment variable configuration

## Environment Variables

- `REACT_APP_API_URL` - Backend API URL (default: http://localhost:8080)
- `CHOKIDAR_USEPOLLING` - Enable file watching in Docker

## Contributing

1. Follow the existing code style and patterns
2. Use TypeScript-style JSDoc comments for functions
3. Ensure responsive design for all new components
4. Test on both light and dark themes
5. Maintain accessibility standards

## License

This project is part of the ZATCA E-Invoicing Middleware system.
