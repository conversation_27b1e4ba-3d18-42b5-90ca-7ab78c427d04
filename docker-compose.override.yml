version: '3.8'

services:
  # Laravel API Backend (with MySQL database dependency)
  laravel-app:
    depends_on:
      - mariadb
    environment:
      - APP_ENV=local
      - APP_DEBUG=true
      - DB_CONNECTION=mysql
      - DB_HOST=mariadb
      - DB_PORT=3306
      - DB_DATABASE=zatca_einvoicing
      - DB_USERNAME=zatca_user
      - DB_PASSWORD=zatca_password
      - CACHE_DRIVER=array
      - ZATCA_SANDBOX_MODE=true
      - ZATCA_API_URL=https://gw-fatoora.zatca.gov.sa/e-invoicing/developer-portal

  # Angular Frontend (without <PERSON>vel dependency)
  angular-frontend:
    depends_on: []
