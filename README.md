# ZATCA E-Invoicing Middleware

A comprehensive full-stack solution for Saudi Arabian ZATCA (Zakat, Tax and Customs Authority) e-invoicing compliance. This application provides both a Laravel-based API backend and a modern React frontend admin panel for complete e-invoice management.

## 🏗️ Architecture

```
zatca-next/
├── backend/          # Laravel API middleware
│   ├── app/         # Laravel application code
│   ├── docker/      # Docker configuration
│   └── ...
├── frontend/        # React admin panel
│   ├── src/         # React source code
│   ├── public/      # Static assets
│   └── ...
└── docker-start.*   # Startup scripts
```

## ✨ Features

### 🎨 Frontend (React Admin Panel)
- **Installation Wizard**: Guided setup with license agreement and configuration
- **Modern Dashboard**: Interactive charts, real-time statistics, and activity feeds
- **Company Management**: Tree-structured company and branch management
- **Document Management**: Advanced filtering, bulk operations, and export features
- **Settings Panel**: Theme customization, logo upload, and application configuration
- **Responsive Design**: Mobile-first approach with dark/light mode support
- **Premium UI/UX**: Glassmorphism design with smooth animations and micro-interactions

### 🔧 Backend (Laravel API)
- **ZATCA API Integration**: Complete integration with ZATCA e-invoicing APIs
- **User Management**: Multi-user support with role-based access control
- **Company Management**: Support for multiple companies and branches
- **License Validation**: Built-in license validation system
- **API Documentation**: Comprehensive Postman collection included

## 🚀 Quick Start

### Prerequisites
- Docker Desktop
- Git
- 4GB+ RAM
- 10GB+ free disk space

### 🐳 Docker Setup (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd zatca-next
   ```

2. **Start the application**

   **Windows:**
   ```cmd
   docker-start.bat
   ```

   **Linux/macOS:**
   ```bash
   chmod +x docker-start.sh
   ./docker-start.sh
   ```

3. **Access the applications**
   - 🎨 **Frontend**: http://localhost:3000
   - 🔧 **Backend API**: http://localhost:8080
   - 🗄️ **phpMyAdmin**: http://localhost:8082

### 📋 Default Credentials

**Database (phpMyAdmin):**
- Username: `zatca_user`
- Password: `zatca_password`
- Database: `zatca_einvoicing`

**Admin Panel (Demo Mode):**
- Any email and password combination works in demo mode

## 🛠️ Development Setup

### Backend Development
```bash
cd backend
docker-compose up -d
docker-compose exec laravel-app bash
composer install
php artisan migrate
```

### Frontend Development
```bash
cd frontend
npm install
npm run dev
```

## 📱 Application Flow

### 1. Installation Wizard
- **License Agreement**: Interactive license acceptance
- **Prerequisites Check**: System requirements verification
- **Application Setup**: Admin account creation

### 2. Admin Dashboard
- **Overview**: Statistics tiles with flip animations
- **Charts**: Interactive data visualization
- **Recent Activity**: Real-time system updates

### 3. Company Management
- **Tree Structure**: Expandable company hierarchy
- **Branch Management**: Detailed branch configuration
- **CRUD Operations**: Full create, read, update, delete

### 4. Document Management
- **Advanced Filtering**: Multi-criteria search
- **Status Tracking**: Real-time document status
- **Bulk Operations**: Multi-select actions
- **Export Features**: PDF and XML downloads

## 🎨 UI/UX Features

### Design System
- **Theme**: Facebook Blue (#1877F2) with customizable colors
- **Typography**: Inter font family for modern readability
- **Animations**: Framer Motion for smooth transitions
- **Icons**: FontAwesome for consistent iconography

### Responsive Design
- **Mobile-First**: Optimized for all screen sizes
- **Touch-Friendly**: Large tap targets and gestures
- **Accessibility**: WCAG 2.1 compliant with keyboard navigation

### Dark/Light Mode
- **Auto-Detection**: System preference detection
- **Manual Toggle**: User-controlled theme switching
- **Persistent**: Theme preference saved locally

## 🔧 Technology Stack

### Frontend
- **React 18**: Modern React with hooks
- **Vite**: Fast build tool and dev server
- **Tailwind CSS**: Utility-first styling
- **Framer Motion**: Animation library
- **Chart.js**: Data visualization
- **React Hot Toast**: Elegant notifications

### Backend
- **Laravel 10**: PHP framework
- **MariaDB 11.7**: Database
- **Apache 2.4**: Web server
- **Docker**: Containerization

### DevOps
- **Docker Compose**: Multi-container orchestration
- **Volume Mounting**: Live code reloading
- **Environment Variables**: Configuration management

## 📊 API Endpoints

The backend provides RESTful APIs for:
- User authentication and management
- Company and branch operations
- Document processing and validation
- ZATCA integration services

See the included Postman collection for detailed API documentation.

## 🔒 Security Features

- **License Validation**: Custom license verification
- **Role-Based Access**: Multi-level user permissions
- **Input Validation**: Comprehensive data sanitization
- **CSRF Protection**: Laravel security features
- **Environment Isolation**: Docker containerization

## 📈 Performance

- **Lazy Loading**: Component-based code splitting
- **Optimized Images**: Compressed assets
- **Caching**: Browser and server-side caching
- **CDN Ready**: Static asset optimization

## 🧪 Testing

```bash
# Backend tests
cd backend
docker-compose exec laravel-app php artisan test

# Frontend tests (when implemented)
cd frontend
npm test
```

## 📝 Documentation

- **Frontend**: See `frontend/README.md` for detailed React documentation
- **Backend**: Laravel documentation and inline comments
- **API**: Postman collection with examples
- **Docker**: Docker Compose configuration comments

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Follow existing code style
4. Test your changes
5. Submit a pull request

## 📄 License

This project is proprietary software for ZATCA e-invoicing compliance.

## 🆘 Support

For technical support:
1. Check the documentation
2. Review Docker logs: `docker-compose logs -f`
3. Verify system requirements
4. Contact the development team

## 🗺️ Roadmap

- [ ] Advanced reporting features
- [ ] Multi-language support
- [ ] Enhanced validation engine
- [ ] Mobile app companion
- [ ] Advanced analytics dashboard

---

**Happy coding! 🎯**
