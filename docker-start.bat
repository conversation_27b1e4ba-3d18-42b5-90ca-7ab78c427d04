@echo off
echo 🚀 Starting ZATCA Admin Panel...
echo ==================================

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker Desktop first.
    pause
    exit /b 1
)

REM Navigate to backend directory
cd backend

echo 📦 Building and starting containers...

REM Stop any existing containers
docker-compose down

REM Build and start containers
docker-compose up -d --build

echo ⏳ Waiting for services to start...
timeout /t 10 /nobreak >nul

echo 🔍 Checking service status...

REM Check services (simplified for Windows)
echo ✅ Services are starting up...

echo.
echo 🎉 ZATCA Admin Panel is starting up!
echo ==================================
echo 📱 Frontend (React):     http://localhost:3000
echo 🔧 Backend (Laravel):    http://localhost:8080
echo 🗄️  Database (phpMyAdmin): http://localhost:8082
echo.
echo 📋 Database Credentials:
echo    Username: zatca_user
echo    Password: zatca_password
echo    Database: zatca_einvoicing
echo.
echo ⏱️  Services may take a few minutes to fully start.
echo 📖 Check logs with: docker-compose logs -f
echo 🛑 Stop services with: docker-compose down
echo.
echo Happy coding! 🎯
echo.
pause
