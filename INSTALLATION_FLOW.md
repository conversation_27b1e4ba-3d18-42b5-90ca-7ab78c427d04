# ZatcaNext Installation Flow Documentation

## **Complete Installation Process**

This document outlines the step-by-step installation process for the ZatcaNext ZATCA e-Invoicing middleware.

## **🚀 Initial Setup (Docker)**

### **1. Start Docker Environment**
```bash
# Start Docker Desktop first, then:
docker-compose up -d
```

### **2. Run Database Migrations**
```bash
# Migrations will create the settings table and installation tracking
docker exec zatca-laravel-api php artisan migrate --force
```

## **📋 Installation Wizard Flow**

### **Step 1: Check Installation Status**
**Endpoint:** `GET /api/installation/status`

**Response (Not Installed):**
```json
{
    "installation_completed": false,
    "message": "Installation setup required."
}
```

**Response (Already Installed):**
```json
{
    "installation_completed": true,
    "message": "Installation completed. You may login now."
}
```

### **Step 2: Create User (Admin)**
**Endpoint:** `POST /api/installation/create-user`

**Request:**
```json
{
    "name": "Super Admin",
    "email": "<EMAIL>",
    "password": "SecurePassword123!",
    "password_confirmation": "SecurePassword123!"
}
```

**Response:**
```json
{
    "message": "Success! User created successfully. Please create a company now.",
    "user": {
        "id": 1,
        "name": "Super Admin",
        "email": "<EMAIL>"
    },
    "next_step": "Create a company using POST /api/company with JWT authentication"
}
```

### **Step 3: Login to Get JWT Token**
**Endpoint:** `POST /api/token`

**Request:**
```json
{
    "email": "<EMAIL>",
    "password": "SecurePassword123!"
}
```

**Response:**
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 3600
}
```

### **Step 4: Create Company**
**Endpoint:** `POST /api/company`
**Headers:** `Authorization: Bearer {access_token}`

**Request:**
```json
{
    "name": "ZatcaNext Solutions"
}
```

**Response:**
```json
{
    "id": 1,
    "name": "ZatcaNext Solutions",
    "slug": "zatcanext-solutions",
    "api_key": "zatca_api_key_generated",
    "message": "Success! Company created successfully. Please create a business location now.",
    "next_step": "Create a business location using POST /api/business-location"
}
```

### **Step 5: Create Business Location**
**Endpoint:** `POST /api/locations`
**Headers:** `Authorization: Api-Key {company_api_key}`

**Request:**
```json
{
    "seller_name": "ZatcaNext Solutions Store",
    "tax_no": "399999999900003",
    "organisation": "ZatcaNext Solutions",
    "serial_number": "1-zatcanext.com|2-version 1.0|3-ed22f1d8-e6a2-1118-9b58-d9a8f11e445f",
    "organisation_unit": "Main Store",
    "registered_address": "Riyadh Business District, King Fahd Road, Riyadh",
    "business_category": "Technology Services",
    "title": "1100",
    "common_name": "ZatcaNext Main Store"
}
```

**Response:**
```json
{
    "id": 1,
    "authentication_token": "location_auth_token",
    "seller_name": "ZatcaNext Solutions Store",
    "tax_no": "399999999900003",
    "message": "Success! Business location created successfully. Please complete the installation now.",
    "next_step": "Complete installation using POST /api/installation/complete"
}
```

### **Step 6: Complete Installation**
**Endpoint:** `POST /api/installation/complete`

**Response:**
```json
{
    "message": "Success! Installation completed successfully. You can now use all application features.",
    "installation_completed": true,
    "next_step": "Login using POST /api/token"
}
```

## **🔒 Installation Middleware Protection**

After installation is completed, all protected routes require the installation to be finished:

### **Protected Routes (Require Installation):**
- All ZATCA API endpoints (`/api/{environment}/compliance`, `/api/{environment}/clearance`, `/api/{environment}/reporting`)
- Company management endpoints (except creation during installation)
- Advanced business location operations

### **Unprotected Routes (Available During Installation):**
- Installation wizard endpoints (`/api/installation/*`)
- Authentication endpoints (`/api/token`)
- Basic company creation (`POST /api/company`)
- Basic location creation (`POST /api/locations`)

## **📊 Installation Status Tracking**

The system tracks installation completion through:

1. **Settings Table:** Stores `installation_completed` boolean flag
2. **User Count:** Checks if at least one user exists
3. **Company Count:** Checks if at least one company exists  
4. **Location Count:** Checks if at least one business location exists

## **🔄 Error Handling**

### **Installation Already Completed:**
```json
{
    "message": "Installation already completed. You may login now.",
    "error": "INSTALLATION_ALREADY_COMPLETED"
}
```

### **Missing Installation Requirements:**
```json
{
    "message": "Application installation is not completed. Please complete the installation wizard first.",
    "error": "INSTALLATION_REQUIRED",
    "installation_status_url": "/api/installation/status",
    "installation_setup_url": "/api/installation/setup"
}
```

### **Step Requirements Not Met:**
```json
{
    "message": "Company not created yet. Please create a company.",
    "error": "COMPANY_NOT_CREATED",
    "next_step": "Create company using POST /api/company"
}
```

## **📝 Postman Collection Usage**

The updated Postman collection includes:

1. **🔧 Installation Wizard** folder with all installation steps
2. **Automated variable storage** for tokens and IDs
3. **Step-by-step guidance** with console logging
4. **Error handling** and next step instructions

### **Collection Variables Set Automatically:**
- `user_id` - Created user ID
- `access_token` - JWT authentication token
- `company_id` - Created company ID
- `company_api_key` - Company API key
- `location_id` - Created location ID
- `location_auth_token` - Location authentication token

## **🎯 Next Steps After Installation**

Once installation is complete, you can:

1. **Use ZATCA APIs** for compliance, clearance, and reporting
2. **Manage multiple companies** and business locations
3. **Access all protected endpoints** with proper authentication
4. **Develop the Angular admin panel** for frontend management

## **🔧 Development Workflow**

1. **First Time Setup:** Follow installation wizard steps 1-6
2. **Development:** Use JWT tokens for API access
3. **Testing:** Use Postman collection for API testing
4. **Frontend Integration:** Connect Angular admin panel to APIs
